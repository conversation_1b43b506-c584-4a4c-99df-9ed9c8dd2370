import 'dart:async';

import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';

import 'package:flutter/material.dart';

// Compact version of progress widget that will be fixed to the bottom bar
class FixedProgressWidget extends StatefulWidget {
  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Color? backgroundColor;
  final Color? textColor;

  const FixedProgressWidget({
    super.key,
    required this.epubPlayerKey,
    this.backgroundColor,
    this.textColor,
  });

  @override
  State<FixedProgressWidget> createState() => _FixedProgressWidgetState();
}

class _FixedProgressWidgetState extends State<FixedProgressWidget> {
  double _readProgress = 0.0;
  Timer? _sliderTimer;

  @override
  void initState() {
    super.initState();
    _readProgress = widget.epubPlayerKey.currentState!.percentage;
  }

  @override
  void didUpdateWidget(FixedProgressWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update progress whenever the widget rebuilds with new data
    final currentProgress = widget.epubPlayerKey.currentState!.percentage;
    if (_readProgress != currentProgress) {
      setState(() {
        _readProgress = currentProgress;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final percentage = widget.epubPlayerKey.currentState!.percentage;

    // Use provided colors or fallback to theme colors
    final bgColor = widget.backgroundColor ?? colorScheme.surface;
    final txtColor = widget.textColor ?? colorScheme.onSurface;
    final primaryColor = colorScheme.primary;

    // Ensure slider position matches current reading position
    if (_readProgress != percentage) {
      _readProgress = percentage;
    }

    return Padding(
      // Use consistent horizontal padding, remove bottom padding
      padding: EdgeInsets.only(
          left: DesignSystem.spaceM,
          right: DesignSystem.spaceM,
          top: DesignSystem.spaceXS,
          bottom: 0.0), // 16.0, 16.0, 4.0, 0.0 (preserves exact spacing)
      child: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Custom Progress bar with thumb and arrows
            Row(
              children: [
                // Left arrow control
                SemanticHelpers.button(
                  context: context,
                  label: 'Previous page',
                  hint: 'Navigate to the previous page in the book',
                  onTap: () {
                    widget.epubPlayerKey.currentState!.prevPage();
                  },
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: Icon(Icons.chevron_left, color: txtColor),
                    onPressed: () {
                      widget.epubPlayerKey.currentState!.prevPage();
                    },
                  ),
                ),
                // Progress slider
                Expanded(
                  child: SliderTheme(
                    data: SliderThemeData(
                      trackHeight: 40.0,
                      thumbShape: const RoundSliderThumbShape(
                        enabledThumbRadius: 20.0,
                        elevation: 2.0,
                      ),
                      overlayShape:
                          const RoundSliderOverlayShape(overlayRadius: 28.0),
                      trackShape: const RoundedRectSliderTrackShape(),
                      activeTrackColor: txtColor.withValues(alpha: 0.25),
                      inactiveTrackColor: txtColor.withAlpha(25),
                      thumbColor: Theme.of(context).colorScheme.onPrimary,
                      overlayColor: txtColor.withAlpha(50),
                    ),
                    child: SemanticHelpers.slider(
                      label: 'Reading progress',
                      value: _readProgress,
                      min: 0.0,
                      max: 1.0,
                      unit: '%',
                      child: Slider(
                        value: _readProgress,
                        onChanged: (value) {
                          setState(() {
                            _readProgress = value;
                          });
                          _sliderTimer?.cancel();
                          _sliderTimer = Timer(
                            const Duration(milliseconds: 100),
                            () async {
                              await widget.epubPlayerKey.currentState!
                                  .goToPercentage(value);
                              Timer(const Duration(milliseconds: 300), () {
                                setState(() {});
                              });
                            },
                          );
                        },
                      ),
                    ),
                  ),
                ),
                // Right arrow control
                SemanticHelpers.button(
                  context: context,
                  label: 'Next page',
                  hint: 'Navigate to the next page in the book',
                  onTap: () {
                    widget.epubPlayerKey.currentState!.nextPage();
                  },
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: Icon(Icons.chevron_right, color: txtColor),
                    onPressed: () {
                      widget.epubPlayerKey.currentState!.nextPage();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Simple estimate of remaining reading time based on percentage
  // In a real implementation, this would be more sophisticated
  String _estimateRemainingTime(double percentage) {
    // Assuming average book takes 300 minutes to read
    int estimatedMinutes = ((1 - percentage) * 300).round();
    if (estimatedMinutes >= 60) {
      return "${(estimatedMinutes / 60).floor()}h ${estimatedMinutes % 60}m";
    }
    return "${estimatedMinutes}m";
  }
}

class ProgressWidget extends StatefulWidget {
  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Function(bool) showOrHideAppBarAndBottomBar;
  final Color? backgroundColor;
  final Color? textColor;

  const ProgressWidget({
    super.key,
    required this.epubPlayerKey,
    required this.showOrHideAppBarAndBottomBar,
    this.backgroundColor,
    this.textColor,
  });

  @override
  State<ProgressWidget> createState() => _ProgressWidgetState();
}

class _ProgressWidgetState extends State<ProgressWidget> {
  Timer? _sliderTimer;
  double _readProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _readProgress = widget.epubPlayerKey.currentState!.percentage;
  }

  @override
  void didUpdateWidget(ProgressWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update progress whenever the widget rebuilds with new data
    final currentProgress = widget.epubPlayerKey.currentState!.percentage;
    if (_readProgress != currentProgress) {
      setState(() {
        _readProgress = currentProgress;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get the theme colors or use provided colors
    final bgColor =
        widget.backgroundColor ?? Theme.of(context).colorScheme.surface;
    final txtColor =
        widget.textColor ?? Theme.of(context).colorScheme.onSurface;
    final primaryColor = Theme.of(context).colorScheme.primary;

    // Ensure slider position matches current reading position
    final currentProgress = widget.epubPlayerKey.currentState!.percentage;
    if (_readProgress != currentProgress) {
      _readProgress = currentProgress;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          // Add 24px top padding for consistent spacing
          const SizedBox(height: 24.0),
          Text(
            widget.epubPlayerKey.currentState!.chapterTitle,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 20,
              fontFamily: 'SourceHanSerif',
              fontWeight: FontWeight.bold,
              color: txtColor,
            ),
          ),
          Divider(color: txtColor.withAlpha(51)), // ~20% opacity
          // Custom Progress bar with thumb and arrows - same style as FixedProgressWidget
          Row(
            children: [
              // Left arrow control
              IconButton(
                icon: Icon(Icons.chevron_left, color: txtColor),
                onPressed: () {
                  widget.epubPlayerKey.currentState!.prevChapter();
                  widget.showOrHideAppBarAndBottomBar(false);
                },
              ),
              // Progress slider
              Expanded(
                child: SliderTheme(
                  data: SliderThemeData(
                    trackHeight: 40.0,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 20.0,
                      elevation: 2.0,
                    ),
                    overlayShape:
                        const RoundSliderOverlayShape(overlayRadius: 28.0),
                    trackShape: const RoundedRectSliderTrackShape(),
                    activeTrackColor: txtColor.withValues(alpha: 0.25),
                    inactiveTrackColor: txtColor.withAlpha(25),
                    thumbColor: Theme.of(context).colorScheme.onPrimary,
                    overlayColor: txtColor.withAlpha(50),
                  ),
                  child: Slider(
                    value: _readProgress,
                    onChanged: (value) {
                      setState(() {
                        _readProgress = value;
                      });
                      _sliderTimer?.cancel();
                      _sliderTimer = Timer(
                        const Duration(milliseconds: 100),
                        () async {
                          await widget.epubPlayerKey.currentState!
                              .goToPercentage(value);
                          Timer(const Duration(milliseconds: 300), () {
                            setState(() {});
                          });
                        },
                      );
                    },
                  ),
                ),
              ),
              // Right arrow control
              IconButton(
                icon: Icon(Icons.chevron_right, color: txtColor),
                onPressed: () {
                  widget.epubPlayerKey.currentState!.nextChapter();
                  widget.showOrHideAppBarAndBottomBar(false);
                },
              ),
            ],
          ),
          Row(
            children: [
              ProgressDisplay(
                mainText: widget.epubPlayerKey.currentState!.chapterCurrentPage
                    .toString(),
                subText: L10n.of(context).reading_page_current_page,
                textColor: txtColor,
              ),
              ProgressDisplay(
                mainText: widget.epubPlayerKey.currentState!.chapterTotalPages
                    .toString(),
                subText: L10n.of(context).reading_page_chapter_pages,
                textColor: txtColor,
              ),
              ProgressDisplay(
                mainText: (widget.epubPlayerKey.currentState!.percentage * 100)
                    .toStringAsFixed(2),
                subText: '%',
                textColor: txtColor,
              ),
            ],
          ),
          // Add 24px bottom padding for consistent spacing
          const SizedBox(height: 24.0),
        ],
      ),
    );
  }
}

class ProgressDisplay extends StatelessWidget {
  const ProgressDisplay({
    super.key,
    required this.mainText,
    required this.subText,
    this.textColor,
  });

  final String mainText;
  final String subText;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          Text(
            mainText,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 20,
              fontFamily: 'SourceHanSerif',
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          Text(
            subText,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 10,
              fontFamily: 'SourceHanSerif',
              fontWeight: FontWeight.w300,
              color: textColor,
            ),
          )
        ],
      ),
    );
  }
}
