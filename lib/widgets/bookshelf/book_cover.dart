import 'dart:io';

import 'package:dasso_reader/models/book.dart';
import 'package:flutter/material.dart';

Widget bookCover(
  BuildContext context,
  Book book, {
  double? height,
  double? width,
  double? radius,
}) {
  radius ??= 8;
  File file = File(book.coverFullPath);
  Widget child = file.existsSync()
      ? Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              image: FileImage(file),
              fit: BoxFit.cover,
            ),
          ),
        )
      : _BookCoverPlaceholder(book: book);

  return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(7),
          border: Border.all(
            width: 0.3,
            color: Theme.of(context).colorScheme.outline,
          )),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: child,
      ));
}

/// Optimized book cover placeholder that only rebuilds when book changes
///
/// This widget is extracted to minimize rebuilds and uses const constructor
/// for better performance.
class _BookCoverPlaceholder extends StatelessWidget {
  const _BookCoverPlaceholder({
    required this.book,
  });

  final Book book;

  @override
  Widget build(BuildContext context) {
    // Use theme-aware colors for book cover placeholder
    final colorScheme = Theme.of(context).colorScheme;
    final backgroundColor = colorScheme.surfaceContainerHighest;

    return Container(
      color: backgroundColor,
      child: Center(
        child: Icon(
          Icons.book,
          size: 40,
          color: colorScheme.onSurface,
        ),
      ),
    );
  }
}
