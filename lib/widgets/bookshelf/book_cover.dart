import 'dart:io';

import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:flutter/material.dart';

Widget bookCover(
  BuildContext context,
  Book book, {
  double? height,
  double? width,
  double? radius,
}) {
  radius ??= DesignSystem.radiusS;
  File file = File(book.coverFullPath);
  Widget child = file.existsSync()
      ? Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              image: FileImage(file),
              fit: BoxFit.cover,
            ),
          ),
        )
      : _BookCoverPlaceholder(book: book);

  return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(DesignSystem.radiusS - 1),
          border: Border.all(
            width: 0.5, // Thin border for book cover outline
            color: Theme.of(context).colorScheme.outline,
          )),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: child,
      ));
}

/// Optimized book cover placeholder that only rebuilds when book changes
///
/// This widget is extracted to minimize rebuilds and uses const constructor
/// for better performance.
class _BookCoverPlaceholder extends StatelessWidget {
  const _BookCoverPlaceholder({
    required this.book,
  });

  final Book book;

  @override
  Widget build(BuildContext context) {
    // Use theme-aware colors for book cover placeholder
    final colorScheme = Theme.of(context).colorScheme;
    final backgroundColor = colorScheme.surfaceContainerHighest;

    return Container(
      color: backgroundColor,
      child: Center(
        child: Icon(
          Icons.book,
          size: DesignSystem.widgetIconSizeLarge,
          color: colorScheme.onSurface,
        ),
      ),
    );
  }
}
