/*! For license information please see pdf-legacy.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.FoliateJS=t():e.FoliateJS=t()}(window,(function(){return function(){var __webpack_modules__={179:function(){},296:function(e,t,a){"use strict";a.d(t,{A:function(){return i}});var r=a(7800);function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var r,i,n,s,l=[],o=!0,_=!1;try{if(n=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;o=!1}else for(;!(o=(r=n.call(a)).done)&&(l.push(r.value),l.length!==t);o=!0);}catch(e){_=!0,i=e}finally{try{if(!o&&null!=a.return&&(s=a.return(),Object(s)!==s))return}finally{if(_)throw i}}return l}}(e,t)||(0,r.A)(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},467:function(e,t,a){"use strict";function r(e,t,a,r,i,n,s){try{var l=e[n](s),o=l.value}catch(e){return void a(e)}l.done?t(o):Promise.resolve(o).then(r,i)}function i(e){return function(){var t=this,a=arguments;return new Promise((function(i,n){var s=e.apply(t,a);function l(e){r(s,i,n,l,o,"next",e)}function o(e){r(s,i,n,l,o,"throw",e)}l(void 0)}))}}a.d(t,{A:function(){return i}})},579:function(e,t,a){var r=a(3738).default;e.exports=function(e){if(null!=e){var t=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],a=0;if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&a>=e.length&&(e=void 0),{value:e&&e[a++],done:!e}}}}throw new TypeError(r(e)+" is not iterable")},e.exports.__esModule=!0,e.exports.default=e.exports},816:function(e,t,a){"use strict";a.d(t,{A:function(){return i}});var r=a(2284);function i(e){var t=function(e){if("object"!=(0,r.A)(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,"string");if("object"!=(0,r.A)(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==(0,r.A)(t)?t:t+""}},887:function(e,t,a){var r=a(6993),i=a(1791);e.exports=function(e,t,a,n,s){return new i(r().w(e,t,a,n),s||Promise)},e.exports.__esModule=!0,e.exports.default=e.exports},950:function(){},991:function(e,t,a){"use strict";a.d(t,{A:function(){return i}});var r=a(3954);function i(){return i="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,a){var i=function(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=(0,r.A)(e)););return e}(e,t);if(i){var n=Object.getOwnPropertyDescriptor(i,t);return n.get?n.get.call(arguments.length<3?e:a):n.value}},i.apply(null,arguments)}},1750:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.r(__webpack_exports__);var _babel_runtime_helpers_get__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(991),_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(4467),_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__=__webpack_require__(467),_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__(5458),_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__(296),_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__(2901),_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(3029),_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(6822),_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(3954),_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__(5501),_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(2284),_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(4756),_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__),root,factory;function _superPropGet(e,t,a,r){var i=(0,_babel_runtime_helpers_get__WEBPACK_IMPORTED_MODULE_0__.A)((0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_1__.A)(1&r?e.prototype:e),t,a);return 2&r&&"function"==typeof i?function(e){return i.apply(a,e)}:i}function ownKeys(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(a),!0).forEach((function(t){(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function _classPrivateGetter(e,t,a){return a(_assertClassBrand(e,t))}function _classPrivateMethodInitSpec(e,t){_checkPrivateRedeclaration(e,t),t.add(e)}function _classPrivateFieldInitSpec(e,t,a){_checkPrivateRedeclaration(e,t),t.set(e,a)}function _checkPrivateRedeclaration(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateFieldGet(e,t){return e.get(_assertClassBrand(e,t))}function _classPrivateFieldSet(e,t,a){return e.set(_assertClassBrand(e,t),a),a}function _assertClassBrand(e,t,a){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:a;throw new TypeError("Private element is not present on this object")}function _createForOfIteratorHelper(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!a){if(Array.isArray(e)||(a=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,s=!0,l=!1;return{s:function(){a=a.call(e)},n:function(){var e=a.next();return s=e.done,e},e:function(e){l=!0,n=e},f:function(){try{s||null==a.return||a.return()}finally{if(l)throw n}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=Array(t);a<t;a++)r[a]=e[a];return r}function _callSuper(e,t,a){return t=(0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_1__.A)(t),(0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_4__.A)(e,_isNativeReflectConstruct()?Reflect.construct(t,a||[],(0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_1__.A)(e).constructor):t.apply(e,a))}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}module=__webpack_require__.hmd(module),root=globalThis,factory=function(){return function(){var __webpack_modules__=[,function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.VerbosityLevel=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.RenderingIntentFlag=t.PromiseCapability=t.PermissionFlag=t.PasswordResponses=t.PasswordException=t.PageActionEventType=t.OPS=t.MissingPDFException=t.MAX_IMAGE_SIZE_TO_CACHE=t.LINE_FACTOR=t.LINE_DESCENT_FACTOR=t.InvalidPDFException=t.ImageKind=t.IDENTITY_MATRIX=t.FormatError=t.FeatureTest=t.FONT_IDENTITY_MATRIX=t.DocumentActionEventType=t.CMapCompressionType=t.BaseException=t.BASELINE_FACTOR=t.AnnotationType=t.AnnotationReplyType=t.AnnotationPrefix=t.AnnotationMode=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationEditorType=t.AnnotationEditorPrefix=t.AnnotationEditorParamsType=t.AnnotationBorderStyleType=t.AnnotationActionEventType=t.AbortException=void 0,t.assert=function(e,t){e||s(t)},t.bytesToString=f,t.createValidAbsoluteUrl=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!e)return null;try{if(a&&"string"==typeof e){if(a.addDefaultProtocol&&e.startsWith("www.")){var r=e.match(/\./g);(null==r?void 0:r.length)>=2&&(e="http://".concat(e))}if(a.tryConvertEncoding)try{e=A(e)}catch(e){}}var i=t?new URL(e,t):new URL(e);if(function(e){switch(null==e?void 0:e.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(i))return i}catch(e){}return null},t.getModificationDate=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date;return[e.getUTCFullYear().toString(),(e.getUTCMonth()+1).toString().padStart(2,"0"),e.getUTCDate().toString().padStart(2,"0"),e.getUTCHours().toString().padStart(2,"0"),e.getUTCMinutes().toString().padStart(2,"0"),e.getUTCSeconds().toString().padStart(2,"0")].join("")},t.getUuid=function(){var e,t;if("undefined"!=typeof crypto&&"function"==typeof(null===(e=crypto)||void 0===e?void 0:e.randomUUID))return crypto.randomUUID();var a=new Uint8Array(32);if("undefined"!=typeof crypto&&"function"==typeof(null===(t=crypto)||void 0===t?void 0:t.getRandomValues))crypto.getRandomValues(a);else for(var r=0;r<32;r++)a[r]=Math.floor(255*Math.random());return f(a)},t.getVerbosityLevel=function(){return i},t.info=function(e){i>=r.INFOS&&console.log("Info: ".concat(e))},t.isArrayBuffer=function(e){return"object"===(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(e)&&void 0!==(null==e?void 0:e.byteLength)},t.isArrayEqual=function(e,t){if(e.length!==t.length)return!1;for(var a=0,r=e.length;a<r;a++)if(e[a]!==t[a])return!1;return!0},t.isNodeJS=void 0,t.normalizeUnicode=function(e){return M||(M=/([\xA0\xB5\u037E\u0EB3\u2000-\u200A\u202F\u2126\uFB00-\uFB04\uFB06\uFB20-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBA1\uFBA4-\uFBA9\uFBAE-\uFBB1\uFBD3-\uFBDC\uFBDE-\uFBE7\uFBEA-\uFBF8\uFBFC\uFBFD\uFC00-\uFC5D\uFC64-\uFCF1\uFCF5-\uFD3D\uFD88\uFDF4\uFDFA\uFDFB\uFE71\uFE77\uFE79\uFE7B\uFE7D]+)|(\uFB05+)/g,k=new Map([["ﬅ","ſt"]])),e.replaceAll(M,(function(e,t,a){return t?t.normalize("NFKC"):k.get(a)}))},t.objectFromMap=function(e){var t,a=Object.create(null),r=_createForOfIteratorHelper(e);try{for(r.s();!(t=r.n()).done;){var i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(t.value,2),n=i[0],s=i[1];a[n]=s}}catch(e){r.e(e)}finally{r.f()}return a},t.objectSize=function(e){return Object.keys(e).length},t.setVerbosityLevel=function(e){Number.isInteger(e)&&(i=e)},t.shadow=l,t.string32=function(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e)},t.stringToBytes=m,t.stringToPDFString=function(e){var t;if(e[0]>="ï"&&("þ"===e[0]&&"ÿ"===e[1]?t="utf-16be":"ÿ"===e[0]&&"þ"===e[1]?t="utf-16le":"ï"===e[0]&&"»"===e[1]&&"¿"===e[2]&&(t="utf-8"),t))try{var a=new TextDecoder(t,{fatal:!0}),r=m(e);return a.decode(r)}catch(e){n('stringToPDFString: "'.concat(e,'".'))}for(var i=[],s=0,l=e.length;s<l;s++){var o=y[e.charCodeAt(s)];i.push(o?String.fromCharCode(o):e.charAt(s))}return i.join("")},t.stringToUTF8String=A,t.unreachable=s,t.utf8StringToString=function(e){return unescape(encodeURIComponent(e))},t.warn=n;var a=!("object"!==("undefined"==typeof process?"undefined":(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(process))||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type);t.isNodeJS=a,t.IDENTITY_MATRIX=[1,0,0,1,0,0],t.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0],t.MAX_IMAGE_SIZE_TO_CACHE=1e7,t.LINE_FACTOR=1.35,t.LINE_DESCENT_FACTOR=.35;t.BASELINE_FACTOR=.35/1.35,t.RenderingIntentFlag={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256},t.AnnotationMode={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},t.AnnotationEditorPrefix="pdfjs_internal_editor_",t.AnnotationEditorType={DISABLE:-1,NONE:0,FREETEXT:3,STAMP:13,INK:15},t.AnnotationEditorParamsType={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23},t.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},t.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},t.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},t.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},t.AnnotationReplyType={GROUP:"Group",REPLY:"R"},t.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512},t.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864},t.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},t.AnnotationActionEventType={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"},t.DocumentActionEventType={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"},t.PageActionEventType={O:"PageOpen",C:"PageClose"};var r={ERRORS:0,WARNINGS:1,INFOS:5};t.VerbosityLevel=r,t.CMapCompressionType={NONE:0,BINARY:1},t.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91},t.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};var i=r.WARNINGS;function n(e){i>=r.WARNINGS&&console.log("Warning: ".concat(e))}function s(e){throw new Error(e)}function l(e,t,a){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return Object.defineProperty(e,t,{value:a,enumerable:!r,configurable:!0,writable:!1}),a}var o=function(){function e(t,a){this.constructor===e&&s("Cannot initialize BaseException."),this.message=t,this.name=a}return e.prototype=new Error,e.constructor=e,e}();t.BaseException=o;var _=function(e){function t(e,a){var r;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(r=_callSuper(this,t,[e,"PasswordException"])).code=a,r}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(o);t.PasswordException=_;var c=function(e){function t(e,a){var r;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(r=_callSuper(this,t,[e,"UnknownErrorException"])).details=a,r}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(o);t.UnknownErrorException=c;var u=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,"InvalidPDFException"])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(o);t.InvalidPDFException=u;var h=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,"MissingPDFException"])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(o);t.MissingPDFException=h;var d=function(e){function t(e,a){var r;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(r=_callSuper(this,t,[e,"UnexpectedResponseException"])).status=a,r}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(o);t.UnexpectedResponseException=d;var p=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,"FormatError"])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(o);t.FormatError=p;var v=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,"AbortException"])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(o);function f(e){"object"===(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(e)&&void 0!==(null==e?void 0:e.length)||s("Invalid argument for bytesToString");var t=e.length,a=8192;if(t<a)return String.fromCharCode.apply(null,e);for(var r=[],i=0;i<t;i+=a){var n=Math.min(i+a,t),l=e.subarray(i,n);r.push(String.fromCharCode.apply(null,l))}return r.join("")}function m(e){"string"!=typeof e&&s("Invalid argument for stringToBytes");for(var t=e.length,a=new Uint8Array(t),r=0;r<t;++r)a[r]=255&e.charCodeAt(r);return a}t.AbortException=v;var b=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e)}),null,[{key:"isLittleEndian",get:function(){return l(this,"isLittleEndian",((e=new Uint8Array(4))[0]=1,1===new Uint32Array(e.buffer,0,1)[0]));var e}},{key:"isEvalSupported",get:function(){return l(this,"isEvalSupported",function(){try{return new Function(""),!0}catch(e){return!1}}())}},{key:"isOffscreenCanvasSupported",get:function(){return l(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}},{key:"platform",get:function(){return"undefined"==typeof navigator?l(this,"platform",{isWin:!1,isMac:!1}):l(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}},{key:"isCSSRoundSupported",get:function(){var e,t;return l(this,"isCSSRoundSupported",null===(e=globalThis.CSS)||void 0===e||null===(t=e.supports)||void 0===t?void 0:t.call(e,"width: round(1.5px, 1px)"))}}])}();t.FeatureTest=b;var g=(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(Array(256).keys()).map((function(e){return e.toString(16).padStart(2,"0")})),P=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e)}),null,[{key:"makeHexColor",value:function(e,t,a){return"#".concat(g[e]).concat(g[t]).concat(g[a])}},{key:"scaleMinMax",value:function(e,t){var a;e[0]?(e[0]<0&&(a=t[0],t[0]=t[1],t[1]=a),t[0]*=e[0],t[1]*=e[0],e[3]<0&&(a=t[2],t[2]=t[3],t[3]=a),t[2]*=e[3],t[3]*=e[3]):(a=t[0],t[0]=t[2],t[2]=a,a=t[1],t[1]=t[3],t[3]=a,e[1]<0&&(a=t[2],t[2]=t[3],t[3]=a),t[2]*=e[1],t[3]*=e[1],e[2]<0&&(a=t[0],t[0]=t[1],t[1]=a),t[0]*=e[2],t[1]*=e[2]),t[0]+=e[4],t[1]+=e[4],t[2]+=e[5],t[3]+=e[5]}},{key:"transform",value:function(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]}},{key:"applyTransform",value:function(e,t){return[e[0]*t[0]+e[1]*t[2]+t[4],e[0]*t[1]+e[1]*t[3]+t[5]]}},{key:"applyInverseTransform",value:function(e,t){var a=t[0]*t[3]-t[1]*t[2];return[(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/a,(-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/a]}},{key:"getAxialAlignedBoundingBox",value:function(e,t){var a=this.applyTransform(e,t),r=this.applyTransform(e.slice(2,4),t),i=this.applyTransform([e[0],e[3]],t),n=this.applyTransform([e[2],e[1]],t);return[Math.min(a[0],r[0],i[0],n[0]),Math.min(a[1],r[1],i[1],n[1]),Math.max(a[0],r[0],i[0],n[0]),Math.max(a[1],r[1],i[1],n[1])]}},{key:"inverseTransform",value:function(e){var t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]}},{key:"singularValueDecompose2dScale",value:function(e){var t=[e[0],e[2],e[1],e[3]],a=e[0]*t[0]+e[1]*t[2],r=e[0]*t[1]+e[1]*t[3],i=e[2]*t[0]+e[3]*t[2],n=e[2]*t[1]+e[3]*t[3],s=(a+n)/2,l=Math.sqrt(Math.pow(a+n,2)-4*(a*n-i*r))/2,o=s+l||1,_=s-l||1;return[Math.sqrt(o),Math.sqrt(_)]}},{key:"normalizeRect",value:function(e){var t=e.slice(0);return e[0]>e[2]&&(t[0]=e[2],t[2]=e[0]),e[1]>e[3]&&(t[1]=e[3],t[3]=e[1]),t}},{key:"intersect",value:function(e,t){var a=Math.max(Math.min(e[0],e[2]),Math.min(t[0],t[2])),r=Math.min(Math.max(e[0],e[2]),Math.max(t[0],t[2]));if(a>r)return null;var i=Math.max(Math.min(e[1],e[3]),Math.min(t[1],t[3])),n=Math.min(Math.max(e[1],e[3]),Math.max(t[1],t[3]));return i>n?null:[a,i,r,n]}},{key:"bezierBoundingBox",value:function(e,t,a,r,i,n,s,l){for(var o,_,c,u,h,d,p,v,f=[],m=[[],[]],b=0;b<2;++b)if(0===b?(_=6*e-12*a+6*i,o=-3*e+9*a-9*i+3*s,c=3*a-3*e):(_=6*t-12*r+6*n,o=-3*t+9*r-9*n+3*l,c=3*r-3*t),Math.abs(o)<1e-12){if(Math.abs(_)<1e-12)continue;0<(u=-c/_)&&u<1&&f.push(u)}else p=_*_-4*c*o,v=Math.sqrt(p),p<0||(0<(h=(-_+v)/(2*o))&&h<1&&f.push(h),0<(d=(-_-v)/(2*o))&&d<1&&f.push(d));for(var g,P=f.length,y=P;P--;)g=1-(u=f[P]),m[0][P]=g*g*g*e+3*g*g*u*a+3*g*u*u*i+u*u*u*s,m[1][P]=g*g*g*t+3*g*g*u*r+3*g*u*u*n+u*u*u*l;return m[0][y]=e,m[1][y]=t,m[0][y+1]=s,m[1][y+1]=l,m[0].length=m[1].length=y+2,[Math.min.apply(Math,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(m[0])),Math.min.apply(Math,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(m[1])),Math.max.apply(Math,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(m[0])),Math.max.apply(Math,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(m[1]))]}}])}();t.Util=P;var y=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function A(e){return decodeURIComponent(escape(e))}var E=new WeakMap,C=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){var t=this;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateFieldInitSpec(this,E,!1),this.promise=new Promise((function(e,a){t.resolve=function(a){_classPrivateFieldSet(E,t,!0),e(a)},t.reject=function(e){_classPrivateFieldSet(E,t,!0),a(e)}}))}),[{key:"settled",get:function(){return _classPrivateFieldGet(E,this)}}])}();t.PromiseCapability=C;var M=null,k=null;t.AnnotationPrefix="pdfjs_internal_id_"},function(__unused_webpack_module,exports,__w_pdfjs_require__){Object.defineProperty(exports,"__esModule",{value:!0}),exports.RenderTask=exports.PDFWorkerUtil=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDocumentLoadingTask=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultFilterFactory=exports.DefaultCanvasFactory=exports.DefaultCMapReaderFactory=void 0,Object.defineProperty(exports,"SVGGraphics",{enumerable:!0,get:function(){return _displaySvg.SVGGraphics}}),exports.build=void 0,exports.getDocument=getDocument,exports.version=void 0;var _util=__w_pdfjs_require__(1),_annotation_storage=__w_pdfjs_require__(3),_display_utils=__w_pdfjs_require__(6),_font_loader=__w_pdfjs_require__(9),_displayNode_utils=__w_pdfjs_require__(10),_canvas=__w_pdfjs_require__(11),_worker_options=__w_pdfjs_require__(14),_message_handler=__w_pdfjs_require__(15),_metadata=__w_pdfjs_require__(16),_optional_content_config=__w_pdfjs_require__(17),_transport_stream=__w_pdfjs_require__(18),_displayFetch_stream=__w_pdfjs_require__(19),_displayNetwork=__w_pdfjs_require__(22),_displayNode_stream=__w_pdfjs_require__(23),_displaySvg=__w_pdfjs_require__(24),_xfa_text=__w_pdfjs_require__(25),DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100,DELAYED_CLEANUP_TIMEOUT=5e3,DefaultCanvasFactory=_util.isNodeJS?_displayNode_utils.NodeCanvasFactory:_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;var DefaultCMapReaderFactory=_util.isNodeJS?_displayNode_utils.NodeCMapReaderFactory:_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;var DefaultFilterFactory=_util.isNodeJS?_displayNode_utils.NodeFilterFactory:_display_utils.DOMFilterFactory;exports.DefaultFilterFactory=DefaultFilterFactory;var DefaultStandardFontDataFactory=_util.isNodeJS?_displayNode_utils.NodeStandardFontDataFactory:_display_utils.DOMStandardFontDataFactory;function getDocument(e){var t,a;if("string"==typeof e||e instanceof URL?e={url:e}:(0,_util.isArrayBuffer)(e)&&(e={data:e}),"object"!==(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(e))throw new Error("Invalid parameter in getDocument, need parameter object.");if(!e.url&&!e.data&&!e.range)throw new Error("Invalid parameter object: need either .data, .range or .url");var r=new PDFDocumentLoadingTask,i=r.docId,n=e.url?getUrlProp(e.url):null,s=e.data?getDataProp(e.data):null,l=e.httpHeaders||null,o=!0===e.withCredentials,_=null!==(t=e.password)&&void 0!==t?t:null,c=e.range instanceof PDFDataRangeTransport?e.range:null,u=Number.isInteger(e.rangeChunkSize)&&e.rangeChunkSize>0?e.rangeChunkSize:DEFAULT_RANGE_CHUNK_SIZE,h=e.worker instanceof PDFWorker?e.worker:null,d=e.verbosity,p="string"!=typeof e.docBaseUrl||(0,_display_utils.isDataScheme)(e.docBaseUrl)?null:e.docBaseUrl,v="string"==typeof e.cMapUrl?e.cMapUrl:null,f=!1!==e.cMapPacked,m=e.CMapReaderFactory||DefaultCMapReaderFactory,b="string"==typeof e.standardFontDataUrl?e.standardFontDataUrl:null,g=e.StandardFontDataFactory||DefaultStandardFontDataFactory,P=!0!==e.stopAtErrors,y=Number.isInteger(e.maxImageSize)&&e.maxImageSize>-1?e.maxImageSize:-1,A=!1!==e.isEvalSupported,E="boolean"==typeof e.isOffscreenCanvasSupported?e.isOffscreenCanvasSupported:!_util.isNodeJS,C=Number.isInteger(e.canvasMaxAreaInBytes)?e.canvasMaxAreaInBytes:-1,M="boolean"==typeof e.disableFontFace?e.disableFontFace:_util.isNodeJS,k=!0===e.fontExtraProperties,O=!0===e.enableXfa,D=e.ownerDocument||globalThis.document,F=!0===e.disableRange,S=!0===e.disableStream,T=!0===e.disableAutoFetch,I=!0===e.pdfBug,w=c?c.length:null!==(a=e.length)&&void 0!==a?a:NaN,x="boolean"==typeof e.useSystemFonts?e.useSystemFonts:!_util.isNodeJS&&!M,R="boolean"==typeof e.useWorkerFetch?e.useWorkerFetch:m===_display_utils.DOMCMapReaderFactory&&g===_display_utils.DOMStandardFontDataFactory&&v&&b&&(0,_display_utils.isValidFetchUrl)(v,document.baseURI)&&(0,_display_utils.isValidFetchUrl)(b,document.baseURI),L=e.canvasFactory||new DefaultCanvasFactory({ownerDocument:D}),B=e.filterFactory||new DefaultFilterFactory({docId:i,ownerDocument:D});(0,_util.setVerbosityLevel)(d);var W={canvasFactory:L,filterFactory:B};if(R||(W.cMapReaderFactory=new m({baseUrl:v,isCompressed:f}),W.standardFontDataFactory=new g({baseUrl:b})),!h){var U={verbosity:d,port:_worker_options.GlobalWorkerOptions.workerPort};h=U.port?PDFWorker.fromPort(U):new PDFWorker(U),r._worker=h}var G={docId:i,apiVersion:"3.11.174",data:s,password:_,disableAutoFetch:T,rangeChunkSize:u,length:w,docBaseUrl:p,enableXfa:O,evaluatorOptions:{maxImageSize:y,disableFontFace:M,ignoreErrors:P,isEvalSupported:A,isOffscreenCanvasSupported:E,canvasMaxAreaInBytes:C,fontExtraProperties:k,useSystemFonts:x,cMapUrl:R?v:null,standardFontDataUrl:R?b:null}},K={ignoreErrors:P,isEvalSupported:A,disableFontFace:M,fontExtraProperties:k,enableXfa:O,ownerDocument:D,disableAutoFetch:T,pdfBug:I,styleElement:null};return h.promise.then((function(){if(r.destroyed)throw new Error("Loading aborted");var e=_fetchDocument(h,G),t=new Promise((function(e){var t,a;c?t=new _transport_stream.PDFDataTransportStream({length:w,initialData:c.initialData,progressiveDone:c.progressiveDone,contentDispositionFilename:c.contentDispositionFilename,disableRange:F,disableStream:S},c):s||(a={url:n,length:w,httpHeaders:l,withCredentials:o,rangeChunkSize:u,disableRange:F,disableStream:S},t=_util.isNodeJS?new _displayNode_stream.PDFNodeStream(a):(0,_display_utils.isValidFetchUrl)(a.url)?new _displayFetch_stream.PDFFetchStream(a):new _displayNetwork.PDFNetworkStream(a)),e(t)}));return Promise.all([e,t]).then((function(e){var t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,2),a=t[0],n=t[1];if(r.destroyed)throw new Error("Loading aborted");var s=new _message_handler.MessageHandler(i,a,h.port),l=new WorkerTransport(s,r,n,K,W);r._transport=l,s.send("Ready",null)}))})).catch(r._capability.reject),r}function _fetchDocument(e,t){return _fetchDocument2.apply(this,arguments)}function _fetchDocument2(){return(_fetchDocument2=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t,a){var r;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.destroyed){e.next=1;break}throw new Error("Worker was destroyed");case 1:return e.next=2,t.messageHandler.sendWithPromise("GetDocRequest",a,a.data?[a.data.buffer]:null);case 2:if(r=e.sent,!t.destroyed){e.next=3;break}throw new Error("Worker was destroyed");case 3:return e.abrupt("return",r);case 4:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function getUrlProp(e){if(e instanceof URL)return e.href;try{return new URL(e,window.location).href}catch(t){if(_util.isNodeJS&&"string"==typeof e)return e}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function getDataProp(e){if(_util.isNodeJS&&"undefined"!=typeof Buffer&&e instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength)return e;if("string"==typeof e)return(0,_util.stringToBytes)(e);if("object"===(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(e)&&!isNaN(null==e?void 0:e.length)||(0,_util.isArrayBuffer)(e))return new Uint8Array(e);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory;var PDFDocumentLoadingTask=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){var t,a;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._capability=new _util.PromiseCapability,this._transport=null,this._worker=null,this.docId="d".concat((_docId._=(t=_docId._,a=t++,t),a)),this.destroyed=!1,this.onPassword=null,this.onProgress=null}),[{key:"promise",get:function(){return this._capability.promise}},{key:"destroy",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a,r,i;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.destroyed=!0,e.prev=1,null!==(t=this._worker)&&void 0!==t&&t.port&&(this._worker._pendingDestroy=!0),e.next=2,null===(a=this._transport)||void 0===a?void 0:a.destroy();case 2:e.next=4;break;case 3:throw e.prev=3,i=e.catch(1),null!==(r=this._worker)&&void 0!==r&&r.port&&delete this._worker._pendingDestroy,i;case 4:this._transport=null,this._worker&&(this._worker.destroy(),this._worker=null);case 5:case"end":return e.stop()}}),e,this,[[1,3]])}))),function(){return e.apply(this,arguments)})}]);var e}(),_docId={_:0};exports.PDFDocumentLoadingTask=PDFDocumentLoadingTask;var PDFDataRangeTransport=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.length=t,this.initialData=a,this.progressiveDone=r,this.contentDispositionFilename=i,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=new _util.PromiseCapability}),[{key:"addRangeListener",value:function(e){this._rangeListeners.push(e)}},{key:"addProgressListener",value:function(e){this._progressListeners.push(e)}},{key:"addProgressiveReadListener",value:function(e){this._progressiveReadListeners.push(e)}},{key:"addProgressiveDoneListener",value:function(e){this._progressiveDoneListeners.push(e)}},{key:"onDataRange",value:function(e,t){var a,r=_createForOfIteratorHelper(this._rangeListeners);try{for(r.s();!(a=r.n()).done;)(0,a.value)(e,t)}catch(e){r.e(e)}finally{r.f()}}},{key:"onDataProgress",value:function(e,t){var a=this;this._readyCapability.promise.then((function(){var r,i=_createForOfIteratorHelper(a._progressListeners);try{for(i.s();!(r=i.n()).done;)(0,r.value)(e,t)}catch(e){i.e(e)}finally{i.f()}}))}},{key:"onDataProgressiveRead",value:function(e){var t=this;this._readyCapability.promise.then((function(){var a,r=_createForOfIteratorHelper(t._progressiveReadListeners);try{for(r.s();!(a=r.n()).done;)(0,a.value)(e)}catch(e){r.e(e)}finally{r.f()}}))}},{key:"onDataProgressiveDone",value:function(){var e=this;this._readyCapability.promise.then((function(){var t,a=_createForOfIteratorHelper(e._progressiveDoneListeners);try{for(a.s();!(t=a.n()).done;)(0,t.value)()}catch(e){a.e(e)}finally{a.f()}}))}},{key:"transportReady",value:function(){this._readyCapability.resolve()}},{key:"requestDataRange",value:function(e,t){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}},{key:"abort",value:function(){}}])}();exports.PDFDataRangeTransport=PDFDataRangeTransport;var PDFDocumentProxy=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a){var r=this;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._pdfInfo=t,this._transport=a,Object.defineProperty(this,"getJavaScript",{value:function(){return(0,_display_utils.deprecated)("`PDFDocumentProxy.getJavaScript`, please use `PDFDocumentProxy.getJSActions` instead."),r.getJSActions().then((function(e){if(!e)return e;var t=[];for(var a in e)t.push.apply(t,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(e[a]));return t}))}})}),[{key:"annotationStorage",get:function(){return this._transport.annotationStorage}},{key:"filterFactory",get:function(){return this._transport.filterFactory}},{key:"numPages",get:function(){return this._pdfInfo.numPages}},{key:"fingerprints",get:function(){return this._pdfInfo.fingerprints}},{key:"isPureXfa",get:function(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}},{key:"allXfaHtml",get:function(){return this._transport._htmlForXfa}},{key:"getPage",value:function(e){return this._transport.getPage(e)}},{key:"getPageIndex",value:function(e){return this._transport.getPageIndex(e)}},{key:"getDestinations",value:function(){return this._transport.getDestinations()}},{key:"getDestination",value:function(e){return this._transport.getDestination(e)}},{key:"getPageLabels",value:function(){return this._transport.getPageLabels()}},{key:"getPageLayout",value:function(){return this._transport.getPageLayout()}},{key:"getPageMode",value:function(){return this._transport.getPageMode()}},{key:"getViewerPreferences",value:function(){return this._transport.getViewerPreferences()}},{key:"getOpenAction",value:function(){return this._transport.getOpenAction()}},{key:"getAttachments",value:function(){return this._transport.getAttachments()}},{key:"getJSActions",value:function(){return this._transport.getDocJSActions()}},{key:"getOutline",value:function(){return this._transport.getOutline()}},{key:"getOptionalContentConfig",value:function(){return this._transport.getOptionalContentConfig()}},{key:"getPermissions",value:function(){return this._transport.getPermissions()}},{key:"getMetadata",value:function(){return this._transport.getMetadata()}},{key:"getMarkInfo",value:function(){return this._transport.getMarkInfo()}},{key:"getData",value:function(){return this._transport.getData()}},{key:"saveDocument",value:function(){return this._transport.saveDocument()}},{key:"getDownloadInfo",value:function(){return this._transport.downloadInfoCapability.promise}},{key:"cleanup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._transport.startCleanup(e||this.isPureXfa)}},{key:"destroy",value:function(){return this.loadingTask.destroy()}},{key:"loadingParams",get:function(){return this._transport.loadingParams}},{key:"loadingTask",get:function(){return this._transport.loadingTask}},{key:"getFieldObjects",value:function(){return this._transport.getFieldObjects()}},{key:"hasJSActions",value:function(){return this._transport.hasJSActions()}},{key:"getCalculationOrderIds",value:function(){return this._transport.getCalculationOrderIds()}}])}();exports.PDFDocumentProxy=PDFDocumentProxy;var _delayedCleanupTimeout=new WeakMap,_pendingCleanup=new WeakMap,_PDFPageProxy_brand=new WeakSet,PDFPageProxy=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a,r){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,_PDFPageProxy_brand),_classPrivateFieldInitSpec(this,_delayedCleanupTimeout,null),_classPrivateFieldInitSpec(this,_pendingCleanup,!1),this._pageIndex=t,this._pageInfo=a,this._transport=r,this._stats=i?new _display_utils.StatTimer:null,this._pdfBug=i,this.commonObjs=r.commonObjs,this.objs=new PDFObjects,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}),[{key:"pageNumber",get:function(){return this._pageIndex+1}},{key:"rotate",get:function(){return this._pageInfo.rotate}},{key:"ref",get:function(){return this._pageInfo.ref}},{key:"userUnit",get:function(){return this._pageInfo.userUnit}},{key:"view",get:function(){return this._pageInfo.view}},{key:"getViewport",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.scale,a=e.rotation,r=void 0===a?this.rotate:a,i=e.offsetX,n=void 0===i?0:i,s=e.offsetY,l=void 0===s?0:s,o=e.dontFlip,_=void 0!==o&&o;return new _display_utils.PageViewport({viewBox:this.view,scale:t,rotation:r,offsetX:n,offsetY:l,dontFlip:_})}},{key:"getAnnotations",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).intent,t=void 0===e?"display":e,a=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,a.renderingIntent)}},{key:"getJSActions",value:function(){return this._transport.getPageJSActions(this._pageIndex)}},{key:"filterFactory",get:function(){return this._transport.filterFactory}},{key:"isPureXfa",get:function(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}},{key:"getXfa",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(null===(t=this._transport._htmlForXfa)||void 0===t?void 0:t.children[this._pageIndex])||null);case 1:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"render",value:function(e){var t,a,r=this,i=e.canvasContext,n=e.viewport,s=e.intent,l=void 0===s?"display":s,o=e.annotationMode,_=void 0===o?_util.AnnotationMode.ENABLE:o,c=e.transform,u=void 0===c?null:c,h=e.background,d=void 0===h?null:h,p=e.optionalContentConfigPromise,v=void 0===p?null:p,f=e.annotationCanvasMap,m=void 0===f?null:f,b=e.pageColors,g=void 0===b?null:b,P=e.printAnnotationStorage,y=void 0===P?null:P;null===(t=this._stats)||void 0===t||t.time("Overall");var A=this._transport.getRenderingIntent(l,_,y);_classPrivateFieldSet(_pendingCleanup,this,!1),_assertClassBrand(_PDFPageProxy_brand,this,_abortDelayedCleanup).call(this),v||(v=this._transport.getOptionalContentConfig());var E=this._intentStates.get(A.cacheKey);E||(E=Object.create(null),this._intentStates.set(A.cacheKey,E)),E.streamReaderCancelTimeout&&(clearTimeout(E.streamReaderCancelTimeout),E.streamReaderCancelTimeout=null);var C,M=!!(A.renderingIntent&_util.RenderingIntentFlag.PRINT);E.displayReadyCapability||(E.displayReadyCapability=new _util.PromiseCapability,E.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},null===(C=this._stats)||void 0===C||C.time("Page Request"),this._pumpOperatorList(A));var k=function(e){var t,a;E.renderTasks.delete(O),(r._maybeCleanupAfterRender||M)&&_classPrivateFieldSet(_pendingCleanup,r,!0),_assertClassBrand(_PDFPageProxy_brand,r,_tryCleanup).call(r,!M),e?(O.capability.reject(e),r._abortOperatorList({intentState:E,reason:e instanceof Error?e:new Error(e)})):O.capability.resolve(),null===(t=r._stats)||void 0===t||t.timeEnd("Rendering"),null===(a=r._stats)||void 0===a||a.timeEnd("Overall")},O=new InternalRenderTask({callback:k,params:{canvasContext:i,viewport:n,transform:u,background:d},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:m,operatorList:E.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!M,pdfBug:this._pdfBug,pageColors:g});((a=E).renderTasks||(a.renderTasks=new Set)).add(O);var D=O.task;return Promise.all([E.displayReadyCapability.promise,v]).then((function(e){var t,a=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,2),i=a[0],n=a[1];r.destroyed?k():(null===(t=r._stats)||void 0===t||t.time("Rendering"),O.initializeGraphics({transparency:i,optionalContentConfig:n}),O.operatorListChanged())})).catch(k),D}},{key:"getOperatorList",value:function(){var e,t,a,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=r.intent,n=void 0===i?"display":i,s=r.annotationMode,l=void 0===s?_util.AnnotationMode.ENABLE:s,o=r.printAnnotationStorage,_=void 0===o?null:o,c=this._transport.getRenderingIntent(n,l,_,!0),u=this._intentStates.get(c.cacheKey);return u||(u=Object.create(null),this._intentStates.set(c.cacheKey,u)),u.opListReadCapability||((e=Object.create(null)).operatorListChanged=function(){u.operatorList.lastChunk&&(u.opListReadCapability.resolve(u.operatorList),u.renderTasks.delete(e))},u.opListReadCapability=new _util.PromiseCapability,((t=u).renderTasks||(t.renderTasks=new Set)).add(e),u.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},null===(a=this._stats)||void 0===a||a.time("Page Request"),this._pumpOperatorList(c)),u.opListReadCapability.promise}},{key:"streamTextContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.includeMarkedContent,a=void 0!==t&&t,r=e.disableNormalization,i=void 0!==r&&r;return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===a,disableNormalization:!0===i},{highWaterMark:100,size:function(e){return e.items.length}})}},{key:"getTextContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this._transport._htmlForXfa)return this.getXfa().then((function(e){return _xfa_text.XfaText.textContent(e)}));var t=this.streamTextContent(e);return new Promise((function(e,a){var r=t.getReader(),i={items:[],styles:Object.create(null)};!function t(){r.read().then((function(a){var r,n=a.value;a.done?e(i):(Object.assign(i.styles,n.styles),(r=i.items).push.apply(r,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(n.items)),t())}),a)}()}))}},{key:"getStructTree",value:function(){return this._transport.getStructTree(this._pageIndex)}},{key:"_destroy",value:function(){this.destroyed=!0;var e,t=[],a=_createForOfIteratorHelper(this._intentStates.values());try{for(a.s();!(e=a.n()).done;){var r=e.value;if(this._abortOperatorList({intentState:r,reason:new Error("Page was destroyed."),force:!0}),!r.opListReadCapability){var i,n=_createForOfIteratorHelper(r.renderTasks);try{for(n.s();!(i=n.n()).done;){var s=i.value;t.push(s.completed),s.cancel()}}catch(e){n.e(e)}finally{n.f()}}}}catch(e){a.e(e)}finally{a.f()}return this.objs.clear(),_classPrivateFieldSet(_pendingCleanup,this,!1),_assertClassBrand(_PDFPageProxy_brand,this,_abortDelayedCleanup).call(this),Promise.all(t)}},{key:"cleanup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];_classPrivateFieldSet(_pendingCleanup,this,!0);var t=_assertClassBrand(_PDFPageProxy_brand,this,_tryCleanup).call(this,!1);return e&&t&&this._stats&&(this._stats=new _display_utils.StatTimer),t}},{key:"_startRenderPage",value:function(e,t){var a,r,i=this._intentStates.get(t);i&&(null===(a=this._stats)||void 0===a||a.timeEnd("Page Request"),null===(r=i.displayReadyCapability)||void 0===r||r.resolve(e))}},{key:"_renderPageChunk",value:function(e,t){for(var a=0,r=e.length;a<r;a++)t.operatorList.fnArray.push(e.fnArray[a]),t.operatorList.argsArray.push(e.argsArray[a]);t.operatorList.lastChunk=e.lastChunk,t.operatorList.separateAnnots=e.separateAnnots;var i,n=_createForOfIteratorHelper(t.renderTasks);try{for(n.s();!(i=n.n()).done;)i.value.operatorListChanged()}catch(e){n.e(e)}finally{n.f()}e.lastChunk&&_assertClassBrand(_PDFPageProxy_brand,this,_tryCleanup).call(this,!0)}},{key:"_pumpOperatorList",value:function(e){var t=this,a=e.renderingIntent,r=e.cacheKey,i=e.annotationStorageSerializable,n=i.map,s=i.transfers,l=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:a,cacheKey:r,annotationStorage:n},s).getReader(),o=this._intentStates.get(r);o.streamReader=l;var _=function(){l.read().then((function(e){var a=e.value;e.done?o.streamReader=null:t._transport.destroyed||(t._renderPageChunk(a,o),_())}),(function(e){if(o.streamReader=null,!t._transport.destroyed){if(o.operatorList){o.operatorList.lastChunk=!0;var a,r=_createForOfIteratorHelper(o.renderTasks);try{for(r.s();!(a=r.n()).done;)a.value.operatorListChanged()}catch(e){r.e(e)}finally{r.f()}_assertClassBrand(_PDFPageProxy_brand,t,_tryCleanup).call(t,!0)}if(o.displayReadyCapability)o.displayReadyCapability.reject(e);else{if(!o.opListReadCapability)throw e;o.opListReadCapability.reject(e)}}}))};_()}},{key:"_abortOperatorList",value:function(e){var t=this,a=e.intentState,r=e.reason,i=e.force,n=void 0!==i&&i;if(a.streamReader){if(a.streamReaderCancelTimeout&&(clearTimeout(a.streamReaderCancelTimeout),a.streamReaderCancelTimeout=null),!n){if(a.renderTasks.size>0)return;if(r instanceof _display_utils.RenderingCancelledException){var s=RENDERING_CANCELLED_TIMEOUT;return r.extraDelay>0&&r.extraDelay<1e3&&(s+=r.extraDelay),void(a.streamReaderCancelTimeout=setTimeout((function(){a.streamReaderCancelTimeout=null,t._abortOperatorList({intentState:a,reason:r,force:!0})}),s))}}if(a.streamReader.cancel(new _util.AbortException(r.message)).catch((function(){})),a.streamReader=null,!this._transport.destroyed){var l,o=_createForOfIteratorHelper(this._intentStates);try{for(o.s();!(l=o.n()).done;){var _=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(l.value,2),c=_[0];if(_[1]===a){this._intentStates.delete(c);break}}}catch(e){o.e(e)}finally{o.f()}this.cleanup()}}}},{key:"stats",get:function(){return this._stats}}]);var e}();function _tryCleanup(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(_assertClassBrand(_PDFPageProxy_brand,this,_abortDelayedCleanup).call(this),!_classPrivateFieldGet(_pendingCleanup,this)||this.destroyed)return!1;if(t)return _classPrivateFieldSet(_delayedCleanupTimeout,this,setTimeout((function(){_classPrivateFieldSet(_delayedCleanupTimeout,e,null),_assertClassBrand(_PDFPageProxy_brand,e,_tryCleanup).call(e,!1)}),DELAYED_CLEANUP_TIMEOUT)),!1;var a,r=_createForOfIteratorHelper(this._intentStates.values());try{for(r.s();!(a=r.n()).done;){var i=a.value,n=i.renderTasks,s=i.operatorList;if(n.size>0||!s.lastChunk)return!1}}catch(e){r.e(e)}finally{r.f()}return this._intentStates.clear(),this.objs.clear(),_classPrivateFieldSet(_pendingCleanup,this,!1),!0}function _abortDelayedCleanup(){_classPrivateFieldGet(_delayedCleanupTimeout,this)&&(clearTimeout(_classPrivateFieldGet(_delayedCleanupTimeout,this)),_classPrivateFieldSet(_delayedCleanupTimeout,this,null))}exports.PDFPageProxy=PDFPageProxy;var _listeners=new WeakMap,_deferred=new WeakMap,LoopbackPort=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateFieldInitSpec(this,_listeners,new Set),_classPrivateFieldInitSpec(this,_deferred,Promise.resolve())}),[{key:"postMessage",value:function(e,t){var a=this,r={data:structuredClone(e,t?{transfer:t}:null)};_classPrivateFieldGet(_deferred,this).then((function(){var e,t=_createForOfIteratorHelper(_classPrivateFieldGet(_listeners,a));try{for(t.s();!(e=t.n()).done;)e.value.call(a,r)}catch(e){t.e(e)}finally{t.f()}}))}},{key:"addEventListener",value:function(e,t){_classPrivateFieldGet(_listeners,this).add(t)}},{key:"removeEventListener",value:function(e,t){_classPrivateFieldGet(_listeners,this).delete(t)}},{key:"terminate",value:function(){_classPrivateFieldGet(_listeners,this).clear()}}])}();exports.LoopbackPort=LoopbackPort;var PDFWorkerUtil={isWorkerDisabled:!1,fallbackWorkerSrc:null,fakeWorkerId:0};if(exports.PDFWorkerUtil=PDFWorkerUtil,_util.isNodeJS)PDFWorkerUtil.isWorkerDisabled=!0,PDFWorkerUtil.fallbackWorkerSrc="./pdf.worker.js";else if("object"===("undefined"==typeof document?"undefined":(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(document))){var _document,pdfjsFilePath=null===(_document=document)||void 0===_document||null===(_document=_document.currentScript)||void 0===_document?void 0:_document.src;pdfjsFilePath&&(PDFWorkerUtil.fallbackWorkerSrc=pdfjsFilePath.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}PDFWorkerUtil.isSameOrigin=function(e,t){var a;try{if(!(a=new URL(e)).origin||"null"===a.origin)return!1}catch(e){return!1}var r=new URL(t,a);return a.origin===r.origin},PDFWorkerUtil.createCDNWrapper=function(e){var t='importScripts("'.concat(e,'");');return URL.createObjectURL(new Blob([t]))};var PDFWorker=function(){function PDFWorker(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name,a=void 0===t?null:t,r=e.port,i=void 0===r?null:r,n=e.verbosity,s=void 0===n?(0,_util.getVerbosityLevel)():n;if((0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,PDFWorker),this.name=a,this.destroyed=!1,this.verbosity=s,this._readyCapability=new _util.PromiseCapability,this._port=null,this._webWorker=null,this._messageHandler=null,i){var l;if(null!==(l=_workerPorts._)&&void 0!==l&&l.has(i))throw new Error("Cannot use more than one PDFWorker per port.");return(_workerPorts._||(_workerPorts._=new WeakMap)).set(i,this),void this._initializeFromPort(i)}this._initialize()}return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(PDFWorker,[{key:"promise",get:function(){return this._readyCapability.promise}},{key:"port",get:function(){return this._port}},{key:"messageHandler",get:function(){return this._messageHandler}},{key:"_initializeFromPort",value:function(e){this._port=e,this._messageHandler=new _message_handler.MessageHandler("main","worker",e),this._messageHandler.on("ready",(function(){})),this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})}},{key:"_initialize",value:function(){var e=this;if(!PDFWorkerUtil.isWorkerDisabled&&!PDFWorker._mainThreadWorkerMessageHandler){var t=PDFWorker.workerSrc;try{PDFWorkerUtil.isSameOrigin(window.location.href,t)||(t=PDFWorkerUtil.createCDNWrapper(new URL(t,window.location).href));var a=new Worker(t),r=new _message_handler.MessageHandler("main","worker",a),i=function(){a.removeEventListener("error",n),r.destroy(),a.terminate(),e.destroyed?e._readyCapability.reject(new Error("Worker was destroyed")):e._setupFakeWorker()},n=function(){e._webWorker||i()};a.addEventListener("error",n),r.on("test",(function(t){a.removeEventListener("error",n),e.destroyed?i():t?(e._messageHandler=r,e._port=a,e._webWorker=a,e._readyCapability.resolve(),r.send("configure",{verbosity:e.verbosity})):(e._setupFakeWorker(),r.destroy(),a.terminate())})),r.on("ready",(function(t){if(a.removeEventListener("error",n),e.destroyed)i();else try{s()}catch(t){e._setupFakeWorker()}}));var s=function(){var e=new Uint8Array;r.send("test",e,[e.buffer])};return void s()}catch(e){(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}},{key:"_setupFakeWorker",value:function(){var e=this;PDFWorkerUtil.isWorkerDisabled||((0,_util.warn)("Setting up fake worker."),PDFWorkerUtil.isWorkerDisabled=!0),PDFWorker._setupFakeWorkerGlobal.then((function(t){if(e.destroyed)e._readyCapability.reject(new Error("Worker was destroyed"));else{var a=new LoopbackPort;e._port=a;var r="fake".concat(PDFWorkerUtil.fakeWorkerId++),i=new _message_handler.MessageHandler(r+"_worker",r,a);t.setup(i,a);var n=new _message_handler.MessageHandler(r,r+"_worker",a);e._messageHandler=n,e._readyCapability.resolve(),n.send("configure",{verbosity:e.verbosity})}})).catch((function(t){e._readyCapability.reject(new Error('Setting up fake worker failed: "'.concat(t.message,'".')))}))}},{key:"destroy",value:function(){var e;this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),null===(e=_workerPorts._)||void 0===e||e.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}}],[{key:"fromPort",value:function(e){var t;if(null==e||!e.port)throw new Error("PDFWorker.fromPort - invalid method signature.");var a=null===(t=_assertClassBrand(PDFWorker,this,_workerPorts)._)||void 0===t?void 0:t.get(e.port);if(a){if(a._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return a}return new PDFWorker(e)}},{key:"workerSrc",get:function(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(null!==PDFWorkerUtil.fallbackWorkerSrc)return _util.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.'),PDFWorkerUtil.fallbackWorkerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}},{key:"_mainThreadWorkerMessageHandler",get:function(){try{var e;return(null===(e=globalThis.pdfjsWorker)||void 0===e?void 0:e.WorkerMessageHandler)||null}catch(e){return null}}},{key:"_setupFakeWorkerGlobal",get:function get(){var _this13=this,loader=function(){var _ref13=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function _callee3(){var mainWorkerMessageHandler,worker;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(_context3){for(;;)switch(_context3.prev=_context3.next){case 0:if(mainWorkerMessageHandler=_this13._mainThreadWorkerMessageHandler,!mainWorkerMessageHandler){_context3.next=1;break}return _context3.abrupt("return",mainWorkerMessageHandler);case 1:if(!_util.isNodeJS){_context3.next=2;break}return worker=eval("require")(_this13.workerSrc),_context3.abrupt("return",worker.WorkerMessageHandler);case 2:return _context3.next=3,(0,_display_utils.loadScript)(_this13.workerSrc);case 3:return _context3.abrupt("return",window.pdfjsWorker.WorkerMessageHandler);case 4:case"end":return _context3.stop()}}),_callee3)})));return function(){return _ref13.apply(this,arguments)}}();return(0,_util.shadow)(this,"_setupFakeWorkerGlobal",loader())}}])}(),_workerPorts={_:void 0};exports.PDFWorker=PDFWorker;var _methodPromises=new WeakMap,_pageCache=new WeakMap,_pagePromises=new WeakMap,_passwordCapability=new WeakMap,_WorkerTransport_brand=new WeakSet,WorkerTransport=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a,r,i,n){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,_WorkerTransport_brand),_classPrivateFieldInitSpec(this,_methodPromises,new Map),_classPrivateFieldInitSpec(this,_pageCache,new Map),_classPrivateFieldInitSpec(this,_pagePromises,new Map),_classPrivateFieldInitSpec(this,_passwordCapability,null),this.messageHandler=t,this.loadingTask=a,this.commonObjs=new PDFObjects,this.fontLoader=new _font_loader.FontLoader({ownerDocument:i.ownerDocument,styleElement:i.styleElement}),this._params=i,this.canvasFactory=n.canvasFactory,this.filterFactory=n.filterFactory,this.cMapReaderFactory=n.cMapReaderFactory,this.standardFontDataFactory=n.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=r,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=new _util.PromiseCapability,this.setupMessageHandler()}),[{key:"annotationStorage",get:function(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}},{key:"getRenderingIntent",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_util.AnnotationMode.ENABLE,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=_util.RenderingIntentFlag.DISPLAY,n=_annotation_storage.SerializableEmpty;switch(e){case"any":i=_util.RenderingIntentFlag.ANY;break;case"display":break;case"print":i=_util.RenderingIntentFlag.PRINT;break;default:(0,_util.warn)("getRenderingIntent - invalid intent: ".concat(e))}switch(t){case _util.AnnotationMode.DISABLE:i+=_util.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case _util.AnnotationMode.ENABLE:break;case _util.AnnotationMode.ENABLE_FORMS:i+=_util.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case _util.AnnotationMode.ENABLE_STORAGE:n=((i+=_util.RenderingIntentFlag.ANNOTATIONS_STORAGE)&_util.RenderingIntentFlag.PRINT&&a instanceof _annotation_storage.PrintAnnotationStorage?a:this.annotationStorage).serializable;break;default:(0,_util.warn)("getRenderingIntent - invalid annotationMode: ".concat(t))}return r&&(i+=_util.RenderingIntentFlag.OPLIST),{renderingIntent:i,cacheKey:"".concat(i,"_").concat(n.hash),annotationStorageSerializable:n}}},{key:"destroy",value:function(){var e,t=this;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=new _util.PromiseCapability,null===(e=_classPrivateFieldGet(_passwordCapability,this))||void 0===e||e.reject(new Error("Worker was destroyed during onPassword callback"));var a,r=[],i=_createForOfIteratorHelper(_classPrivateFieldGet(_pageCache,this).values());try{for(i.s();!(a=i.n()).done;){var n=a.value;r.push(n._destroy())}}catch(e){i.e(e)}finally{i.f()}_classPrivateFieldGet(_pageCache,this).clear(),_classPrivateFieldGet(_pagePromises,this).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();var s=this.messageHandler.sendWithPromise("Terminate",null);return r.push(s),Promise.all(r).then((function(){var e;t.commonObjs.clear(),t.fontLoader.clear(),_classPrivateFieldGet(_methodPromises,t).clear(),t.filterFactory.destroy(),null===(e=t._networkStream)||void 0===e||e.cancelAllRequests(new _util.AbortException("Worker was terminated.")),t.messageHandler&&(t.messageHandler.destroy(),t.messageHandler=null),t.destroyCapability.resolve()}),this.destroyCapability.reject),this.destroyCapability.promise}},{key:"setupMessageHandler",value:function(){var e=this,t=this.messageHandler,a=this.loadingTask;t.on("GetReader",(function(t,a){(0,_util.assert)(e._networkStream,"GetReader - no `IPDFStream` instance available."),e._fullReader=e._networkStream.getFullReader(),e._fullReader.onProgress=function(t){e._lastProgress={loaded:t.loaded,total:t.total}},a.onPull=function(){e._fullReader.read().then((function(e){var t=e.value;e.done?a.close():((0,_util.assert)(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),a.enqueue(new Uint8Array(t),1,[t]))})).catch((function(e){a.error(e)}))},a.onCancel=function(t){e._fullReader.cancel(t),a.ready.catch((function(t){if(!e.destroyed)throw t}))}})),t.on("ReaderHeadersReady",(function(t){var r=new _util.PromiseCapability,i=e._fullReader;return i.headersReady.then((function(){var t;i.isStreamingSupported&&i.isRangeSupported||(e._lastProgress&&(null===(t=a.onProgress)||void 0===t||t.call(a,e._lastProgress)),i.onProgress=function(e){var t;null===(t=a.onProgress)||void 0===t||t.call(a,{loaded:e.loaded,total:e.total})}),r.resolve({isStreamingSupported:i.isStreamingSupported,isRangeSupported:i.isRangeSupported,contentLength:i.contentLength})}),r.reject),r.promise})),t.on("GetRangeReader",(function(t,a){(0,_util.assert)(e._networkStream,"GetRangeReader - no `IPDFStream` instance available.");var r=e._networkStream.getRangeReader(t.begin,t.end);r?(a.onPull=function(){r.read().then((function(e){var t=e.value;e.done?a.close():((0,_util.assert)(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),a.enqueue(new Uint8Array(t),1,[t]))})).catch((function(e){a.error(e)}))},a.onCancel=function(t){r.cancel(t),a.ready.catch((function(t){if(!e.destroyed)throw t}))}):a.close()})),t.on("GetDoc",(function(t){var r=t.pdfInfo;e._numPages=r.numPages,e._htmlForXfa=r.htmlForXfa,delete r.htmlForXfa,a._capability.resolve(new PDFDocumentProxy(r,e))})),t.on("DocException",(function(e){var t;switch(e.name){case"PasswordException":t=new _util.PasswordException(e.message,e.code);break;case"InvalidPDFException":t=new _util.InvalidPDFException(e.message);break;case"MissingPDFException":t=new _util.MissingPDFException(e.message);break;case"UnexpectedResponseException":t=new _util.UnexpectedResponseException(e.message,e.status);break;case"UnknownErrorException":t=new _util.UnknownErrorException(e.message,e.details);break;default:(0,_util.unreachable)("DocException - expected a valid Error.")}a._capability.reject(t)})),t.on("PasswordRequest",(function(t){if(_classPrivateFieldSet(_passwordCapability,e,new _util.PromiseCapability),a.onPassword)try{a.onPassword((function(t){t instanceof Error?_classPrivateFieldGet(_passwordCapability,e).reject(t):_classPrivateFieldGet(_passwordCapability,e).resolve({password:t})}),t.code)}catch(t){_classPrivateFieldGet(_passwordCapability,e).reject(t)}else _classPrivateFieldGet(_passwordCapability,e).reject(new _util.PasswordException(t.message,t.code));return _classPrivateFieldGet(_passwordCapability,e).promise})),t.on("DataLoaded",(function(t){var r;null===(r=a.onProgress)||void 0===r||r.call(a,{loaded:t.length,total:t.length}),e.downloadInfoCapability.resolve(t)})),t.on("StartRenderPage",(function(t){e.destroyed||_classPrivateFieldGet(_pageCache,e).get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)})),t.on("commonobj",(function(a){var r,i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(a,3),n=i[0],s=i[1],l=i[2];if(!e.destroyed&&!e.commonObjs.has(n))switch(s){case"Font":var o=e._params;if("error"in l){var _=l.error;(0,_util.warn)("Error during font loading: ".concat(_)),e.commonObjs.resolve(n,_);break}var c=o.pdfBug&&null!==(r=globalThis.FontInspector)&&void 0!==r&&r.enabled?function(e,t){return globalThis.FontInspector.fontAdded(e,t)}:null,u=new _font_loader.FontFaceObject(l,{isEvalSupported:o.isEvalSupported,disableFontFace:o.disableFontFace,ignoreErrors:o.ignoreErrors,inspectFont:c});e.fontLoader.bind(u).catch((function(e){return t.sendWithPromise("FontFallback",{id:n})})).finally((function(){!o.fontExtraProperties&&u.data&&(u.data=null),e.commonObjs.resolve(n,u)}));break;case"FontPath":case"Image":case"Pattern":e.commonObjs.resolve(n,l);break;default:throw new Error("Got unknown common object type ".concat(s))}})),t.on("obj",(function(t){var a=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(t,4),r=a[0],i=a[1],n=a[2],s=a[3];if(!e.destroyed){var l=_classPrivateFieldGet(_pageCache,e).get(i);if(!l.objs.has(r))switch(n){case"Image":var o;l.objs.resolve(r,s),s&&(s.bitmap?s.width*s.height*4:(null===(o=s.data)||void 0===o?void 0:o.length)||0)>_util.MAX_IMAGE_SIZE_TO_CACHE&&(l._maybeCleanupAfterRender=!0);break;case"Pattern":l.objs.resolve(r,s);break;default:throw new Error("Got unknown object type ".concat(n))}}})),t.on("DocProgress",(function(t){var r;e.destroyed||null===(r=a.onProgress)||void 0===r||r.call(a,{loaded:t.loaded,total:t.total})})),t.on("FetchBuiltInCMap",(function(t){return e.destroyed?Promise.reject(new Error("Worker was destroyed.")):e.cMapReaderFactory?e.cMapReaderFactory.fetch(t):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))})),t.on("FetchStandardFontData",(function(t){return e.destroyed?Promise.reject(new Error("Worker was destroyed.")):e.standardFontDataFactory?e.standardFontDataFactory.fetch(t):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter."))}))}},{key:"getData",value:function(){return this.messageHandler.sendWithPromise("GetData",null)}},{key:"saveDocument",value:function(){var e,t,a=this;this.annotationStorage.size<=0&&(0,_util.warn)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");var r=this.annotationStorage.serializable,i=r.map,n=r.transfers;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:i,filename:null!==(e=null===(t=this._fullReader)||void 0===t?void 0:t.filename)&&void 0!==e?e:null},n).finally((function(){a.annotationStorage.resetModified()}))}},{key:"getPage",value:function(e){var t=this;if(!Number.isInteger(e)||e<=0||e>this._numPages)return Promise.reject(new Error("Invalid page request."));var a=e-1,r=_classPrivateFieldGet(_pagePromises,this).get(a);if(r)return r;var i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:a}).then((function(e){if(t.destroyed)throw new Error("Transport destroyed");var r=new PDFPageProxy(a,e,t,t._params.pdfBug);return _classPrivateFieldGet(_pageCache,t).set(a,r),r}));return _classPrivateFieldGet(_pagePromises,this).set(a,i),i}},{key:"getPageIndex",value:function(e){return"object"!==(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(e)||null===e||!Number.isInteger(e.num)||e.num<0||!Number.isInteger(e.gen)||e.gen<0?Promise.reject(new Error("Invalid pageIndex request.")):this.messageHandler.sendWithPromise("GetPageIndex",{num:e.num,gen:e.gen})}},{key:"getAnnotations",value:function(e,t){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:e,intent:t})}},{key:"getFieldObjects",value:function(){return _assertClassBrand(_WorkerTransport_brand,this,_cacheSimpleMethod).call(this,"GetFieldObjects")}},{key:"hasJSActions",value:function(){return _assertClassBrand(_WorkerTransport_brand,this,_cacheSimpleMethod).call(this,"HasJSActions")}},{key:"getCalculationOrderIds",value:function(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}},{key:"getDestinations",value:function(){return this.messageHandler.sendWithPromise("GetDestinations",null)}},{key:"getDestination",value:function(e){return"string"!=typeof e?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:e})}},{key:"getPageLabels",value:function(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}},{key:"getPageLayout",value:function(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}},{key:"getPageMode",value:function(){return this.messageHandler.sendWithPromise("GetPageMode",null)}},{key:"getViewerPreferences",value:function(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}},{key:"getOpenAction",value:function(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}},{key:"getAttachments",value:function(){return this.messageHandler.sendWithPromise("GetAttachments",null)}},{key:"getDocJSActions",value:function(){return _assertClassBrand(_WorkerTransport_brand,this,_cacheSimpleMethod).call(this,"GetDocJSActions")}},{key:"getPageJSActions",value:function(e){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:e})}},{key:"getStructTree",value:function(e){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:e})}},{key:"getOutline",value:function(){return this.messageHandler.sendWithPromise("GetOutline",null)}},{key:"getOptionalContentConfig",value:function(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then((function(e){return new _optional_content_config.OptionalContentConfig(e)}))}},{key:"getPermissions",value:function(){return this.messageHandler.sendWithPromise("GetPermissions",null)}},{key:"getMetadata",value:function(){var e=this,t="GetMetadata",a=_classPrivateFieldGet(_methodPromises,this).get(t);if(a)return a;var r=this.messageHandler.sendWithPromise(t,null).then((function(t){var a,r,i,n;return{info:t[0],metadata:t[1]?new _metadata.Metadata(t[1]):null,contentDispositionFilename:null!==(a=null===(r=e._fullReader)||void 0===r?void 0:r.filename)&&void 0!==a?a:null,contentLength:null!==(i=null===(n=e._fullReader)||void 0===n?void 0:n.contentLength)&&void 0!==i?i:null}}));return _classPrivateFieldGet(_methodPromises,this).set(t,r),r}},{key:"getMarkInfo",value:function(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}},{key:"startCleanup",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a,r,i,n,s=arguments;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=s.length>0&&void 0!==s[0]&&s[0],!this.destroyed){e.next=1;break}return e.abrupt("return");case 1:return e.next=2,this.messageHandler.sendWithPromise("Cleanup",null);case 2:a=_createForOfIteratorHelper(_classPrivateFieldGet(_pageCache,this).values()),e.prev=3,a.s();case 4:if((r=a.n()).done){e.next=6;break}if((i=r.value).cleanup()){e.next=5;break}throw new Error("startCleanup: Page ".concat(i.pageNumber," is currently rendering."));case 5:e.next=4;break;case 6:e.next=8;break;case 7:e.prev=7,n=e.catch(3),a.e(n);case 8:return e.prev=8,a.f(),e.finish(8);case 9:this.commonObjs.clear(),t||this.fontLoader.clear(),_classPrivateFieldGet(_methodPromises,this).clear(),this.filterFactory.destroy(!0);case 10:case"end":return e.stop()}}),e,this,[[3,7,8,9]])}))),function(){return e.apply(this,arguments)})},{key:"loadingParams",get:function(){var e=this._params,t=e.disableAutoFetch,a=e.enableXfa;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:t,enableXfa:a})}}]);var e}();function _cacheSimpleMethod(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=_classPrivateFieldGet(_methodPromises,this).get(e);if(a)return a;var r=this.messageHandler.sendWithPromise(e,t);return _classPrivateFieldGet(_methodPromises,this).set(e,r),r}var _objs=new WeakMap,_PDFObjects_brand=new WeakSet,PDFObjects=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,_PDFObjects_brand),_classPrivateFieldInitSpec(this,_objs,Object.create(null))}),[{key:"get",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(t){var a=_assertClassBrand(_PDFObjects_brand,this,_ensureObj).call(this,e);return a.capability.promise.then((function(){return t(a.data)})),null}var r=_classPrivateFieldGet(_objs,this)[e];if(null==r||!r.capability.settled)throw new Error("Requesting object that isn't resolved yet ".concat(e,"."));return r.data}},{key:"has",value:function(e){var t=_classPrivateFieldGet(_objs,this)[e];return(null==t?void 0:t.capability.settled)||!1}},{key:"resolve",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=_assertClassBrand(_PDFObjects_brand,this,_ensureObj).call(this,e);a.data=t,a.capability.resolve()}},{key:"clear",value:function(){for(var e in _classPrivateFieldGet(_objs,this)){var t,a=_classPrivateFieldGet(_objs,this)[e].data;null==a||null===(t=a.bitmap)||void 0===t||t.close()}_classPrivateFieldSet(_objs,this,Object.create(null))}}])}();function _ensureObj(e){var t;return(t=_classPrivateFieldGet(_objs,this))[e]||(t[e]={capability:new _util.PromiseCapability,data:null})}var _internalRenderTask=new WeakMap,RenderTask=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateFieldInitSpec(this,_internalRenderTask,null),_classPrivateFieldSet(_internalRenderTask,this,t),this.onContinue=null}),[{key:"promise",get:function(){return _classPrivateFieldGet(_internalRenderTask,this).capability.promise}},{key:"cancel",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;_classPrivateFieldGet(_internalRenderTask,this).cancel(null,e)}},{key:"separateAnnots",get:function(){var e=_classPrivateFieldGet(_internalRenderTask,this).operatorList.separateAnnots;if(!e)return!1;var t=_classPrivateFieldGet(_internalRenderTask,this).annotationCanvasMap;return e.form||e.canvas&&(null==t?void 0:t.size)>0}}])}();exports.RenderTask=RenderTask;var InternalRenderTask=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a=t.callback,r=t.params,i=t.objs,n=t.commonObjs,s=t.annotationCanvasMap,l=t.operatorList,o=t.pageIndex,_=t.canvasFactory,c=t.filterFactory,u=t.useRequestAnimationFrame,h=void 0!==u&&u,d=t.pdfBug,p=void 0!==d&&d,v=t.pageColors,f=void 0===v?null:v;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.callback=a,this.params=r,this.objs=i,this.commonObjs=n,this.annotationCanvasMap=s,this.operatorListIdx=null,this.operatorList=l,this._pageIndex=o,this.canvasFactory=_,this.filterFactory=c,this._pdfBug=p,this.pageColors=f,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window,this.cancelled=!1,this.capability=new _util.PromiseCapability,this.task=new RenderTask(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=r.canvasContext.canvas}),[{key:"completed",get:function(){return this.capability.promise.catch((function(){}))}},{key:"initializeGraphics",value:function(e){var t,a,r=e.transparency,i=void 0!==r&&r,n=e.optionalContentConfig;if(!this.cancelled){if(this._canvas){if(_canvasInUse._.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");_canvasInUse._.add(this._canvas)}this._pdfBug&&null!==(t=globalThis.StepperManager)&&void 0!==t&&t.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());var s=this.params,l=s.canvasContext,o=s.viewport,_=s.transform,c=s.background;this.gfx=new _canvas.CanvasGraphics(l,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:n},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:_,viewport:o,transparency:i,background:c}),this.operatorListIdx=0,this.graphicsReady=!0,null===(a=this.graphicsReadyCallback)||void 0===a||a.call(this)}}},{key:"cancel",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.running=!1,this.cancelled=!0,null===(e=this.gfx)||void 0===e||e.endDrawing(),_canvasInUse._.delete(this._canvas),this.callback(t||new _display_utils.RenderingCancelledException("Rendering cancelled, page ".concat(this._pageIndex+1),a))}},{key:"operatorListChanged",value:function(){var e;this.graphicsReady?(null===(e=this.stepper)||void 0===e||e.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)}},{key:"_continue",value:function(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}},{key:"_scheduleNext",value:function(){var e=this;this._useRequestAnimationFrame?window.requestAnimationFrame((function(){e._nextBound().catch(e._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}},{key:"_next",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.cancelled){e.next=1;break}return e.abrupt("return");case 1:this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),_canvasInUse._.delete(this._canvas),this.callback()));case 2:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})}]);var e}(),_canvasInUse={_:new WeakSet},version="3.11.174";exports.version=version;var build="ce8716743";exports.build=build},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.SerializableEmpty=t.PrintAnnotationStorage=t.AnnotationStorage=void 0;var r=a(1),i=a(4),n=a(8),s=Object.freeze({map:null,hash:"",transfers:void 0});t.SerializableEmpty=s;var l=new WeakMap,o=new WeakMap,_=new WeakSet,c=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,_),_classPrivateFieldInitSpec(this,l,!1),_classPrivateFieldInitSpec(this,o,new Map),this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}),[{key:"getValue",value:function(e,t){var a=_classPrivateFieldGet(o,this).get(e);return void 0===a?t:Object.assign(t,a)}},{key:"getRawValue",value:function(e){return _classPrivateFieldGet(o,this).get(e)}},{key:"remove",value:function(e){if(_classPrivateFieldGet(o,this).delete(e),0===_classPrivateFieldGet(o,this).size&&this.resetModified(),"function"==typeof this.onAnnotationEditor){var t,a=_createForOfIteratorHelper(_classPrivateFieldGet(o,this).values());try{for(a.s();!(t=a.n()).done;)if(t.value instanceof i.AnnotationEditor)return}catch(e){a.e(e)}finally{a.f()}this.onAnnotationEditor(null)}}},{key:"setValue",value:function(e,t){var a=_classPrivateFieldGet(o,this).get(e),r=!1;if(void 0!==a)for(var n=0,s=Object.entries(t);n<s.length;n++){var l=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(s[n],2),c=l[0],h=l[1];a[c]!==h&&(r=!0,a[c]=h)}else r=!0,_classPrivateFieldGet(o,this).set(e,t);r&&_assertClassBrand(_,this,u).call(this),t instanceof i.AnnotationEditor&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(t.constructor._type)}},{key:"has",value:function(e){return _classPrivateFieldGet(o,this).has(e)}},{key:"getAll",value:function(){return _classPrivateFieldGet(o,this).size>0?(0,r.objectFromMap)(_classPrivateFieldGet(o,this)):null}},{key:"setAll",value:function(e){for(var t=0,a=Object.entries(e);t<a.length;t++){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(a[t],2),i=r[0],n=r[1];this.setValue(i,n)}}},{key:"size",get:function(){return _classPrivateFieldGet(o,this).size}},{key:"resetModified",value:function(){_classPrivateFieldGet(l,this)&&(_classPrivateFieldSet(l,this,!1),"function"==typeof this.onResetModified&&this.onResetModified())}},{key:"print",get:function(){return new d(this)}},{key:"serializable",get:function(){if(0===_classPrivateFieldGet(o,this).size)return s;var e,t=new Map,a=new n.MurmurHash3_64,r=[],l=Object.create(null),_=!1,c=_createForOfIteratorHelper(_classPrivateFieldGet(o,this));try{for(c.s();!(e=c.n()).done;){var u=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e.value,2),h=u[0],d=u[1],p=d instanceof i.AnnotationEditor?d.serialize(!1,l):d;p&&(t.set(h,p),a.update("".concat(h,":").concat(JSON.stringify(p))),_||(_=!!p.bitmap))}}catch(e){c.e(e)}finally{c.f()}if(_){var v,f=_createForOfIteratorHelper(t.values());try{for(f.s();!(v=f.n()).done;){var m=v.value;m.bitmap&&r.push(m.bitmap)}}catch(e){f.e(e)}finally{f.f()}}return t.size>0?{map:t,hash:a.hexdigest(),transfers:r}:s}}])}();function u(){_classPrivateFieldGet(l,this)||(_classPrivateFieldSet(l,this,!0),"function"==typeof this.onSetModified&&this.onSetModified())}t.AnnotationStorage=c;var h=new WeakMap,d=function(e){function t(e){var a;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateFieldInitSpec(a=_callSuper(this,t),h,void 0);var r=e.serializable,i=r.map,n=r.hash,s=r.transfers,l=structuredClone(i,s?{transfer:s}:null);return _classPrivateFieldSet(h,a,{map:l,hash:n,transfers:s}),a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"print",get:function(){(0,r.unreachable)("Should not call PrintAnnotationStorage.print")}},{key:"serializable",get:function(){return _classPrivateFieldGet(h,this)}}])}(c);t.PrintAnnotationStorage=d},function(e,t,a){var r;Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationEditor=void 0;var i=a(5),n=a(1),s=a(6),l=new WeakMap,o=new WeakMap,_=new WeakMap,c=new WeakMap,u=new WeakMap,h=new WeakMap,d=new WeakMap,p=new WeakMap,v=new WeakMap,f=new WeakMap,m=new WeakMap,b=new WeakMap,g=new WeakMap,P=new WeakMap,y=new WeakSet,A=function(){function e(t){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,y),_classPrivateFieldInitSpec(this,l,""),_classPrivateFieldInitSpec(this,o,!1),_classPrivateFieldInitSpec(this,_,null),_classPrivateFieldInitSpec(this,c,null),_classPrivateFieldInitSpec(this,u,null),_classPrivateFieldInitSpec(this,h,!1),_classPrivateFieldInitSpec(this,d,null),_classPrivateFieldInitSpec(this,p,this.focusin.bind(this)),_classPrivateFieldInitSpec(this,v,this.focusout.bind(this)),_classPrivateFieldInitSpec(this,f,!1),_classPrivateFieldInitSpec(this,m,!1),_classPrivateFieldInitSpec(this,b,!1),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(this,"_initialOptions",Object.create(null)),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(this,"_uiManager",null),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(this,"_focusEventsAllowed",!0),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(this,"_l10nPromise",null),_classPrivateFieldInitSpec(this,g,!1),_classPrivateFieldInitSpec(this,P,e._zIndex++),this.constructor===e&&(0,n.unreachable)("Cannot initialize AnnotationEditor."),this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;var a=this.parent.viewport,r=a.rotation,i=a.rawDims,s=i.pageWidth,A=i.pageHeight,E=i.pageX,C=i.pageY;this.rotation=r,this.pageRotation=(360+r-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[s,A],this.pageTranslation=[E,C];var M=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),k=M[0],O=M[1];this.x=t.x/k,this.y=t.y/O,this.isAttachedToDOM=!1,this.deleted=!1}return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(e,[{key:"editorType",get:function(){return Object.getPrototypeOf(this).constructor._type}},{key:"propertiesToUpdate",get:function(){return[]}},{key:"_isDraggable",get:function(){return _classPrivateFieldGet(g,this)},set:function(e){var t;_classPrivateFieldSet(g,this,e),null===(t=this.div)||void 0===t||t.classList.toggle("draggable",e)}},{key:"center",value:function(){var e=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageDimensions,2),t=e[0],a=e[1];switch(this.parentRotation){case 90:this.x-=this.height*a/(2*t),this.y+=this.width*t/(2*a);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*a/(2*t),this.y-=this.width*t/(2*a);break;default:this.x-=this.width/2,this.y-=this.height/2}this.fixAndSetPosition()}},{key:"addCommands",value:function(e){this._uiManager.addCommands(e)}},{key:"currentLayer",get:function(){return this._uiManager.currentLayer}},{key:"setInBackground",value:function(){this.div.style.zIndex=0}},{key:"setInForeground",value:function(){this.div.style.zIndex=_classPrivateFieldGet(P,this)}},{key:"setParent",value:function(e){null!==e&&(this.pageIndex=e.pageIndex,this.pageDimensions=e.pageDimensions),this.parent=e}},{key:"focusin",value:function(e){this._focusEventsAllowed&&(_classPrivateFieldGet(f,this)?_classPrivateFieldSet(f,this,!1):this.parent.setSelected(this))}},{key:"focusout",value:function(e){var t;if(this._focusEventsAllowed&&this.isAttachedToDOM){var a=e.relatedTarget;null!=a&&a.closest("#".concat(this.id))||(e.preventDefault(),null!==(t=this.parent)&&void 0!==t&&t.isMultipleSelection||this.commitOrRemove())}}},{key:"commitOrRemove",value:function(){this.isEmpty()?this.remove():this.commit()}},{key:"commit",value:function(){this.addToAnnotationStorage()}},{key:"addToAnnotationStorage",value:function(){this._uiManager.addToAnnotationStorage(this)}},{key:"setAt",value:function(e,t,a,r){var i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),n=i[0],s=i[1],l=this.screenToPageTranslation(a,r),o=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(l,2);a=o[0],r=o[1],this.x=(e+a)/n,this.y=(t+r)/s,this.fixAndSetPosition()}},{key:"translate",value:function(e,t){_assertClassBrand(y,this,E).call(this,this.parentDimensions,e,t)}},{key:"translateInPage",value:function(e,t){_assertClassBrand(y,this,E).call(this,this.pageDimensions,e,t),this.div.scrollIntoView({block:"nearest"})}},{key:"drag",value:function(e,t){var a=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),r=a[0],i=a[1];if(this.x+=e/r,this.y+=t/i,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){var n=this.div.getBoundingClientRect(),s=n.x,l=n.y;this.parent.findNewParent(this,s,l)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}var o=this.x,_=this.y,c=_assertClassBrand(y,this,C).call(this),u=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(c,2);o+=u[0],_+=u[1],this.div.style.left="".concat((100*o).toFixed(2),"%"),this.div.style.top="".concat((100*_).toFixed(2),"%"),this.div.scrollIntoView({block:"nearest"})}},{key:"fixAndSetPosition",value:function(){var e=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageDimensions,2),t=e[0],a=e[1],r=this.x,i=this.y,n=this.width,s=this.height;switch(n*=t,s*=a,r*=t,i*=a,this.rotation){case 0:r=Math.max(0,Math.min(t-n,r)),i=Math.max(0,Math.min(a-s,i));break;case 90:r=Math.max(0,Math.min(t-s,r)),i=Math.min(a,Math.max(n,i));break;case 180:r=Math.min(t,Math.max(n,r)),i=Math.min(a,Math.max(s,i));break;case 270:r=Math.min(t,Math.max(s,r)),i=Math.max(0,Math.min(a-n,i))}this.x=r/=t,this.y=i/=a;var l=_assertClassBrand(y,this,C).call(this),o=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(l,2);r+=o[0],i+=o[1];var _=this.div.style;_.left="".concat((100*r).toFixed(2),"%"),_.top="".concat((100*i).toFixed(2),"%"),this.moveInDOM()}},{key:"screenToPageTranslation",value:function(t,a){return M.call(e,t,a,this.parentRotation)}},{key:"pageTranslationToScreen",value:function(t,a){return M.call(e,t,a,360-this.parentRotation)}},{key:"parentScale",get:function(){return this._uiManager.viewParameters.realScale}},{key:"parentRotation",get:function(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}},{key:"parentDimensions",get:function(){var e=this.parentScale,t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageDimensions,2),a=t[0]*e,r=t[1]*e;return n.FeatureTest.isCSSRoundSupported?[Math.round(a),Math.round(r)]:[a,r]}},{key:"setDims",value:function(t,a){var r,i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),n=i[0],s=i[1];this.div.style.width="".concat((100*t/n).toFixed(2),"%"),_classPrivateFieldGet(h,this)||(this.div.style.height="".concat((100*a/s).toFixed(2),"%")),null===(r=_classPrivateFieldGet(_,this))||void 0===r||r.classList.toggle("small",t<e.SMALL_EDITOR_SIZE||a<e.SMALL_EDITOR_SIZE)}},{key:"fixDims",value:function(){var e=this.div.style,t=e.height,a=e.width,r=a.endsWith("%"),i=!_classPrivateFieldGet(h,this)&&t.endsWith("%");if(!r||!i){var n=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),s=n[0],l=n[1];r||(e.width="".concat((100*parseFloat(a)/s).toFixed(2),"%")),_classPrivateFieldGet(h,this)||i||(e.height="".concat((100*parseFloat(t)/l).toFixed(2),"%"))}}},{key:"getInitialTranslation",value:function(){return[0,0]}},{key:"addAltTextButton",value:(t=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function t(){var a,r,i=this;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!_classPrivateFieldGet(_,this)){t.next=1;break}return t.abrupt("return");case 1:return(a=_classPrivateFieldSet(_,this,document.createElement("button"))).className="altText",t.next=2,e._l10nPromise.get("editor_alt_text_button_label");case 2:r=t.sent,a.textContent=r,a.setAttribute("aria-label",r),a.tabIndex="0",a.addEventListener("contextmenu",s.noContextMenu),a.addEventListener("pointerdown",(function(e){return e.stopPropagation()})),a.addEventListener("click",(function(e){e.preventDefault(),i._uiManager.editAltText(i)}),{capture:!0}),a.addEventListener("keydown",(function(e){e.target===a&&"Enter"===e.key&&(e.preventDefault(),i._uiManager.editAltText(i))})),_assertClassBrand(y,this,S).call(this),this.div.append(a),e.SMALL_EDITOR_SIZE||(e.SMALL_EDITOR_SIZE=Math.min(128,Math.round(1.4*a.getBoundingClientRect().width)));case 3:case"end":return t.stop()}}),t,this)}))),function(){return t.apply(this,arguments)})},{key:"getClientDimensions",value:function(){return this.div.getBoundingClientRect()}},{key:"altTextData",get:function(){return{altText:_classPrivateFieldGet(l,this),decorative:_classPrivateFieldGet(o,this)}},set:function(e){var t=e.altText,a=e.decorative;_classPrivateFieldGet(l,this)===t&&_classPrivateFieldGet(o,this)===a||(_classPrivateFieldSet(l,this,t),_classPrivateFieldSet(o,this,a),_assertClassBrand(y,this,S).call(this))}},{key:"render",value:function(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.setAttribute("tabIndex",0),this.setInForeground(),this.div.addEventListener("focusin",_classPrivateFieldGet(p,this)),this.div.addEventListener("focusout",_classPrivateFieldGet(v,this));var e=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),t=e[0],a=e[1];this.parentRotation%180!=0&&(this.div.style.maxWidth="".concat((100*a/t).toFixed(2),"%"),this.div.style.maxHeight="".concat((100*t/a).toFixed(2),"%"));var r=this.getInitialTranslation(),n=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(r,2),s=n[0],l=n[1];return this.translate(s,l),(0,i.bindEvents)(this,this.div,["pointerdown"]),this.div}},{key:"pointerdown",value:function(e){var t=n.FeatureTest.platform.isMac;0!==e.button||e.ctrlKey&&t?e.preventDefault():(_classPrivateFieldSet(f,this,!0),_assertClassBrand(y,this,I).call(this,e))}},{key:"moveInDOM",value:function(){var e;null===(e=this.parent)||void 0===e||e.moveEditorInDOM(this)}},{key:"_setParentAndPosition",value:function(e,t,a){e.changeParent(this),this.x=t,this.y=a,this.fixAndSetPosition()}},{key:"getRect",value:function(e,t){var a=this.parentScale,r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageDimensions,2),i=r[0],n=r[1],s=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageTranslation,2),l=s[0],o=s[1],_=e/a,c=t/a,u=this.x*i,h=this.y*n,d=this.width*i,p=this.height*n;switch(this.rotation){case 0:return[u+_+l,n-h-c-p+o,u+_+d+l,n-h-c+o];case 90:return[u+c+l,n-h+_+o,u+c+p+l,n-h+_+d+o];case 180:return[u-_-d+l,n-h+c+o,u-_+l,n-h+c+p+o];case 270:return[u-c-p+l,n-h-_-d+o,u-c+l,n-h-_+o];default:throw new Error("Invalid rotation")}}},{key:"getRectInCurrentCoords",value:function(e,t){var a=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,4),r=a[0],i=a[1],n=a[2],s=a[3],l=n-r,o=s-i;switch(this.rotation){case 0:return[r,t-s,l,o];case 90:return[r,t-i,o,l];case 180:return[n,t-i,l,o];case 270:return[n,t-s,o,l];default:throw new Error("Invalid rotation")}}},{key:"onceAdded",value:function(){}},{key:"isEmpty",value:function(){return!1}},{key:"enableEditMode",value:function(){_classPrivateFieldSet(b,this,!0)}},{key:"disableEditMode",value:function(){_classPrivateFieldSet(b,this,!1)}},{key:"isInEditMode",value:function(){return _classPrivateFieldGet(b,this)}},{key:"shouldGetKeyboardEvents",value:function(){return!1}},{key:"needsToBeRebuilt",value:function(){return this.div&&!this.isAttachedToDOM}},{key:"rebuild",value:function(){var e,t;null===(e=this.div)||void 0===e||e.addEventListener("focusin",_classPrivateFieldGet(p,this)),null===(t=this.div)||void 0===t||t.addEventListener("focusout",_classPrivateFieldGet(v,this))}},{key:"serialize",value:function(){(0,n.unreachable)("An editor must be serializable")}},{key:"remove",value:function(){var e;this.div.removeEventListener("focusin",_classPrivateFieldGet(p,this)),this.div.removeEventListener("focusout",_classPrivateFieldGet(v,this)),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),null===(e=_classPrivateFieldGet(_,this))||void 0===e||e.remove(),_classPrivateFieldSet(_,this,null),_classPrivateFieldSet(c,this,null)}},{key:"isResizable",get:function(){return!1}},{key:"makeResizable",value:function(){this.isResizable&&(_assertClassBrand(y,this,O).call(this),_classPrivateFieldGet(d,this).classList.remove("hidden"))}},{key:"select",value:function(){var e;this.makeResizable(),null===(e=this.div)||void 0===e||e.classList.add("selectedEditor")}},{key:"unselect",value:function(){var e,t,a;null===(e=_classPrivateFieldGet(d,this))||void 0===e||e.classList.add("hidden"),null===(t=this.div)||void 0===t||t.classList.remove("selectedEditor"),null!==(a=this.div)&&void 0!==a&&a.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus()}},{key:"updateParams",value:function(e,t){}},{key:"disableEditing",value:function(){_classPrivateFieldGet(_,this)&&(_classPrivateFieldGet(_,this).hidden=!0)}},{key:"enableEditing",value:function(){_classPrivateFieldGet(_,this)&&(_classPrivateFieldGet(_,this).hidden=!1)}},{key:"enterInEditMode",value:function(){}},{key:"contentDiv",get:function(){return this.div}},{key:"isEditing",get:function(){return _classPrivateFieldGet(m,this)},set:function(e){_classPrivateFieldSet(m,this,e),this.parent&&(e?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}},{key:"setAspectRatio",value:function(e,t){_classPrivateFieldSet(h,this,!0);var a=e/t,r=this.div.style;r.aspectRatio=a,r.height="auto"}}],[{key:"_defaultLineColor",get:function(){return(0,n.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}},{key:"deleteAnnotationElement",value:function(e){var t=new w({id:e.parent.getNextId(),parent:e.parent,uiManager:e._uiManager});t.annotationElementId=e.annotationElementId,t.deleted=!0,t._uiManager.addToAnnotationStorage(t)}},{key:"initialize",value:function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(e._l10nPromise||(e._l10nPromise=new Map(["editor_alt_text_button_label","editor_alt_text_edit_button_label","editor_alt_text_decorative_tooltip"].map((function(e){return[e,t.get(e)]})))),null!=a&&a.strings){var r,i=_createForOfIteratorHelper(a.strings);try{for(i.s();!(r=i.n()).done;){var n=r.value;e._l10nPromise.set(n,t.get(n))}}catch(e){i.e(e)}finally{i.f()}}if(-1===e._borderLineWidth){var s=getComputedStyle(document.documentElement);e._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}}},{key:"updateDefaultParams",value:function(e,t){}},{key:"defaultPropertiesToUpdate",get:function(){return[]}},{key:"isHandlingMimeForPasting",value:function(e){return!1}},{key:"paste",value:function(e,t){(0,n.unreachable)("Not implemented")}},{key:"deserialize",value:function(e,t,a){var r=new this.prototype.constructor({parent:t,id:t.getNextId(),uiManager:a});r.rotation=e.rotation;var i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(r.pageDimensions,2),n=i[0],s=i[1],l=r.getRectInCurrentCoords(e.rect,s),o=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(l,4),_=o[0],c=o[1],u=o[2],h=o[3];return r.x=_/n,r.y=c/s,r.width=u/n,r.height=h/s,r}},{key:"MIN_SIZE",get:function(){return 16}}]);var t}();function E(e,t,a){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,2),i=r[0],n=r[1],s=this.screenToPageTranslation(t,a),l=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(s,2);t=l[0],a=l[1],this.x+=t/i,this.y+=a/n,this.fixAndSetPosition()}function C(){var e=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),t=e[0],a=e[1],i=r._borderLineWidth,n=i/t,s=i/a;switch(this.rotation){case 90:return[-n,s];case 180:return[n,s];case 270:return[n,-s];default:return[-n,-s]}}function M(e,t,a){switch(a){case 90:return[t,-e];case 180:return[-e,-t];case 270:return[-t,e];default:return[e,t]}}function k(e){switch(e){case 90:var t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageDimensions,2),a=t[0],r=t[1];return[0,-a/r,r/a,0];case 180:return[-1,0,0,-1];case 270:var i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageDimensions,2),n=i[0],s=i[1];return[0,n/s,-s/n,0];default:return[1,0,0,1]}}function O(){if(!_classPrivateFieldGet(d,this)){_classPrivateFieldSet(d,this,document.createElement("div")),_classPrivateFieldGet(d,this).classList.add("resizers");var e=["topLeft","topRight","bottomRight","bottomLeft"];this._willKeepAspectRatio||e.push("topMiddle","middleRight","bottomMiddle","middleLeft");for(var t=0,a=e;t<a.length;t++){var r=a[t],i=document.createElement("div");_classPrivateFieldGet(d,this).append(i),i.classList.add("resizer",r),i.addEventListener("pointerdown",_assertClassBrand(y,this,D).bind(this,r)),i.addEventListener("contextmenu",s.noContextMenu)}this.div.prepend(_classPrivateFieldGet(d,this))}}function D(e,t){var a=this;t.preventDefault();var r=n.FeatureTest.platform.isMac;if(!(0!==t.button||t.ctrlKey&&r)){var i=_assertClassBrand(y,this,F).bind(this,e),s=this._isDraggable;this._isDraggable=!1;var l={passive:!0,capture:!0};window.addEventListener("pointermove",i,l);var o=this.x,_=this.y,c=this.width,u=this.height,h=this.parent.div.style.cursor,d=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(t.target).cursor;var p=function(){a._isDraggable=s,window.removeEventListener("pointerup",p),window.removeEventListener("blur",p),window.removeEventListener("pointermove",i,l),a.parent.div.style.cursor=h,a.div.style.cursor=d;var e=a.x,t=a.y,r=a.width,n=a.height;e===o&&t===_&&r===c&&n===u||a.addCommands({cmd:function(){a.width=r,a.height=n,a.x=e,a.y=t;var i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(a.parentDimensions,2),s=i[0],l=i[1];a.setDims(s*r,l*n),a.fixAndSetPosition()},undo:function(){a.width=c,a.height=u,a.x=o,a.y=_;var e=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(a.parentDimensions,2),t=e[0],r=e[1];a.setDims(t*c,r*u),a.fixAndSetPosition()},mustExec:!0})};window.addEventListener("pointerup",p),window.addEventListener("blur",p)}}function F(e,t){var a,i,n=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),s=n[0],l=n[1],o=this.x,_=this.y,c=this.width,u=this.height,h=r.MIN_SIZE/s,d=r.MIN_SIZE/l,p=function(e){return Math.round(1e4*e)/1e4},v=_assertClassBrand(y,this,k).call(this,this.rotation),f=function(e,t){return[v[0]*e+v[2]*t,v[1]*e+v[3]*t]},m=_assertClassBrand(y,this,k).call(this,360-this.rotation),b=!1,g=!1;switch(e){case"topLeft":b=!0,a=function(e,t){return[0,0]},i=function(e,t){return[e,t]};break;case"topMiddle":a=function(e,t){return[e/2,0]},i=function(e,t){return[e/2,t]};break;case"topRight":b=!0,a=function(e,t){return[e,0]},i=function(e,t){return[0,t]};break;case"middleRight":g=!0,a=function(e,t){return[e,t/2]},i=function(e,t){return[0,t/2]};break;case"bottomRight":b=!0,a=function(e,t){return[e,t]},i=function(e,t){return[0,0]};break;case"bottomMiddle":a=function(e,t){return[e/2,t]},i=function(e,t){return[e/2,0]};break;case"bottomLeft":b=!0,a=function(e,t){return[0,t]},i=function(e,t){return[e,0]};break;case"middleLeft":g=!0,a=function(e,t){return[0,t/2]},i=function(e,t){return[e,t/2]}}var P,A,E=a(c,u),C=i(c,u),M=f.apply(void 0,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(C)),O=p(o+M[0]),D=p(_+M[1]),F=1,S=1,T=this.screenToPageTranslation(t.movementX,t.movementY),I=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(T,2),w=I[0],x=I[1],R=(P=w/s,A=x/l,[m[0]*P+m[2]*A,m[1]*P+m[3]*A]),L=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(R,2);if(w=L[0],x=L[1],b){var B=Math.hypot(c,u);F=S=Math.max(Math.min(Math.hypot(C[0]-E[0]-w,C[1]-E[1]-x)/B,1/c,1/u),h/c,d/u)}else g?F=Math.max(h,Math.min(1,Math.abs(C[0]-E[0]-w)))/c:S=Math.max(d,Math.min(1,Math.abs(C[1]-E[1]-x)))/u;var W=p(c*F),U=p(u*S),G=O-(M=f.apply(void 0,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(i(W,U))))[0],K=D-M[1];this.width=W,this.height=U,this.x=G,this.y=K,this.setDims(s*W,l*U),this.fixAndSetPosition()}function S(){return T.apply(this,arguments)}function T(){return(T=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a,i,n,s,h=this;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=_classPrivateFieldGet(_,this)){e.next=1;break}return e.abrupt("return");case 1:if(_classPrivateFieldGet(l,this)||_classPrivateFieldGet(o,this)){e.next=2;break}return t.classList.remove("done"),null===(a=_classPrivateFieldGet(c,this))||void 0===a||a.remove(),e.abrupt("return");case 2:if(r._l10nPromise.get("editor_alt_text_edit_button_label").then((function(e){t.setAttribute("aria-label",e)})),(i=_classPrivateFieldGet(c,this))||(_classPrivateFieldSet(c,this,i=document.createElement("span")),i.className="tooltip",i.setAttribute("role","tooltip"),n=i.id="alt-text-tooltip-".concat(this.id),t.setAttribute("aria-describedby",n),t.addEventListener("mouseenter",(function(){_classPrivateFieldSet(u,h,setTimeout((function(){_classPrivateFieldSet(u,h,null),_classPrivateFieldGet(c,h).classList.add("show"),h._uiManager._eventBus.dispatch("reporttelemetry",{source:h,details:{type:"editing",subtype:h.editorType,data:{action:"alt_text_tooltip"}}})}),100))})),t.addEventListener("mouseleave",(function(){var e;clearTimeout(_classPrivateFieldGet(u,h)),_classPrivateFieldSet(u,h,null),null===(e=_classPrivateFieldGet(c,h))||void 0===e||e.classList.remove("show")}))),t.classList.add("done"),!_classPrivateFieldGet(o,this)){e.next=4;break}return e.next=3,r._l10nPromise.get("editor_alt_text_decorative_tooltip");case 3:s=e.sent,e.next=5;break;case 4:s=_classPrivateFieldGet(l,this);case 5:i.innerText=s,i.parentNode||t.append(i);case 6:case"end":return e.stop()}}),e,this)})))).apply(this,arguments)}function I(e){var t=this;if(this._isDraggable){var a,r,i=this._uiManager.isSelected(this);this._uiManager.setUpDragSession(),i&&(a={passive:!0,capture:!0},r=function(e){var a=t.screenToPageTranslation(e.movementX,e.movementY),r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(a,2),i=r[0],n=r[1];t._uiManager.dragSelectedEditors(i,n)},window.addEventListener("pointermove",r,a));var s=function(){if(window.removeEventListener("pointerup",s),window.removeEventListener("blur",s),i&&window.removeEventListener("pointermove",r,a),_classPrivateFieldSet(f,t,!1),!t._uiManager.endDragSession()){var l=n.FeatureTest.platform.isMac;e.ctrlKey&&!l||e.shiftKey||e.metaKey&&l?t.parent.toggleSelected(t):t.parent.setSelected(t)}};window.addEventListener("pointerup",s),window.addEventListener("blur",s)}}r=A,(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(A,"_borderLineWidth",-1),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(A,"_colorManager",new i.ColorManager),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(A,"_zIndex",1),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(A,"SMALL_EDITOR_SIZE",0),t.AnnotationEditor=A;var w=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(a=_callSuper(this,t,[e])).annotationElementId=e.annotationElementId,a.deleted=!0,a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"serialize",value:function(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}])}(A)},function(e,t,a){var r;Object.defineProperty(t,"__esModule",{value:!0}),t.KeyboardManager=t.CommandManager=t.ColorManager=t.AnnotationEditorUIManager=void 0,t.bindEvents=function(e,t,a){var r,i=_createForOfIteratorHelper(a);try{for(i.s();!(r=i.n()).done;){var n=r.value;t.addEventListener(n,e[n].bind(e))}}catch(e){i.e(e)}finally{i.f()}},t.opacityToHex=function(e){return Math.round(Math.min(255,Math.max(1,255*e))).toString(16).padStart(2,"0")};var i=a(1),n=a(6),s=new WeakMap,l=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateFieldInitSpec(this,s,0)}),[{key:"getId",value:function(){var e,t;return"".concat(i.AnnotationEditorPrefix).concat((_classPrivateFieldSet(s,this,(e=_classPrivateFieldGet(s,this),t=e++,e)),t))}}])}(),o=new WeakMap,_=new WeakMap,c=new WeakMap,u=new WeakSet,h=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,u),_classPrivateFieldInitSpec(this,o,(0,i.getUuid)()),_classPrivateFieldInitSpec(this,_,0),_classPrivateFieldInitSpec(this,c,null)}),[{key:"getFromFile",value:(a=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t){var a,r,i,n;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=t.lastModified,r=t.name,i=t.size,n=t.type,e.abrupt("return",_assertClassBrand(u,this,d).call(this,"".concat(a,"_").concat(r,"_").concat(i,"_").concat(n),t));case 1:case"end":return e.stop()}}),e,this)}))),function(e){return a.apply(this,arguments)})},{key:"getFromUrl",value:(t=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t){return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",_assertClassBrand(u,this,d).call(this,t,t));case 1:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"getFromId",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t){var a;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(_classPrivateFieldGet(c,this)||_classPrivateFieldSet(c,this,new Map),a=_classPrivateFieldGet(c,this).get(t)){e.next=1;break}return e.abrupt("return",null);case 1:if(!a.bitmap){e.next=2;break}return a.refCounter+=1,e.abrupt("return",a);case 2:if(!a.file){e.next=3;break}return e.abrupt("return",this.getFromFile(a.file));case 3:return e.abrupt("return",this.getFromUrl(a.url));case 4:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"getSvgUrl",value:function(e){var t=_classPrivateFieldGet(c,this).get(e);return null!=t&&t.isSvg?t.svgUrl:null}},{key:"deleteId",value:function(e){_classPrivateFieldGet(c,this)||_classPrivateFieldSet(c,this,new Map);var t=_classPrivateFieldGet(c,this).get(e);t&&(t.refCounter-=1,0===t.refCounter&&(t.bitmap=null))}},{key:"isValidId",value:function(e){return e.startsWith("image_".concat(_classPrivateFieldGet(o,this),"_"))}}],[{key:"_isSVGFittingCanvas",get:function(){var e=new OffscreenCanvas(1,3).getContext("2d"),t=new Image;t.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';var a=t.decode().then((function(){return e.drawImage(t,0,0,1,1,0,0,1,3),0===new Uint32Array(e.getImageData(0,0,1,1).data.buffer)[0]}));return(0,i.shadow)(this,"_isSVGFittingCanvas",a)}}]);var e,t,a}();function d(e,t){return p.apply(this,arguments)}function p(){return(p=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t,a){var i,n,s,l,u,h,d,p,v,f,m;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(_classPrivateFieldGet(c,this)||_classPrivateFieldSet(c,this,new Map),null!==(n=_classPrivateFieldGet(c,this).get(t))){e.next=1;break}return e.abrupt("return",null);case 1:if(null===(i=n)||void 0===i||!i.bitmap){e.next=2;break}return n.refCounter+=1,e.abrupt("return",n);case 2:if(e.prev=2,n||(n={bitmap:null,id:"image_".concat(_classPrivateFieldGet(o,this),"_").concat((_classPrivateFieldSet(_,this,(s=_classPrivateFieldGet(_,this),l=s++,s)),l)),refCounter:0,isSvg:!1}),"string"!=typeof a){e.next=6;break}return n.url=a,e.next=3,fetch(a);case 3:if((h=e.sent).ok){e.next=4;break}throw new Error(h.statusText);case 4:return e.next=5,h.blob();case 5:u=e.sent,e.next=7;break;case 6:u=n.file=a;case 7:if("image/svg+xml"!==u.type){e.next=9;break}return d=r._isSVGFittingCanvas,p=new FileReader,v=new Image,f=new Promise((function(e,t){v.onload=function(){n.bitmap=v,n.isSvg=!0,e()},p.onload=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=n.svgUrl=p.result,e.next=1,d;case 1:if(!e.sent){e.next=2;break}a="".concat(t,"#svgView(preserveAspectRatio(none))"),e.next=3;break;case 2:a=t;case 3:v.src=a;case 4:case"end":return e.stop()}}),e)}))),v.onerror=p.onerror=t})),p.readAsDataURL(u),e.next=8,f;case 8:e.next=11;break;case 9:return e.next=10,createImageBitmap(u);case 10:n.bitmap=e.sent;case 11:n.refCounter=1,e.next=13;break;case 12:e.prev=12,m=e.catch(2),console.error(m),n=null;case 13:return _classPrivateFieldGet(c,this).set(t,n),n&&_classPrivateFieldGet(c,this).set(n.id,n),e.abrupt("return",n);case 14:case"end":return e.stop()}}),e,this,[[2,12]])})))).apply(this,arguments)}r=h;var v=new WeakMap,f=new WeakMap,m=new WeakMap,b=new WeakMap,g=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:128;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateFieldInitSpec(this,v,[]),_classPrivateFieldInitSpec(this,f,!1),_classPrivateFieldInitSpec(this,m,void 0),_classPrivateFieldInitSpec(this,b,-1),_classPrivateFieldSet(m,this,t)}),[{key:"add",value:function(e){var t=e.cmd,a=e.undo,r=e.mustExec,i=e.type,n=void 0===i?NaN:i,s=e.overwriteIfSameType,l=void 0!==s&&s,o=e.keepUndo,_=void 0!==o&&o;if(r&&t(),!_classPrivateFieldGet(f,this)){var c={cmd:t,undo:a,type:n};if(-1===_classPrivateFieldGet(b,this))return _classPrivateFieldGet(v,this).length>0&&(_classPrivateFieldGet(v,this).length=0),_classPrivateFieldSet(b,this,0),void _classPrivateFieldGet(v,this).push(c);if(l&&_classPrivateFieldGet(v,this)[_classPrivateFieldGet(b,this)].type===n)return _&&(c.undo=_classPrivateFieldGet(v,this)[_classPrivateFieldGet(b,this)].undo),void(_classPrivateFieldGet(v,this)[_classPrivateFieldGet(b,this)]=c);var u=_classPrivateFieldGet(b,this)+1;u===_classPrivateFieldGet(m,this)?_classPrivateFieldGet(v,this).splice(0,1):(_classPrivateFieldSet(b,this,u),u<_classPrivateFieldGet(v,this).length&&_classPrivateFieldGet(v,this).splice(u)),_classPrivateFieldGet(v,this).push(c)}}},{key:"undo",value:function(){-1!==_classPrivateFieldGet(b,this)&&(_classPrivateFieldSet(f,this,!0),_classPrivateFieldGet(v,this)[_classPrivateFieldGet(b,this)].undo(),_classPrivateFieldSet(f,this,!1),_classPrivateFieldSet(b,this,_classPrivateFieldGet(b,this)-1))}},{key:"redo",value:function(){_classPrivateFieldGet(b,this)<_classPrivateFieldGet(v,this).length-1&&(_classPrivateFieldSet(b,this,_classPrivateFieldGet(b,this)+1),_classPrivateFieldSet(f,this,!0),_classPrivateFieldGet(v,this)[_classPrivateFieldGet(b,this)].cmd(),_classPrivateFieldSet(f,this,!1))}},{key:"hasSomethingToUndo",value:function(){return-1!==_classPrivateFieldGet(b,this)}},{key:"hasSomethingToRedo",value:function(){return _classPrivateFieldGet(b,this)<_classPrivateFieldGet(v,this).length-1}},{key:"destroy",value:function(){_classPrivateFieldSet(v,this,null)}}])}();t.CommandManager=g;var P=new WeakSet,y=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,P),this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;var a,r=i.FeatureTest.platform.isMac,n=_createForOfIteratorHelper(t);try{for(n.s();!(a=n.n()).done;){var s,l=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(a.value,3),o=l[0],_=l[1],c=l[2],u=void 0===c?{}:c,h=_createForOfIteratorHelper(o);try{for(h.s();!(s=h.n()).done;){var d=s.value,p=d.startsWith("mac+");r&&p?(this.callbacks.set(d.slice(4),{callback:_,options:u}),this.allKeys.add(d.split("+").at(-1))):r||p||(this.callbacks.set(d,{callback:_,options:u}),this.allKeys.add(d.split("+").at(-1)))}}catch(e){h.e(e)}finally{h.f()}}}catch(e){n.e(e)}finally{n.f()}}),[{key:"exec",value:function(e,t){if(this.allKeys.has(t.key)){var a=this.callbacks.get(_assertClassBrand(P,this,A).call(this,t));if(a){var r=a.callback,i=a.options,n=i.bubbles,s=void 0!==n&&n,l=i.args,o=void 0===l?[]:l,_=i.checker,c=void 0===_?null:_;c&&!c(e,t)||(r.bind.apply(r,[e].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(o)))(),s||(t.stopPropagation(),t.preventDefault()))}}}}])}();function A(e){e.altKey&&this.buffer.push("alt"),e.ctrlKey&&this.buffer.push("ctrl"),e.metaKey&&this.buffer.push("meta"),e.shiftKey&&this.buffer.push("shift"),this.buffer.push(e.key);var t=this.buffer.join("+");return this.buffer.length=0,t}t.KeyboardManager=y;var E=function(){function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e)}return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(e,[{key:"_colors",get:function(){var e=new Map([["CanvasText",null],["Canvas",null]]);return(0,n.getColorValues)(e),(0,i.shadow)(this,"_colors",e)}},{key:"convert",value:function(t){var a=(0,n.getRGB)(t);if(!window.matchMedia("(forced-colors: active)").matches)return a;var r,i=_createForOfIteratorHelper(this._colors);try{for(i.s();!(r=i.n()).done;){var s=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(r.value,2),l=s[0];if(s[1].every((function(e,t){return e===a[t]})))return e._colorsMapping.get(l)}}catch(e){i.e(e)}finally{i.f()}return a}},{key:"getHexCode",value:function(e){var t,a=this._colors.get(e);return a?(t=i.Util).makeHexColor.apply(t,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(a)):e}}])}();(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(E,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]])),t.ColorManager=E;var C=new WeakMap,M=new WeakMap,k=new WeakMap,O=new WeakMap,D=new WeakMap,F=new WeakMap,S=new WeakMap,T=new WeakMap,I=new WeakMap,w=new WeakMap,x=new WeakMap,R=new WeakMap,L=new WeakMap,B=new WeakMap,W=new WeakMap,U=new WeakMap,G=new WeakMap,K=new WeakMap,N=new WeakMap,j=new WeakMap,H=new WeakMap,q=new WeakMap,z=new WeakMap,V=new WeakMap,X=new WeakMap,Y=new WeakMap,J=new WeakMap,Q=new WeakMap,Z=new WeakMap,$=new WeakMap,ee=new WeakMap,te=new WeakMap,ae=new WeakMap,re=new WeakMap,ie=new WeakSet,ne=function(){function e(t,a,r,s,o,_){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,ie),_classPrivateFieldInitSpec(this,C,null),_classPrivateFieldInitSpec(this,M,new Map),_classPrivateFieldInitSpec(this,k,new Map),_classPrivateFieldInitSpec(this,O,null),_classPrivateFieldInitSpec(this,D,null),_classPrivateFieldInitSpec(this,F,new g),_classPrivateFieldInitSpec(this,S,0),_classPrivateFieldInitSpec(this,T,new Set),_classPrivateFieldInitSpec(this,I,null),_classPrivateFieldInitSpec(this,w,null),_classPrivateFieldInitSpec(this,x,new Set),_classPrivateFieldInitSpec(this,R,null),_classPrivateFieldInitSpec(this,L,new l),_classPrivateFieldInitSpec(this,B,!1),_classPrivateFieldInitSpec(this,W,!1),_classPrivateFieldInitSpec(this,U,null),_classPrivateFieldInitSpec(this,G,i.AnnotationEditorType.NONE),_classPrivateFieldInitSpec(this,K,new Set),_classPrivateFieldInitSpec(this,N,null),_classPrivateFieldInitSpec(this,j,this.blur.bind(this)),_classPrivateFieldInitSpec(this,H,this.focus.bind(this)),_classPrivateFieldInitSpec(this,q,this.copy.bind(this)),_classPrivateFieldInitSpec(this,z,this.cut.bind(this)),_classPrivateFieldInitSpec(this,V,this.paste.bind(this)),_classPrivateFieldInitSpec(this,X,this.keydown.bind(this)),_classPrivateFieldInitSpec(this,Y,this.onEditingAction.bind(this)),_classPrivateFieldInitSpec(this,J,this.onPageChanging.bind(this)),_classPrivateFieldInitSpec(this,Q,this.onScaleChanging.bind(this)),_classPrivateFieldInitSpec(this,Z,this.onRotationChanging.bind(this)),_classPrivateFieldInitSpec(this,$,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1}),_classPrivateFieldInitSpec(this,ee,[0,0]),_classPrivateFieldInitSpec(this,te,null),_classPrivateFieldInitSpec(this,ae,null),_classPrivateFieldInitSpec(this,re,null),_classPrivateFieldSet(ae,this,t),_classPrivateFieldSet(re,this,a),_classPrivateFieldSet(O,this,r),this._eventBus=s,this._eventBus._on("editingaction",_classPrivateFieldGet(Y,this)),this._eventBus._on("pagechanging",_classPrivateFieldGet(J,this)),this._eventBus._on("scalechanging",_classPrivateFieldGet(Q,this)),this._eventBus._on("rotationchanging",_classPrivateFieldGet(Z,this)),_classPrivateFieldSet(D,this,o.annotationStorage),_classPrivateFieldSet(R,this,o.filterFactory),_classPrivateFieldSet(N,this,_),this.viewParameters={realScale:n.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0}}return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(e,[{key:"destroy",value:function(){_assertClassBrand(ie,this,_e).call(this),_assertClassBrand(ie,this,le).call(this),this._eventBus._off("editingaction",_classPrivateFieldGet(Y,this)),this._eventBus._off("pagechanging",_classPrivateFieldGet(J,this)),this._eventBus._off("scalechanging",_classPrivateFieldGet(Q,this)),this._eventBus._off("rotationchanging",_classPrivateFieldGet(Z,this));var e,t=_createForOfIteratorHelper(_classPrivateFieldGet(k,this).values());try{for(t.s();!(e=t.n()).done;)e.value.destroy()}catch(e){t.e(e)}finally{t.f()}_classPrivateFieldGet(k,this).clear(),_classPrivateFieldGet(M,this).clear(),_classPrivateFieldGet(x,this).clear(),_classPrivateFieldSet(C,this,null),_classPrivateFieldGet(K,this).clear(),_classPrivateFieldGet(F,this).destroy(),_classPrivateFieldGet(O,this).destroy()}},{key:"hcmFilter",get:function(){return(0,i.shadow)(this,"hcmFilter",_classPrivateFieldGet(N,this)?_classPrivateFieldGet(R,this).addHCMFilter(_classPrivateFieldGet(N,this).foreground,_classPrivateFieldGet(N,this).background):"none")}},{key:"direction",get:function(){return(0,i.shadow)(this,"direction",getComputedStyle(_classPrivateFieldGet(ae,this)).direction)}},{key:"editAltText",value:function(e){var t;null===(t=_classPrivateFieldGet(O,this))||void 0===t||t.editAltText(this,e)}},{key:"onPageChanging",value:function(e){var t=e.pageNumber;_classPrivateFieldSet(S,this,t-1)}},{key:"focusMainContainer",value:function(){_classPrivateFieldGet(ae,this).focus()}},{key:"findParent",value:function(e,t){var a,r=_createForOfIteratorHelper(_classPrivateFieldGet(k,this).values());try{for(r.s();!(a=r.n()).done;){var i=a.value,n=i.div.getBoundingClientRect(),s=n.x,l=n.y,o=n.width,_=n.height;if(e>=s&&e<=s+o&&t>=l&&t<=l+_)return i}}catch(e){r.e(e)}finally{r.f()}return null}},{key:"disableUserSelect",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];_classPrivateFieldGet(re,this).classList.toggle("noUserSelect",e)}},{key:"addShouldRescale",value:function(e){_classPrivateFieldGet(x,this).add(e)}},{key:"removeShouldRescale",value:function(e){_classPrivateFieldGet(x,this).delete(e)}},{key:"onScaleChanging",value:function(e){var t=e.scale;this.commitOrRemove(),this.viewParameters.realScale=t*n.PixelsPerInch.PDF_TO_CSS_UNITS;var a,r=_createForOfIteratorHelper(_classPrivateFieldGet(x,this));try{for(r.s();!(a=r.n()).done;)a.value.onScaleChanging()}catch(e){r.e(e)}finally{r.f()}}},{key:"onRotationChanging",value:function(e){var t=e.pagesRotation;this.commitOrRemove(),this.viewParameters.rotation=t}},{key:"addToAnnotationStorage",value:function(e){e.isEmpty()||!_classPrivateFieldGet(D,this)||_classPrivateFieldGet(D,this).has(e.id)||_classPrivateFieldGet(D,this).setValue(e.id,e)}},{key:"blur",value:function(){if(this.hasSelection){var e,t=document.activeElement,a=_createForOfIteratorHelper(_classPrivateFieldGet(K,this));try{for(a.s();!(e=a.n()).done;){var r=e.value;if(r.div.contains(t)){_classPrivateFieldSet(U,this,[r,t]),r._focusEventsAllowed=!1;break}}}catch(e){a.e(e)}finally{a.f()}}}},{key:"focus",value:function(){if(_classPrivateFieldGet(U,this)){var e=_classPrivateFieldGet(U,this),t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,2),a=t[0],r=t[1];_classPrivateFieldSet(U,this,null),r.addEventListener("focusin",(function(){a._focusEventsAllowed=!0}),{once:!0}),r.focus()}}},{key:"addEditListeners",value:function(){_assertClassBrand(ie,this,oe).call(this),_assertClassBrand(ie,this,ce).call(this)}},{key:"removeEditListeners",value:function(){_assertClassBrand(ie,this,_e).call(this),_assertClassBrand(ie,this,ue).call(this)}},{key:"copy",value:function(e){var t;if(e.preventDefault(),null===(t=_classPrivateFieldGet(C,this))||void 0===t||t.commitOrRemove(),this.hasSelection){var a,r=[],i=_createForOfIteratorHelper(_classPrivateFieldGet(K,this));try{for(i.s();!(a=i.n()).done;){var n=a.value.serialize(!0);n&&r.push(n)}}catch(e){i.e(e)}finally{i.f()}0!==r.length&&e.clipboardData.setData("application/pdfjs",JSON.stringify(r))}}},{key:"cut",value:function(e){this.copy(e),this.delete()}},{key:"paste",value:function(e){var t=this;e.preventDefault();var a,r=e.clipboardData,n=_createForOfIteratorHelper(r.items);try{for(n.s();!(a=n.n()).done;){var s,l=a.value,o=_createForOfIteratorHelper(_classPrivateFieldGet(w,this));try{for(o.s();!(s=o.n()).done;){var _=s.value;if(_.isHandlingMimeForPasting(l.type))return void _.paste(l,this.currentLayer)}}catch(e){o.e(e)}finally{o.f()}}}catch(e){n.e(e)}finally{n.f()}var c=r.getData("application/pdfjs");if(c){try{c=JSON.parse(c)}catch(e){return void(0,i.warn)('paste: "'.concat(e.message,'".'))}if(Array.isArray(c)){this.unselectAll();var u=this.currentLayer;try{var h,d=[],p=_createForOfIteratorHelper(c);try{for(p.s();!(h=p.n()).done;){var v=h.value,f=u.deserialize(v);if(!f)return;d.push(f)}}catch(e){p.e(e)}finally{p.f()}this.addCommands({cmd:function(){for(var e=0,a=d;e<a.length;e++){var r=a[e];_assertClassBrand(ie,t,fe).call(t,r)}_assertClassBrand(ie,t,be).call(t,d)},undo:function(){for(var e=0,t=d;e<t.length;e++)t[e].remove()},mustExec:!0})}catch(e){(0,i.warn)('paste: "'.concat(e.message,'".'))}}}}},{key:"keydown",value:function(t){var a;null!==(a=this.getActive())&&void 0!==a&&a.shouldGetKeyboardEvents()||e._keyboardManager.exec(this,t)}},{key:"onEditingAction",value:function(e){["undo","redo","delete","selectAll"].includes(e.name)&&this[e.name]()}},{key:"setEditingState",value:function(e){e?(_assertClassBrand(ie,this,se).call(this),_assertClassBrand(ie,this,oe).call(this),_assertClassBrand(ie,this,ce).call(this),_assertClassBrand(ie,this,he).call(this,{isEditing:_classPrivateFieldGet(G,this)!==i.AnnotationEditorType.NONE,isEmpty:_assertClassBrand(ie,this,me).call(this),hasSomethingToUndo:_classPrivateFieldGet(F,this).hasSomethingToUndo(),hasSomethingToRedo:_classPrivateFieldGet(F,this).hasSomethingToRedo(),hasSelectedEditor:!1})):(_assertClassBrand(ie,this,le).call(this),_assertClassBrand(ie,this,_e).call(this),_assertClassBrand(ie,this,ue).call(this),_assertClassBrand(ie,this,he).call(this,{isEditing:!1}),this.disableUserSelect(!1))}},{key:"registerEditorTypes",value:function(e){if(!_classPrivateFieldGet(w,this)){_classPrivateFieldSet(w,this,e);var t,a=_createForOfIteratorHelper(_classPrivateFieldGet(w,this));try{for(a.s();!(t=a.n()).done;){var r=t.value;_assertClassBrand(ie,this,de).call(this,r.defaultPropertiesToUpdate)}}catch(e){a.e(e)}finally{a.f()}}}},{key:"getId",value:function(){return _classPrivateFieldGet(L,this).getId()}},{key:"currentLayer",get:function(){return _classPrivateFieldGet(k,this).get(_classPrivateFieldGet(S,this))}},{key:"getLayer",value:function(e){return _classPrivateFieldGet(k,this).get(e)}},{key:"currentPageIndex",get:function(){return _classPrivateFieldGet(S,this)}},{key:"addLayer",value:function(e){_classPrivateFieldGet(k,this).set(e.pageIndex,e),_classPrivateFieldGet(B,this)?e.enable():e.disable()}},{key:"removeLayer",value:function(e){_classPrivateFieldGet(k,this).delete(e.pageIndex)}},{key:"updateMode",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(_classPrivateFieldGet(G,this)!==e){if(_classPrivateFieldSet(G,this,e),e===i.AnnotationEditorType.NONE)return this.setEditingState(!1),void _assertClassBrand(ie,this,ve).call(this);this.setEditingState(!0),_assertClassBrand(ie,this,pe).call(this),this.unselectAll();var a,r=_createForOfIteratorHelper(_classPrivateFieldGet(k,this).values());try{for(r.s();!(a=r.n()).done;)a.value.updateMode(e)}catch(e){r.e(e)}finally{r.f()}if(t){var n,s=_createForOfIteratorHelper(_classPrivateFieldGet(M,this).values());try{for(s.s();!(n=s.n()).done;){var l=n.value;if(l.annotationElementId===t){this.setSelected(l),l.enterInEditMode();break}}}catch(e){s.e(e)}finally{s.f()}}}}},{key:"updateToolbar",value:function(e){e!==_classPrivateFieldGet(G,this)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:e})}},{key:"updateParams",value:function(e,t){if(_classPrivateFieldGet(w,this))if(e!==i.AnnotationEditorParamsType.CREATE){var a,r=_createForOfIteratorHelper(_classPrivateFieldGet(K,this));try{for(r.s();!(a=r.n()).done;)a.value.updateParams(e,t)}catch(e){r.e(e)}finally{r.f()}var n,s=_createForOfIteratorHelper(_classPrivateFieldGet(w,this));try{for(s.s();!(n=s.n()).done;)n.value.updateDefaultParams(e,t)}catch(e){s.e(e)}finally{s.f()}}else this.currentLayer.addNewEditor(e)}},{key:"enableWaiting",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(_classPrivateFieldGet(W,this)!==e){_classPrivateFieldSet(W,this,e);var t,a=_createForOfIteratorHelper(_classPrivateFieldGet(k,this).values());try{for(a.s();!(t=a.n()).done;){var r=t.value;e?r.disableClick():r.enableClick(),r.div.classList.toggle("waiting",e)}}catch(e){a.e(e)}finally{a.f()}}}},{key:"getEditors",value:function(e){var t,a=[],r=_createForOfIteratorHelper(_classPrivateFieldGet(M,this).values());try{for(r.s();!(t=r.n()).done;){var i=t.value;i.pageIndex===e&&a.push(i)}}catch(e){r.e(e)}finally{r.f()}return a}},{key:"getEditor",value:function(e){return _classPrivateFieldGet(M,this).get(e)}},{key:"addEditor",value:function(e){_classPrivateFieldGet(M,this).set(e.id,e)}},{key:"removeEditor",value:function(e){var t;_classPrivateFieldGet(M,this).delete(e.id),this.unselect(e),e.annotationElementId&&_classPrivateFieldGet(T,this).has(e.annotationElementId)||null===(t=_classPrivateFieldGet(D,this))||void 0===t||t.remove(e.id)}},{key:"addDeletedAnnotationElement",value:function(e){_classPrivateFieldGet(T,this).add(e.annotationElementId),e.deleted=!0}},{key:"isDeletedAnnotationElement",value:function(e){return _classPrivateFieldGet(T,this).has(e)}},{key:"removeDeletedAnnotationElement",value:function(e){_classPrivateFieldGet(T,this).delete(e.annotationElementId),e.deleted=!1}},{key:"setActiveEditor",value:function(e){_classPrivateFieldGet(C,this)!==e&&(_classPrivateFieldSet(C,this,e),e&&_assertClassBrand(ie,this,de).call(this,e.propertiesToUpdate))}},{key:"toggleSelected",value:function(e){if(_classPrivateFieldGet(K,this).has(e))return _classPrivateFieldGet(K,this).delete(e),e.unselect(),void _assertClassBrand(ie,this,he).call(this,{hasSelectedEditor:this.hasSelection});_classPrivateFieldGet(K,this).add(e),e.select(),_assertClassBrand(ie,this,de).call(this,e.propertiesToUpdate),_assertClassBrand(ie,this,he).call(this,{hasSelectedEditor:!0})}},{key:"setSelected",value:function(e){var t,a=_createForOfIteratorHelper(_classPrivateFieldGet(K,this));try{for(a.s();!(t=a.n()).done;){var r=t.value;r!==e&&r.unselect()}}catch(e){a.e(e)}finally{a.f()}_classPrivateFieldGet(K,this).clear(),_classPrivateFieldGet(K,this).add(e),e.select(),_assertClassBrand(ie,this,de).call(this,e.propertiesToUpdate),_assertClassBrand(ie,this,he).call(this,{hasSelectedEditor:!0})}},{key:"isSelected",value:function(e){return _classPrivateFieldGet(K,this).has(e)}},{key:"unselect",value:function(e){e.unselect(),_classPrivateFieldGet(K,this).delete(e),_assertClassBrand(ie,this,he).call(this,{hasSelectedEditor:this.hasSelection})}},{key:"hasSelection",get:function(){return 0!==_classPrivateFieldGet(K,this).size}},{key:"undo",value:function(){_classPrivateFieldGet(F,this).undo(),_assertClassBrand(ie,this,he).call(this,{hasSomethingToUndo:_classPrivateFieldGet(F,this).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:_assertClassBrand(ie,this,me).call(this)})}},{key:"redo",value:function(){_classPrivateFieldGet(F,this).redo(),_assertClassBrand(ie,this,he).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:_classPrivateFieldGet(F,this).hasSomethingToRedo(),isEmpty:_assertClassBrand(ie,this,me).call(this)})}},{key:"addCommands",value:function(e){_classPrivateFieldGet(F,this).add(e),_assertClassBrand(ie,this,he).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:_assertClassBrand(ie,this,me).call(this)})}},{key:"delete",value:function(){var e=this;if(this.commitOrRemove(),this.hasSelection){var t=(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(_classPrivateFieldGet(K,this));this.addCommands({cmd:function(){var e,a=_createForOfIteratorHelper(t);try{for(a.s();!(e=a.n()).done;)e.value.remove()}catch(e){a.e(e)}finally{a.f()}},undo:function(){var a,r=_createForOfIteratorHelper(t);try{for(r.s();!(a=r.n()).done;){var i=a.value;_assertClassBrand(ie,e,fe).call(e,i)}}catch(e){r.e(e)}finally{r.f()}},mustExec:!0})}}},{key:"commitOrRemove",value:function(){var e;null===(e=_classPrivateFieldGet(C,this))||void 0===e||e.commitOrRemove()}},{key:"hasSomethingToControl",value:function(){return _classPrivateFieldGet(C,this)||this.hasSelection}},{key:"selectAll",value:function(){var e,t=_createForOfIteratorHelper(_classPrivateFieldGet(K,this));try{for(t.s();!(e=t.n()).done;)e.value.commit()}catch(e){t.e(e)}finally{t.f()}_assertClassBrand(ie,this,be).call(this,_classPrivateFieldGet(M,this).values())}},{key:"unselectAll",value:function(){if(_classPrivateFieldGet(C,this))_classPrivateFieldGet(C,this).commitOrRemove();else if(this.hasSelection){var e,t=_createForOfIteratorHelper(_classPrivateFieldGet(K,this));try{for(t.s();!(e=t.n()).done;)e.value.unselect()}catch(e){t.e(e)}finally{t.f()}_classPrivateFieldGet(K,this).clear(),_assertClassBrand(ie,this,he).call(this,{hasSelectedEditor:!1})}}},{key:"translateSelectedEditors",value:function(e,t){var a=this;if(arguments.length>2&&void 0!==arguments[2]&&arguments[2]||this.commitOrRemove(),this.hasSelection){_classPrivateFieldGet(ee,this)[0]+=e,_classPrivateFieldGet(ee,this)[1]+=t;var r=_classPrivateFieldGet(ee,this),i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(r,2),n=i[0],s=i[1],l=(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(_classPrivateFieldGet(K,this));_classPrivateFieldGet(te,this)&&clearTimeout(_classPrivateFieldGet(te,this)),_classPrivateFieldSet(te,this,setTimeout((function(){_classPrivateFieldSet(te,a,null),_classPrivateFieldGet(ee,a)[0]=_classPrivateFieldGet(ee,a)[1]=0,a.addCommands({cmd:function(){var e,t=_createForOfIteratorHelper(l);try{for(t.s();!(e=t.n()).done;){var r=e.value;_classPrivateFieldGet(M,a).has(r.id)&&r.translateInPage(n,s)}}catch(e){t.e(e)}finally{t.f()}},undo:function(){var e,t=_createForOfIteratorHelper(l);try{for(t.s();!(e=t.n()).done;){var r=e.value;_classPrivateFieldGet(M,a).has(r.id)&&r.translateInPage(-n,-s)}}catch(e){t.e(e)}finally{t.f()}},mustExec:!1})}),1e3));var o,_=_createForOfIteratorHelper(l);try{for(_.s();!(o=_.n()).done;)o.value.translateInPage(e,t)}catch(e){_.e(e)}finally{_.f()}}}},{key:"setUpDragSession",value:function(){if(this.hasSelection){this.disableUserSelect(!0),_classPrivateFieldSet(I,this,new Map);var e,t=_createForOfIteratorHelper(_classPrivateFieldGet(K,this));try{for(t.s();!(e=t.n()).done;){var a=e.value;_classPrivateFieldGet(I,this).set(a,{savedX:a.x,savedY:a.y,savedPageIndex:a.pageIndex,newX:0,newY:0,newPageIndex:-1})}}catch(e){t.e(e)}finally{t.f()}}}},{key:"endDragSession",value:function(){var e=this;if(!_classPrivateFieldGet(I,this))return!1;this.disableUserSelect(!1);var t=_classPrivateFieldGet(I,this);_classPrivateFieldSet(I,this,null);var a,r=!1,i=_createForOfIteratorHelper(t);try{for(i.s();!(a=i.n()).done;){var n=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(a.value,2),s=n[0],l=s.x,o=s.y,_=s.pageIndex,c=n[1];c.newX=l,c.newY=o,c.newPageIndex=_,r||(r=l!==c.savedX||o!==c.savedY||_!==c.savedPageIndex)}}catch(e){i.e(e)}finally{i.f()}if(!r)return!1;var u=function(t,a,r,i){if(_classPrivateFieldGet(M,e).has(t.id)){var n=_classPrivateFieldGet(k,e).get(i);n?t._setParentAndPosition(n,a,r):(t.pageIndex=i,t.x=a,t.y=r)}};return this.addCommands({cmd:function(){var e,a=_createForOfIteratorHelper(t);try{for(a.s();!(e=a.n()).done;){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e.value,2),i=r[0],n=r[1],s=n.newX,l=n.newY,o=n.newPageIndex;u(i,s,l,o)}}catch(e){a.e(e)}finally{a.f()}},undo:function(){var e,a=_createForOfIteratorHelper(t);try{for(a.s();!(e=a.n()).done;){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e.value,2),i=r[0],n=r[1],s=n.savedX,l=n.savedY,o=n.savedPageIndex;u(i,s,l,o)}}catch(e){a.e(e)}finally{a.f()}},mustExec:!0}),!0}},{key:"dragSelectedEditors",value:function(e,t){if(_classPrivateFieldGet(I,this)){var a,r=_createForOfIteratorHelper(_classPrivateFieldGet(I,this).keys());try{for(r.s();!(a=r.n()).done;)a.value.drag(e,t)}catch(e){r.e(e)}finally{r.f()}}}},{key:"rebuild",value:function(e){if(null===e.parent){var t=this.getLayer(e.pageIndex);t?(t.changeParent(e),t.addOrRebuild(e)):(this.addEditor(e),this.addToAnnotationStorage(e),e.rebuild())}else e.parent.addOrRebuild(e)}},{key:"isActive",value:function(e){return _classPrivateFieldGet(C,this)===e}},{key:"getActive",value:function(){return _classPrivateFieldGet(C,this)}},{key:"getMode",value:function(){return _classPrivateFieldGet(G,this)}},{key:"imageManager",get:function(){return(0,i.shadow)(this,"imageManager",new h)}}],[{key:"_keyboardManager",get:function(){var t=e.prototype,a=function(e){var t=document.activeElement;return t&&_classPrivateFieldGet(ae,e).contains(t)&&e.hasSomethingToControl()},r=this.TRANSLATE_SMALL,n=this.TRANSLATE_BIG;return(0,i.shadow)(this,"_keyboardManager",new y([[["ctrl+a","mac+meta+a"],t.selectAll],[["ctrl+z","mac+meta+z"],t.undo],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-r,0],checker:a}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-n,0],checker:a}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[r,0],checker:a}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[n,0],checker:a}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-r],checker:a}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-n],checker:a}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,r],checker:a}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,n],checker:a}]]))}}])}();function se(){window.addEventListener("focus",_classPrivateFieldGet(H,this)),window.addEventListener("blur",_classPrivateFieldGet(j,this))}function le(){window.removeEventListener("focus",_classPrivateFieldGet(H,this)),window.removeEventListener("blur",_classPrivateFieldGet(j,this))}function oe(){window.addEventListener("keydown",_classPrivateFieldGet(X,this),{capture:!0})}function _e(){window.removeEventListener("keydown",_classPrivateFieldGet(X,this),{capture:!0})}function ce(){document.addEventListener("copy",_classPrivateFieldGet(q,this)),document.addEventListener("cut",_classPrivateFieldGet(z,this)),document.addEventListener("paste",_classPrivateFieldGet(V,this))}function ue(){document.removeEventListener("copy",_classPrivateFieldGet(q,this)),document.removeEventListener("cut",_classPrivateFieldGet(z,this)),document.removeEventListener("paste",_classPrivateFieldGet(V,this))}function he(e){var t=this;Object.entries(e).some((function(e){var a=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,2),r=a[0],i=a[1];return _classPrivateFieldGet($,t)[r]!==i}))&&this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(_classPrivateFieldGet($,this),e)})}function de(e){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:e})}function pe(){if(!_classPrivateFieldGet(B,this)){_classPrivateFieldSet(B,this,!0);var e,t=_createForOfIteratorHelper(_classPrivateFieldGet(k,this).values());try{for(t.s();!(e=t.n()).done;)e.value.enable()}catch(e){t.e(e)}finally{t.f()}}}function ve(){if(this.unselectAll(),_classPrivateFieldGet(B,this)){_classPrivateFieldSet(B,this,!1);var e,t=_createForOfIteratorHelper(_classPrivateFieldGet(k,this).values());try{for(t.s();!(e=t.n()).done;)e.value.disable()}catch(e){t.e(e)}finally{t.f()}}}function fe(e){var t=_classPrivateFieldGet(k,this).get(e.pageIndex);t?t.addOrRebuild(e):this.addEditor(e)}function me(){if(0===_classPrivateFieldGet(M,this).size)return!0;if(1===_classPrivateFieldGet(M,this).size){var e,t=_createForOfIteratorHelper(_classPrivateFieldGet(M,this).values());try{for(t.s();!(e=t.n()).done;)return e.value.isEmpty()}catch(e){t.e(e)}finally{t.f()}}return!1}function be(e){_classPrivateFieldGet(K,this).clear();var t,a=_createForOfIteratorHelper(e);try{for(a.s();!(t=a.n()).done;){var r=t.value;r.isEmpty()||(_classPrivateFieldGet(K,this).add(r),r.select())}}catch(e){a.e(e)}finally{a.f()}_assertClassBrand(ie,this,he).call(this,{hasSelectedEditor:!0})}(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(ne,"TRANSLATE_SMALL",1),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(ne,"TRANSLATE_BIG",10),t.AnnotationEditorUIManager=ne},function(e,t,a){var r;Object.defineProperty(t,"__esModule",{value:!0}),t.StatTimer=t.RenderingCancelledException=t.PixelsPerInch=t.PageViewport=t.PDFDateString=t.DOMStandardFontDataFactory=t.DOMSVGFactory=t.DOMFilterFactory=t.DOMCanvasFactory=t.DOMCMapReaderFactory=void 0,t.deprecated=function(e){console.log("Deprecated API usage: "+e)},t.getColorValues=function(e){var t=document.createElement("span");t.style.visibility="hidden",document.body.append(t);var a,r=_createForOfIteratorHelper(e.keys());try{for(r.s();!(a=r.n()).done;){var i=a.value;t.style.color=i;var n=window.getComputedStyle(t).color;e.set(i,K(n))}}catch(e){r.e(e)}finally{r.f()}t.remove()},t.getCurrentTransform=function(e){var t=e.getTransform();return[t.a,t.b,t.c,t.d,t.e,t.f]},t.getCurrentTransformInverse=function(e){var t=e.getTransform().invertSelf();return[t.a,t.b,t.c,t.d,t.e,t.f]},t.getFilenameFromUrl=function(e){if(!(arguments.length>1&&void 0!==arguments[1]&&arguments[1])){var t=e.split(/[#?]/,1);e=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(t,1)[0]}return e.substring(e.lastIndexOf("/")+1)},t.getPdfFilenameFromUrl=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"document.pdf";if("string"!=typeof e)return t;if(L(e))return(0,n.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),t;var a=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,r=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(e),i=a.exec(r[1])||a.exec(r[2])||a.exec(r[3]);if(i&&(i=i[0]).includes("%"))try{i=a.exec(decodeURIComponent(i))[0]}catch(e){}return i||t},t.getRGB=K,t.getXfaPageViewport=function(e,t){var a=t.scale,r=void 0===a?1:a,i=t.rotation,n=void 0===i?0:i,s=e.attributes.style,l=s.width,o=s.height,_=[0,0,parseInt(l),parseInt(o)];return new x({viewBox:_,scale:r,rotation:n})},t.isDataScheme=L,t.isPdfFile=function(e){return"string"==typeof e&&/\.pdf$/i.test(e)},t.isValidFetchUrl=U,t.loadScript=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(a,r){var i=document.createElement("script");i.src=e,i.onload=function(e){t&&i.remove(),a(e)},i.onerror=function(){r(new Error("Cannot load script at: ".concat(i.src)))},(document.head||document.documentElement).append(i)}))},t.noContextMenu=function(e){e.preventDefault()},t.setLayerDimensions=function(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(t instanceof x){var i=t.rawDims,s=i.pageWidth,l=i.pageHeight,o=e.style,_=n.FeatureTest.isCSSRoundSupported,c="var(--scale-factor) * ".concat(s,"px"),u="var(--scale-factor) * ".concat(l,"px"),h=_?"round(".concat(c,", 1px)"):"calc(".concat(c,")"),d=_?"round(".concat(u,", 1px)"):"calc(".concat(u,")");a&&t.rotation%180!=0?(o.width=d,o.height=h):(o.width=h,o.height=d)}r&&e.setAttribute("data-main-rotation",t.rotation)};var i=a(7),n=a(1),s="http://www.w3.org/2000/svg",l=(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e)}));r=l,(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(l,"CSS",96),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(l,"PDF",72),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(l,"PDF_TO_CSS_UNITS",r.CSS/r.PDF),t.PixelsPerInch=l;var o=new WeakMap,_=new WeakMap,c=new WeakMap,u=new WeakMap,h=new WeakMap,d=new WeakMap,p=new WeakMap,v=new WeakMap,f=new WeakMap,m=new WeakMap,b=new WeakMap,g=new WeakSet,P=function(e){function t(){var e,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=a.docId,i=a.ownerDocument,n=void 0===i?globalThis.document:i;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateMethodInitSpec(e=_callSuper(this,t),g),_classPrivateFieldInitSpec(e,o,void 0),_classPrivateFieldInitSpec(e,_,void 0),_classPrivateFieldInitSpec(e,c,void 0),_classPrivateFieldInitSpec(e,u,void 0),_classPrivateFieldInitSpec(e,h,void 0),_classPrivateFieldInitSpec(e,d,void 0),_classPrivateFieldInitSpec(e,p,void 0),_classPrivateFieldInitSpec(e,v,void 0),_classPrivateFieldInitSpec(e,f,void 0),_classPrivateFieldInitSpec(e,m,void 0),_classPrivateFieldInitSpec(e,b,0),_classPrivateFieldSet(c,e,r),_classPrivateFieldSet(u,e,n),e}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"addFilter",value:function(e){var t,a;if(!e)return"none";var r,i,n,s,l=_classPrivateGetter(g,this,y).get(e);if(l)return l;if(1===e.length){for(var o=e[0],_=new Array(256),u=0;u<256;u++)_[u]=o[u]/255;s=r=i=n=_.join(",")}else{for(var h=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,3),d=h[0],p=h[1],v=h[2],f=new Array(256),m=new Array(256),P=new Array(256),A=0;A<256;A++)f[A]=d[A]/255,m[A]=p[A]/255,P[A]=v[A]/255;r=f.join(","),i=m.join(","),n=P.join(","),s="".concat(r).concat(i).concat(n)}if(l=_classPrivateGetter(g,this,y).get(s))return _classPrivateGetter(g,this,y).set(e,l),l;var E="g_".concat(_classPrivateFieldGet(c,this),"_transfer_map_").concat((_classPrivateFieldSet(b,this,(t=_classPrivateFieldGet(b,this),a=t++,t)),a)),M="url(#".concat(E,")");_classPrivateGetter(g,this,y).set(e,M),_classPrivateGetter(g,this,y).set(s,M);var O=_assertClassBrand(g,this,C).call(this,E);return _assertClassBrand(g,this,k).call(this,r,i,n,O),M}},{key:"addHCMFilter",value:function(e,t){var a,r,i,s="".concat(e,"-").concat(t);if(_classPrivateFieldGet(d,this)===s)return _classPrivateFieldGet(p,this);if(_classPrivateFieldSet(d,this,s),_classPrivateFieldSet(p,this,"none"),null===(a=_classPrivateFieldGet(h,this))||void 0===a||a.remove(),!e||!t)return _classPrivateFieldGet(p,this);var l=_assertClassBrand(g,this,O).call(this,e);e=(r=n.Util).makeHexColor.apply(r,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(l));var o=_assertClassBrand(g,this,O).call(this,t);if(t=(i=n.Util).makeHexColor.apply(i,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(o)),_classPrivateGetter(g,this,A).style.color="","#000000"===e&&"#ffffff"===t||e===t)return _classPrivateFieldGet(p,this);for(var _=new Array(256),u=0;u<=255;u++){var f=u/255;_[u]=f<=.03928?f/12.92:Math.pow((f+.055)/1.055,2.4)}var m=_.join(","),b="g_".concat(_classPrivateFieldGet(c,this),"_hcm_filter"),P=_classPrivateFieldSet(v,this,_assertClassBrand(g,this,C).call(this,b));_assertClassBrand(g,this,k).call(this,m,m,m,P),_assertClassBrand(g,this,E).call(this,P);var y=function(e,t){for(var a=l[e]/255,r=o[e]/255,i=new Array(t+1),n=0;n<=t;n++)i[n]=a+n/t*(r-a);return i.join(",")};return _assertClassBrand(g,this,k).call(this,y(0,5),y(1,5),y(2,5),P),_classPrivateFieldSet(p,this,"url(#".concat(b,")")),_classPrivateFieldGet(p,this)}},{key:"addHighlightHCMFilter",value:function(e,t,a,r){var i,n="".concat(e,"-").concat(t,"-").concat(a,"-").concat(r);if(_classPrivateFieldGet(f,this)===n)return _classPrivateFieldGet(m,this);if(_classPrivateFieldSet(f,this,n),_classPrivateFieldSet(m,this,"none"),null===(i=_classPrivateFieldGet(v,this))||void 0===i||i.remove(),!e||!t)return _classPrivateFieldGet(m,this);var s=[e,t].map(_assertClassBrand(g,this,O).bind(this)),l=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(s,2),o=l[0],_=l[1],u=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),h=Math.round(.2126*_[0]+.7152*_[1]+.0722*_[2]),d=[a,r].map(_assertClassBrand(g,this,O).bind(this)),p=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(d,2),b=p[0],P=p[1];if(h<u){var y=[h,u,P,b];u=y[0],h=y[1],b=y[2],P=y[3]}_classPrivateGetter(g,this,A).style.color="";var M=function(e,t,a){for(var r=new Array(256),i=(h-u)/a,n=e/255,s=(t-e)/(255*a),l=0,o=0;o<=a;o++){for(var _=Math.round(u+o*i),c=n+o*s,d=l;d<=_;d++)r[d]=c;l=_+1}for(var p=l;p<256;p++)r[p]=r[l-1];return r.join(",")},D="g_".concat(_classPrivateFieldGet(c,this),"_hcm_highlight_filter"),F=_classPrivateFieldSet(v,this,_assertClassBrand(g,this,C).call(this,D));return _assertClassBrand(g,this,E).call(this,F),_assertClassBrand(g,this,k).call(this,M(b[0],P[0],5),M(b[1],P[1],5),M(b[2],P[2],5),F),_classPrivateFieldSet(m,this,"url(#".concat(D,")")),_classPrivateFieldGet(m,this)}},{key:"destroy",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&(_classPrivateFieldGet(p,this)||_classPrivateFieldGet(m,this))||(_classPrivateFieldGet(_,this)&&(_classPrivateFieldGet(_,this).parentNode.parentNode.remove(),_classPrivateFieldSet(_,this,null)),_classPrivateFieldGet(o,this)&&(_classPrivateFieldGet(o,this).clear(),_classPrivateFieldSet(o,this,null)),_classPrivateFieldSet(b,this,0))}}])}(i.BaseFilterFactory);function y(e){return _classPrivateFieldGet(o,e)||_classPrivateFieldSet(o,e,new Map)}function A(e){if(!_classPrivateFieldGet(_,e)){var t=_classPrivateFieldGet(u,e).createElement("div"),a=t.style;a.visibility="hidden",a.contain="strict",a.width=a.height=0,a.position="absolute",a.top=a.left=0,a.zIndex=-1;var r=_classPrivateFieldGet(u,e).createElementNS(s,"svg");r.setAttribute("width",0),r.setAttribute("height",0),_classPrivateFieldSet(_,e,_classPrivateFieldGet(u,e).createElementNS(s,"defs")),t.append(r),r.append(_classPrivateFieldGet(_,e)),_classPrivateFieldGet(u,e).body.append(t)}return _classPrivateFieldGet(_,e)}function E(e){var t=_classPrivateFieldGet(u,this).createElementNS(s,"feColorMatrix");t.setAttribute("type","matrix"),t.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),e.append(t)}function C(e){var t=_classPrivateFieldGet(u,this).createElementNS(s,"filter");return t.setAttribute("color-interpolation-filters","sRGB"),t.setAttribute("id",e),_classPrivateGetter(g,this,A).append(t),t}function M(e,t,a){var r=_classPrivateFieldGet(u,this).createElementNS(s,t);r.setAttribute("type","discrete"),r.setAttribute("tableValues",a),e.append(r)}function k(e,t,a,r){var i=_classPrivateFieldGet(u,this).createElementNS(s,"feComponentTransfer");r.append(i),_assertClassBrand(g,this,M).call(this,i,"feFuncR",e),_assertClassBrand(g,this,M).call(this,i,"feFuncG",t),_assertClassBrand(g,this,M).call(this,i,"feFuncB",a)}function O(e){return _classPrivateGetter(g,this,A).style.color=e,K(getComputedStyle(_classPrivateGetter(g,this,A)).getPropertyValue("color"))}t.DOMFilterFactory=P;var D=function(e){function t(){var e,a=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).ownerDocument,r=void 0===a?globalThis.document:a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(e=_callSuper(this,t))._document=r,e}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"_createCanvas",value:function(e,t){var a=this._document.createElement("canvas");return a.width=e,a.height=t,a}}])}(i.BaseCanvasFactory);function F(e){return S.apply(this,arguments)}function S(){return S=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t){var a,r,i,s,l,o,_,c=arguments;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=c.length>1&&void 0!==c[1]&&c[1],!U(t,document.baseURI)){e.next=7;break}return e.next=1,fetch(t);case 1:if((r=e.sent).ok){e.next=2;break}throw new Error(r.statusText);case 2:if(!a){e.next=4;break}return s=Uint8Array,e.next=3,r.arrayBuffer();case 3:l=e.sent,i=new s(l),e.next=6;break;case 4:return o=n.stringToBytes,e.next=5,r.text();case 5:_=e.sent,i=o(_);case 6:return e.abrupt("return",i);case 7:return e.abrupt("return",new Promise((function(e,r){var i=new XMLHttpRequest;i.open("GET",t,!0),a&&(i.responseType="arraybuffer"),i.onreadystatechange=function(){if(i.readyState===XMLHttpRequest.DONE){var t;if((200===i.status||0===i.status)&&(a&&i.response?t=new Uint8Array(i.response):!a&&i.responseText&&(t=(0,n.stringToBytes)(i.responseText)),t))return void e(t);r(new Error(i.statusText))}},i.send(null)})));case 8:case"end":return e.stop()}}),e)}))),S.apply(this,arguments)}t.DOMCanvasFactory=D;var T=function(e){function t(){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,arguments)}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"_fetchData",value:function(e,t){return F(e,this.isCompressed).then((function(e){return{cMapData:e,compressionType:t}}))}}])}(i.BaseCMapReaderFactory);t.DOMCMapReaderFactory=T;var I=function(e){function t(){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,arguments)}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"_fetchData",value:function(e){return F(e,!0)}}])}(i.BaseStandardFontDataFactory);t.DOMStandardFontDataFactory=I;var w=function(e){function t(){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,arguments)}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"_createSVG",value:function(e){return document.createElementNS(s,e)}}])}(i.BaseSVGFactory);t.DOMSVGFactory=w;var x=function(){function e(t){var a=t.viewBox,r=t.scale,i=t.rotation,n=t.offsetX,s=void 0===n?0:n,l=t.offsetY,o=void 0===l?0:l,_=t.dontFlip,c=void 0!==_&&_;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.viewBox=a,this.scale=r,this.rotation=i,this.offsetX=s,this.offsetY=o;var u,h,d,p,v,f,m,b,g=(a[2]+a[0])/2,P=(a[3]+a[1])/2;switch((i%=360)<0&&(i+=360),i){case 180:u=-1,h=0,d=0,p=1;break;case 90:u=0,h=1,d=1,p=0;break;case 270:u=0,h=-1,d=-1,p=0;break;case 0:u=1,h=0,d=0,p=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}c&&(d=-d,p=-p),0===u?(v=Math.abs(P-a[1])*r+s,f=Math.abs(g-a[0])*r+o,m=(a[3]-a[1])*r,b=(a[2]-a[0])*r):(v=Math.abs(g-a[0])*r+s,f=Math.abs(P-a[1])*r+o,m=(a[2]-a[0])*r,b=(a[3]-a[1])*r),this.transform=[u*r,h*r,d*r,p*r,v-u*r*g-d*r*P,f-h*r*g-p*r*P],this.width=m,this.height=b}return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(e,[{key:"rawDims",get:function(){var e=this.viewBox;return(0,n.shadow)(this,"rawDims",{pageWidth:e[2]-e[0],pageHeight:e[3]-e[1],pageX:e[0],pageY:e[1]})}},{key:"clone",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.scale,r=void 0===a?this.scale:a,i=t.rotation,n=void 0===i?this.rotation:i,s=t.offsetX,l=void 0===s?this.offsetX:s,o=t.offsetY,_=void 0===o?this.offsetY:o,c=t.dontFlip,u=void 0!==c&&c;return new e({viewBox:this.viewBox.slice(),scale:r,rotation:n,offsetX:l,offsetY:_,dontFlip:u})}},{key:"convertToViewportPoint",value:function(e,t){return n.Util.applyTransform([e,t],this.transform)}},{key:"convertToViewportRectangle",value:function(e){var t=n.Util.applyTransform([e[0],e[1]],this.transform),a=n.Util.applyTransform([e[2],e[3]],this.transform);return[t[0],t[1],a[0],a[1]]}},{key:"convertToPdfPoint",value:function(e,t){return n.Util.applyInverseTransform([e,t],this.transform)}}])}();t.PageViewport=x;var R=function(e){function t(e){var a,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(a=_callSuper(this,t,[e,"RenderingCancelledException"])).extraDelay=r,a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(n.BaseException);function L(e){for(var t=e.length,a=0;a<t&&""===e[a].trim();)a++;return"data:"===e.substring(a,a+5).toLowerCase()}t.RenderingCancelledException=R;var B,W=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(this,"started",Object.create(null)),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(this,"times",[])}),[{key:"time",value:function(e){e in this.started&&(0,n.warn)("Timer is already running for ".concat(e)),this.started[e]=Date.now()}},{key:"timeEnd",value:function(e){e in this.started||(0,n.warn)("Timer has not been started for ".concat(e)),this.times.push({name:e,start:this.started[e],end:Date.now()}),delete this.started[e]}},{key:"toString",value:function(){var e,t=[],a=0,r=_createForOfIteratorHelper(this.times);try{for(r.s();!(e=r.n()).done;){var i=e.value.name;a=Math.max(i.length,a)}}catch(e){r.e(e)}finally{r.f()}var n,s=_createForOfIteratorHelper(this.times);try{for(s.s();!(n=s.n()).done;){var l=n.value,o=l.name,_=l.start,c=l.end;t.push("".concat(o.padEnd(a)," ").concat(c-_,"ms\n"))}}catch(e){s.e(e)}finally{s.f()}return t.join("")}}])}();function U(e,t){try{var a=(t?new URL(e,t):new URL(e)).protocol;return"http:"===a||"https:"===a}catch(e){return!1}}t.StatTimer=W;var G=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e)}),null,[{key:"toDateObject",value:function(e){if(!e||"string"!=typeof e)return null;B||(B=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));var t=B.exec(e);if(!t)return null;var a=parseInt(t[1],10),r=parseInt(t[2],10);r=r>=1&&r<=12?r-1:0;var i=parseInt(t[3],10);i=i>=1&&i<=31?i:1;var n=parseInt(t[4],10);n=n>=0&&n<=23?n:0;var s=parseInt(t[5],10);s=s>=0&&s<=59?s:0;var l=parseInt(t[6],10);l=l>=0&&l<=59?l:0;var o=t[7]||"Z",_=parseInt(t[8],10);_=_>=0&&_<=23?_:0;var c=parseInt(t[9],10)||0;return c=c>=0&&c<=59?c:0,"-"===o?(n+=_,s+=c):"+"===o&&(n-=_,s-=c),new Date(Date.UTC(a,r,i,n,s,l))}}])}();function K(e){if(e.startsWith("#")){var t=parseInt(e.slice(1),16);return[(16711680&t)>>16,(65280&t)>>8,255&t]}return e.startsWith("rgb(")?e.slice(4,-1).split(",").map((function(e){return parseInt(e)})):e.startsWith("rgba(")?e.slice(5,-1).split(",").map((function(e){return parseInt(e)})).slice(0,3):((0,n.warn)('Not a valid color format: "'.concat(e,'"')),[0,0,0])}t.PDFDateString=G},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.BaseStandardFontDataFactory=t.BaseSVGFactory=t.BaseFilterFactory=t.BaseCanvasFactory=t.BaseCMapReaderFactory=void 0;var r=a(1),i=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.constructor===e&&(0,r.unreachable)("Cannot initialize BaseFilterFactory.")}),[{key:"addFilter",value:function(e){return"none"}},{key:"addHCMFilter",value:function(e,t){return"none"}},{key:"addHighlightHCMFilter",value:function(e,t,a,r){return"none"}},{key:"destroy",value:function(){}}])}();t.BaseFilterFactory=i;var n=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.constructor===e&&(0,r.unreachable)("Cannot initialize BaseCanvasFactory.")}),[{key:"create",value:function(e,t){if(e<=0||t<=0)throw new Error("Invalid canvas size");var a=this._createCanvas(e,t);return{canvas:a,context:a.getContext("2d")}}},{key:"reset",value:function(e,t,a){if(!e.canvas)throw new Error("Canvas is not specified");if(t<=0||a<=0)throw new Error("Invalid canvas size");e.canvas.width=t,e.canvas.height=a}},{key:"destroy",value:function(e){if(!e.canvas)throw new Error("Canvas is not specified");e.canvas.width=0,e.canvas.height=0,e.canvas=null,e.context=null}},{key:"_createCanvas",value:function(e,t){(0,r.unreachable)("Abstract method `_createCanvas` called.")}}])}();t.BaseCanvasFactory=n;var s=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a=t.baseUrl,i=void 0===a?null:a,n=t.isCompressed,s=void 0===n||n;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.constructor===e&&(0,r.unreachable)("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=i,this.isCompressed=s}),[{key:"fetch",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t){var a,i,n,s=this;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=t.name,this.baseUrl){e.next=1;break}throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');case 1:if(a){e.next=2;break}throw new Error("CMap name must be specified.");case 2:return i=this.baseUrl+a+(this.isCompressed?".bcmap":""),n=this.isCompressed?r.CMapCompressionType.BINARY:r.CMapCompressionType.NONE,e.abrupt("return",this._fetchData(i,n).catch((function(e){throw new Error("Unable to load ".concat(s.isCompressed?"binary ":"","CMap at: ").concat(i))})));case 3:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"_fetchData",value:function(e,t){(0,r.unreachable)("Abstract method `_fetchData` called.")}}]);var e}();t.BaseCMapReaderFactory=s;var l=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a=t.baseUrl,i=void 0===a?null:a;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.constructor===e&&(0,r.unreachable)("Cannot initialize BaseStandardFontDataFactory."),this.baseUrl=i}),[{key:"fetch",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t){var a,r;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=t.filename,this.baseUrl){e.next=1;break}throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');case 1:if(a){e.next=2;break}throw new Error("Font filename must be specified.");case 2:return r="".concat(this.baseUrl).concat(a),e.abrupt("return",this._fetchData(r).catch((function(e){throw new Error("Unable to load font data at: ".concat(r))})));case 3:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"_fetchData",value:function(e){(0,r.unreachable)("Abstract method `_fetchData` called.")}}]);var e}();t.BaseStandardFontDataFactory=l;var o=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.constructor===e&&(0,r.unreachable)("Cannot initialize BaseSVGFactory.")}),[{key:"create",value:function(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(e<=0||t<=0)throw new Error("Invalid SVG dimensions");var r=this._createSVG("svg:svg");return r.setAttribute("version","1.1"),a||(r.setAttribute("width","".concat(e,"px")),r.setAttribute("height","".concat(t,"px"))),r.setAttribute("preserveAspectRatio","none"),r.setAttribute("viewBox","0 0 ".concat(e," ").concat(t)),r}},{key:"createElement",value:function(e){if("string"!=typeof e)throw new Error("Invalid SVG element type");return this._createSVG(e)}},{key:"_createSVG",value:function(e){(0,r.unreachable)("Abstract method `_createSVG` called.")}}])}();t.BaseSVGFactory=o},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.MurmurHash3_64=void 0;var r=a(1),i=3285377520,n=4294901760,s=65535,l=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.h1=t?4294967295&t:i,this.h2=t?4294967295&t:i}),[{key:"update",value:function(e){var t,a;if("string"==typeof e){t=new Uint8Array(2*e.length),a=0;for(var i=0,l=e.length;i<l;i++){var o=e.charCodeAt(i);o<=255?t[a++]=o:(t[a++]=o>>>8,t[a++]=255&o)}}else{if(!(0,r.isArrayBuffer)(e))throw new Error("Wrong data format in MurmurHash3_64_update. Input must be a string or array.");a=(t=e.slice()).byteLength}for(var _=a>>2,c=a-4*_,u=new Uint32Array(t.buffer,0,_),h=0,d=0,p=this.h1,v=this.h2,f=3432918353,m=461845907,b=11601,g=13715,P=0;P<_;P++)1&P?p=5*(p=(p^=h=(h=(h=(h=u[P])*f&n|h*b&s)<<15|h>>>17)*m&n|h*g&s)<<13|p>>>19)+3864292196:v=5*(v=(v^=d=(d=(d=(d=u[P])*f&n|d*b&s)<<15|d>>>17)*m&n|d*g&s)<<13|v>>>19)+3864292196;switch(h=0,c){case 3:h^=t[4*_+2]<<16;case 2:h^=t[4*_+1]<<8;case 1:h=(h=(h=(h^=t[4*_])*f&n|h*b&s)<<15|h>>>17)*m&n|h*g&s,1&_?p^=h:v^=h}this.h1=p,this.h2=v}},{key:"hexdigest",value:function(){var e=this.h1,t=this.h2;return e=3981806797*(e^=t>>>1)&n|36045*e&s,e=444984403*(e^=(t=4283543511*t&n|(2950163797*(t<<16|e>>>16)&n)>>>16)>>>1)&n|60499*e&s,((e^=(t=3301882366*t&n|(3120437893*(t<<16|e>>>16)&n)>>>16)>>>1)>>>0).toString(16).padStart(8,"0")+(t>>>0).toString(16).padStart(8,"0")}}])}();t.MurmurHash3_64=l},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.FontLoader=t.FontFaceObject=void 0;var r=a(1),i=new WeakMap,n=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a=t.ownerDocument,r=void 0===a?globalThis.document:a;t.styleElement,(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateFieldInitSpec(this,i,new Set),this._document=r,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}),[{key:"addNativeFontFace",value:function(e){this.nativeFontFaces.add(e),this._document.fonts.add(e)}},{key:"removeNativeFontFace",value:function(e){this.nativeFontFaces.delete(e),this._document.fonts.delete(e)}},{key:"insertRule",value:function(e){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));var t=this.styleElement.sheet;t.insertRule(e,t.cssRules.length)}},{key:"clear",value:function(){var e,t=_createForOfIteratorHelper(this.nativeFontFaces);try{for(t.s();!(e=t.n()).done;){var a=e.value;this._document.fonts.delete(a)}}catch(e){t.e(e)}finally{t.f()}this.nativeFontFaces.clear(),_classPrivateFieldGet(i,this).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}},{key:"loadSystemFont",value:(t=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t){var a,n,s,l;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t&&!_classPrivateFieldGet(i,this).has(t.loadedName)){e.next=1;break}return e.abrupt("return");case 1:if((0,r.assert)(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),!this.isFontLoadingAPISupported){e.next=6;break}return a=t.loadedName,n=t.src,s=t.style,l=new FontFace(a,n,s),this.addNativeFontFace(l),e.prev=2,e.next=3,l.load();case 3:_classPrivateFieldGet(i,this).add(a),e.next=5;break;case 4:e.prev=4,e.catch(2),(0,r.warn)("Cannot load system font: ".concat(t.baseFontName,", installing it could help to improve PDF rendering.")),this.removeNativeFontFace(l);case 5:return e.abrupt("return");case 6:(0,r.unreachable)("Not implemented: loadSystemFont without the Font Loading API.");case 7:case"end":return e.stop()}}),e,this,[[2,4]])}))),function(e){return t.apply(this,arguments)})},{key:"bind",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t){var a,i,n,s=this;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t.attached||t.missingFile&&!t.systemFontInfo)){e.next=1;break}return e.abrupt("return");case 1:if(t.attached=!0,!t.systemFontInfo){e.next=3;break}return e.next=2,this.loadSystemFont(t.systemFontInfo);case 2:case 7:return e.abrupt("return");case 3:if(!this.isFontLoadingAPISupported){e.next=8;break}if(!(a=t.createNativeFontFace())){e.next=7;break}return this.addNativeFontFace(a),e.prev=4,e.next=5,a.loaded;case 5:e.next=7;break;case 6:throw e.prev=6,n=e.catch(4),(0,r.warn)("Failed to load font '".concat(a.family,"': '").concat(n,"'.")),t.disableFontFace=!0,n;case 8:if(!(i=t.createFontFaceRule())){e.next=10;break}if(this.insertRule(i),!this.isSyncFontLoadingSupported){e.next=9;break}return e.abrupt("return");case 9:return e.next=10,new Promise((function(e){var a=s._queueLoadingCallback(e);s._prepareFontLoadEvent(t,a)}));case 10:case"end":return e.stop()}}),e,this,[[4,6]])}))),function(t){return e.apply(this,arguments)})},{key:"isFontLoadingAPISupported",get:function(){var e,t=!(null===(e=this._document)||void 0===e||!e.fonts);return(0,r.shadow)(this,"isFontLoadingAPISupported",t)}},{key:"isSyncFontLoadingSupported",get:function(){var e=!1;return(r.isNodeJS||"undefined"!=typeof navigator&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(e=!0),(0,r.shadow)(this,"isSyncFontLoadingSupported",e)}},{key:"_queueLoadingCallback",value:function(e){var t=this.loadingRequests,a={done:!1,complete:function(){for((0,r.assert)(!a.done,"completeRequest() cannot be called twice."),a.done=!0;t.length>0&&t[0].done;){var e=t.shift();setTimeout(e.callback,0)}},callback:e};return t.push(a),a}},{key:"_loadTestFont",get:function(){var e=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return(0,r.shadow)(this,"_loadTestFont",e)}},{key:"_prepareFontLoadEvent",value:function(e,t){function a(e,t){return e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|255&e.charCodeAt(t+3)}function i(e,t,a,r){return e.substring(0,t)+r+e.substring(t+a)}var n,s,l=this._document.createElement("canvas");l.width=1,l.height=1;var o=l.getContext("2d"),_=0,c="lt".concat(Date.now()).concat(this.loadTestFontId++),u=this._loadTestFont,h=1482184792,d=a(u=i(u,976,c.length,c),16);for(n=0,s=c.length-3;n<s;n+=4)d=d-h+a(c,n)|0;n<c.length&&(d=d-h+a(c+"XXX",n)|0),u=i(u,16,4,(0,r.string32)(d));var p="url(data:font/opentype;base64,".concat(btoa(u),");"),v='@font-face {font-family:"'.concat(c,'";src:').concat(p,"}");this.insertRule(v);var f=this._document.createElement("div");f.style.visibility="hidden",f.style.width=f.style.height="10px",f.style.position="absolute",f.style.top=f.style.left="0px";for(var m=0,b=[e.loadedName,c];m<b.length;m++){var g=b[m],P=this._document.createElement("span");P.textContent="Hi",P.style.fontFamily=g,f.append(P)}this._document.body.append(f),function e(t,a){if(++_>30)return(0,r.warn)("Load test font never loaded."),void a();o.font="30px "+t,o.fillText(".",0,20),o.getImageData(0,0,1,1).data[3]>0?a():setTimeout(e.bind(null,t,a))}(c,(function(){f.remove(),t.complete()}))}}]);var e,t}();t.FontLoader=n;var s=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a){var r=a.isEvalSupported,i=void 0===r||r,n=a.disableFontFace,s=void 0!==n&&n,l=a.ignoreErrors,o=void 0!==l&&l,_=a.inspectFont,c=void 0===_?null:_;for(var u in(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.compiledGlyphs=Object.create(null),t)this[u]=t[u];this.isEvalSupported=!1!==i,this.disableFontFace=!0===s,this.ignoreErrors=!0===o,this._inspectFont=c}),[{key:"createNativeFontFace",value:function(){var e,t;if(!this.data||this.disableFontFace)return null;if(this.cssFontInfo){var a={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(a.style="oblique ".concat(this.cssFontInfo.italicAngle,"deg")),t=new FontFace(this.cssFontInfo.fontFamily,this.data,a)}else t=new FontFace(this.loadedName,this.data,{});return null===(e=this._inspectFont)||void 0===e||e.call(this,this),t}},{key:"createFontFaceRule",value:function(){var e;if(!this.data||this.disableFontFace)return null;var t,a=(0,r.bytesToString)(this.data),i="url(data:".concat(this.mimetype,";base64,").concat(btoa(a),");");if(this.cssFontInfo){var n="font-weight: ".concat(this.cssFontInfo.fontWeight,";");this.cssFontInfo.italicAngle&&(n+="font-style: oblique ".concat(this.cssFontInfo.italicAngle,"deg;")),t='@font-face {font-family:"'.concat(this.cssFontInfo.fontFamily,'";').concat(n,"src:").concat(i,"}")}else t='@font-face {font-family:"'.concat(this.loadedName,'";src:').concat(i,"}");return null===(e=this._inspectFont)||void 0===e||e.call(this,this,i),t}},{key:"getPathGenerator",value:function(e,t){if(void 0!==this.compiledGlyphs[t])return this.compiledGlyphs[t];var a;try{a=e.get(this.loadedName+"_path_"+t)}catch(e){if(!this.ignoreErrors)throw e;return(0,r.warn)('getPathGenerator - ignoring character: "'.concat(e,'".')),this.compiledGlyphs[t]=function(e,t){}}if(this.isEvalSupported&&r.FeatureTest.isEvalSupported){var i,n=[],s=_createForOfIteratorHelper(a);try{for(s.s();!(i=s.n()).done;){var l=i.value,o=void 0!==l.args?l.args.join(","):"";n.push("c.",l.cmd,"(",o,");\n")}}catch(e){s.e(e)}finally{s.f()}return this.compiledGlyphs[t]=new Function("c","size",n.join(""))}return this.compiledGlyphs[t]=function(e,t){var r,i=_createForOfIteratorHelper(a);try{for(i.s();!(r=i.n()).done;){var n=r.value;"scale"===n.cmd&&(n.args=[t,-t]),e[n.cmd].apply(e,n.args)}}catch(e){i.e(e)}finally{i.f()}}}}])}();t.FontFaceObject=s},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.NodeStandardFontDataFactory=t.NodeFilterFactory=t.NodeCanvasFactory=t.NodeCMapReaderFactory=void 0;var r=a(7),i=(a(1),function(e){return new Promise((function(t,a){__webpack_require__(950).readFile(e,(function(e,r){!e&&r?t(new Uint8Array(r)):a(new Error(e))}))}))}),n=function(e){function t(){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,arguments)}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(r.BaseFilterFactory);t.NodeFilterFactory=n;var s=function(e){function t(){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,arguments)}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"_createCanvas",value:function(e,t){return __webpack_require__(6359).createCanvas(e,t)}}])}(r.BaseCanvasFactory);t.NodeCanvasFactory=s;var l=function(e){function t(){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,arguments)}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"_fetchData",value:function(e,t){return i(e).then((function(e){return{cMapData:e,compressionType:t}}))}}])}(r.BaseCMapReaderFactory);t.NodeCMapReaderFactory=l;var o=function(e){function t(){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,arguments)}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"_fetchData",value:function(e){return i(e)}}])}(r.BaseStandardFontDataFactory);t.NodeStandardFontDataFactory=o},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.CanvasGraphics=void 0;var r=a(1),i=a(6),n=a(12),s=a(13),l=4096,o=16,_=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.canvasFactory=t,this.cache=Object.create(null)}),[{key:"getCanvas",value:function(e,t,a){var r;return void 0!==this.cache[e]?(r=this.cache[e],this.canvasFactory.reset(r,t,a)):(r=this.canvasFactory.create(t,a),this.cache[e]=r),r}},{key:"delete",value:function(e){delete this.cache[e]}},{key:"clear",value:function(){for(var e in this.cache){var t=this.cache[e];this.canvasFactory.destroy(t),delete this.cache[e]}}}])}();function c(e,t,a,r,n,s,l,o,_,c){var u=(0,i.getCurrentTransform)(e),h=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(u,6),d=h[0],p=h[1],v=h[2],f=h[3],m=h[4],b=h[5];if(0===p&&0===v){var g=l*d+m,P=Math.round(g),y=o*f+b,A=Math.round(y),E=(l+_)*d+m,C=Math.abs(Math.round(E)-P)||1,M=(o+c)*f+b,k=Math.abs(Math.round(M)-A)||1;return e.setTransform(Math.sign(d),0,0,Math.sign(f),P,A),e.drawImage(t,a,r,n,s,0,0,C,k),e.setTransform(d,p,v,f,m,b),[C,k]}if(0===d&&0===f){var O=o*v+m,D=Math.round(O),F=l*p+b,S=Math.round(F),T=(o+c)*v+m,I=Math.abs(Math.round(T)-D)||1,w=(l+_)*p+b,x=Math.abs(Math.round(w)-S)||1;return e.setTransform(0,Math.sign(p),Math.sign(v),0,D,S),e.drawImage(t,a,r,n,s,0,0,x,I),e.setTransform(d,p,v,f,m,b),[x,I]}return e.drawImage(t,a,r,n,s,l,o,_,c),[Math.hypot(d,p)*_,Math.hypot(v,f)*c]}var u=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=r.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=r.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=r.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,t,a])}),[{key:"clone",value:function(){var e=Object.create(this);return e.clipBox=this.clipBox.slice(),e}},{key:"setCurrentPoint",value:function(e,t){this.x=e,this.y=t}},{key:"updatePathMinMax",value:function(e,t,a){var i=r.Util.applyTransform([t,a],e),n=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(i,2);t=n[0],a=n[1],this.minX=Math.min(this.minX,t),this.minY=Math.min(this.minY,a),this.maxX=Math.max(this.maxX,t),this.maxY=Math.max(this.maxY,a)}},{key:"updateRectMinMax",value:function(e,t){var a=r.Util.applyTransform(t,e),i=r.Util.applyTransform(t.slice(2),e);this.minX=Math.min(this.minX,a[0],i[0]),this.minY=Math.min(this.minY,a[1],i[1]),this.maxX=Math.max(this.maxX,a[0],i[0]),this.maxY=Math.max(this.maxY,a[1],i[1])}},{key:"updateScalingPathMinMax",value:function(e,t){r.Util.scaleMinMax(e,t),this.minX=Math.min(this.minX,t[0]),this.maxX=Math.max(this.maxX,t[1]),this.minY=Math.min(this.minY,t[2]),this.maxY=Math.max(this.maxY,t[3])}},{key:"updateCurvePathMinMax",value:function(e,t,a,i,n,s,l,o,_,c){var u=r.Util.bezierBoundingBox(t,a,i,n,s,l,o,_);if(c)return c[0]=Math.min(c[0],u[0],u[2]),c[1]=Math.max(c[1],u[0],u[2]),c[2]=Math.min(c[2],u[1],u[3]),void(c[3]=Math.max(c[3],u[1],u[3]));this.updateRectMinMax(e,u)}},{key:"getPathBoundingBox",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n.PathType.FILL,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=[this.minX,this.minY,this.maxX,this.maxY];if(e===n.PathType.STROKE){t||(0,r.unreachable)("Stroke bounding box must include transform.");var i=r.Util.singularValueDecompose2dScale(t),s=i[0]*this.lineWidth/2,l=i[1]*this.lineWidth/2;a[0]-=s,a[1]-=l,a[2]+=s,a[3]+=l}return a}},{key:"updateClipFromPath",value:function(){var e=r.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(e||[0,0,0,0])}},{key:"isEmptyClip",value:function(){return this.minX===1/0}},{key:"startNewPathAndClipBox",value:function(e){this.clipBox=e,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}},{key:"getClippedPathBoundingBox",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n.PathType.FILL,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return r.Util.intersect(this.clipBox,this.getPathBoundingBox(e,t))}}])}();function h(e,t){if("undefined"!=typeof ImageData&&t instanceof ImageData)e.putImageData(t,0,0);else{var a,i,n,s,l,_=t.height,c=t.width,u=_%o,h=(_-u)/o,d=0===u?h:h+1,p=e.createImageData(c,o),v=0,f=t.data,m=p.data;if(t.kind===r.ImageKind.GRAYSCALE_1BPP){var b=f.byteLength,g=new Uint32Array(m.buffer,0,m.byteLength>>2),P=g.length,y=c+7>>3,A=4294967295,E=r.FeatureTest.isLittleEndian?4278190080:255;for(i=0;i<d;i++){for(s=i<h?o:u,a=0,n=0;n<s;n++){for(var C=b-v,M=0,k=C>y?c:8*C-7,O=-8&k,D=0,F=0;M<O;M+=8)F=f[v++],g[a++]=128&F?A:E,g[a++]=64&F?A:E,g[a++]=32&F?A:E,g[a++]=16&F?A:E,g[a++]=8&F?A:E,g[a++]=4&F?A:E,g[a++]=2&F?A:E,g[a++]=1&F?A:E;for(;M<k;M++)0===D&&(F=f[v++],D=128),g[a++]=F&D?A:E,D>>=1}for(;a<P;)g[a++]=0;e.putImageData(p,0,i*o)}}else if(t.kind===r.ImageKind.RGBA_32BPP){for(n=0,l=c*o*4,i=0;i<h;i++)m.set(f.subarray(v,v+l)),v+=l,e.putImageData(p,0,n),n+=o;i<d&&(l=c*u*4,m.set(f.subarray(v,v+l)),e.putImageData(p,0,n))}else{if(t.kind!==r.ImageKind.RGB_24BPP)throw new Error("bad image kind: ".concat(t.kind));for(l=c*(s=o),i=0;i<d;i++){for(i>=h&&(l=c*(s=u)),a=0,n=l;n--;)m[a++]=f[v++],m[a++]=f[v++],m[a++]=f[v++],m[a++]=255;e.putImageData(p,0,i*o)}}}}function d(e,t){if(t.bitmap)e.drawImage(t.bitmap,0,0);else for(var a=t.height,r=t.width,i=a%o,n=(a-i)/o,l=0===i?n:n+1,_=e.createImageData(r,o),c=0,u=t.data,h=_.data,d=0;d<l;d++){var p=d<n?o:i;c=(0,s.convertBlackAndWhiteToRGBA)({src:u,srcPos:c,dest:h,width:r,height:p,nonBlackColor:0}).srcPos,e.putImageData(_,0,d*o)}}function p(e,t){for(var a=0,r=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];a<r.length;a++){var i=r[a];void 0!==e[i]&&(t[i]=e[i])}void 0!==e.setLineDash&&(t.setLineDash(e.getLineDash()),t.lineDashOffset=e.lineDashOffset)}function v(e){if(e.strokeStyle=e.fillStyle="#000000",e.fillRule="nonzero",e.globalAlpha=1,e.lineWidth=1,e.lineCap="butt",e.lineJoin="miter",e.miterLimit=10,e.globalCompositeOperation="source-over",e.font="10px sans-serif",void 0!==e.setLineDash&&(e.setLineDash([]),e.lineDashOffset=0),!r.isNodeJS){var t=e.filter;"none"!==t&&""!==t&&(e.filter="none")}}function f(e,t,a,r){for(var i=e.length,n=3;n<i;n+=4){var s=e[n];if(0===s)e[n-3]=t,e[n-2]=a,e[n-1]=r;else if(s<255){var l=255-s;e[n-3]=e[n-3]*s+t*l>>8,e[n-2]=e[n-2]*s+a*l>>8,e[n-1]=e[n-1]*s+r*l>>8}}}function m(e,t,a){for(var r=e.length,i=1/255,n=3;n<r;n+=4){var s=a?a[e[n]]:e[n];t[n]=t[n]*s*i|0}}function b(e,t,a){for(var r=e.length,i=3;i<r;i+=4){var n=77*e[i-3]+152*e[i-2]+28*e[i-1];t[i]=a?t[i]*a[n>>8]>>8:t[i]*n>>16}}function g(e,t){var a=r.Util.singularValueDecompose2dScale(e);a[0]=Math.fround(a[0]),a[1]=Math.fround(a[1]);var n=Math.fround((globalThis.devicePixelRatio||1)*i.PixelsPerInch.PDF_TO_CSS_UNITS);return void 0!==t?t:a[0]<=n||a[1]<=n}var P=["butt","round","square"],y=["miter","round","bevel"],A={},E={},C=new WeakSet,M=function(){function e(t,a,r,i,n,s,l,o){var c=s.optionalContentConfig,h=s.markedContentStack,d=void 0===h?null:h;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,C),this.ctx=t,this.current=new u(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=a,this.objs=r,this.canvasFactory=i,this.filterFactory=n,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=d||[],this.optionalContentConfig=c,this.cachedCanvases=new _(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=l,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=o,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(e,[{key:"getObject",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return"string"==typeof e?e.startsWith("g_")?this.commonObjs.get(e):this.objs.get(e):t}},{key:"beginDrawing",value:function(e){var t,a,r=e.transform,n=e.viewport,s=e.transparency,l=void 0!==s&&s,o=e.background,_=void 0===o?null:o,c=this.ctx.canvas.width,u=this.ctx.canvas.height,h=this.ctx.fillStyle;if(this.ctx.fillStyle=_||"#ffffff",this.ctx.fillRect(0,0,c,u),this.ctx.fillStyle=h,l){var d,p=this.cachedCanvases.getCanvas("transparent",c,u);this.compositeCtx=this.ctx,this.transparentCanvas=p.canvas,this.ctx=p.context,this.ctx.save(),(d=this.ctx).transform.apply(d,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)((0,i.getCurrentTransform)(this.compositeCtx)))}this.ctx.save(),v(this.ctx),r&&((a=this.ctx).transform.apply(a,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(r)),this.outputScaleX=r[0],this.outputScaleY=r[0]),(t=this.ctx).transform.apply(t,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(n.transform)),this.viewportScale=n.scale,this.baseTransform=(0,i.getCurrentTransform)(this.ctx)}},{key:"executeOperatorList",value:function(e,t,a,i){var n=e.argsArray,s=e.fnArray,l=t||0,o=n.length;if(o===l)return l;for(var _,c=o-l>10&&"function"==typeof a,u=c?Date.now()+15:0,h=0,d=this.commonObjs,p=this.objs;;){if(void 0!==i&&l===i.nextBreakPoint)return i.breakIt(l,a),l;if((_=s[l])!==r.OPS.dependency)this[_].apply(this,n[l]);else{var v,f=_createForOfIteratorHelper(n[l]);try{for(f.s();!(v=f.n()).done;){var m=v.value,b=m.startsWith("g_")?d:p;if(!b.has(m))return b.get(m,a),l}}catch(e){f.e(e)}finally{f.f()}}if(++l===o)return l;if(c&&++h>10){if(Date.now()>u)return a(),l;h=0}}}},{key:"endDrawing",value:function(){_assertClassBrand(C,this,k).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();var e,t=_createForOfIteratorHelper(this._cachedBitmapsMap.values());try{for(t.s();!(e=t.n()).done;){var a,r=e.value,i=_createForOfIteratorHelper(r.values());try{for(i.s();!(a=i.n()).done;){var n=a.value;"undefined"!=typeof HTMLCanvasElement&&n instanceof HTMLCanvasElement&&(n.width=n.height=0)}}catch(e){i.e(e)}finally{i.f()}r.clear()}}catch(e){t.e(e)}finally{t.f()}this._cachedBitmapsMap.clear(),_assertClassBrand(C,this,O).call(this)}},{key:"_scaleImage",value:function(e,t){for(var a,r,i=e.width,n=e.height,s=Math.max(Math.hypot(t[0],t[1]),1),l=Math.max(Math.hypot(t[2],t[3]),1),o=i,_=n,c="prescale1";s>2&&o>1||l>2&&_>1;){var u=o,h=_;s>2&&o>1&&(s/=o/(u=o>=16384?Math.floor(o/2)-1||1:Math.ceil(o/2))),l>2&&_>1&&(l/=_/(h=_>=16384?Math.floor(_/2)-1||1:Math.ceil(_)/2)),(r=(a=this.cachedCanvases.getCanvas(c,u,h)).context).clearRect(0,0,u,h),r.drawImage(e,0,0,o,_,0,0,u,h),e=a.canvas,o=u,_=h,c="prescale1"===c?"prescale2":"prescale1"}return{img:e,paintWidth:o,paintHeight:_}}},{key:"_createMaskCanvas",value:function(e){var t,a,s,l,o=this.ctx,_=e.width,u=e.height,h=this.current.fillColor,p=this.current.patternFill,v=(0,i.getCurrentTransform)(o);if((e.bitmap||e.data)&&e.count>1){var f=e.bitmap||e.data.buffer;a=JSON.stringify(p?v:[v.slice(0,4),h]),(t=this._cachedBitmapsMap.get(f))||(t=new Map,this._cachedBitmapsMap.set(f,t));var m=t.get(a);if(m&&!p)return{canvas:m,offsetX:Math.round(Math.min(v[0],v[2])+v[4]),offsetY:Math.round(Math.min(v[1],v[3])+v[5])};s=m}s||d((l=this.cachedCanvases.getCanvas("maskCanvas",_,u)).context,e);var b=r.Util.transform(v,[1/_,0,0,-1/u,0,0]);b=r.Util.transform(b,[1,0,0,1,0,-u]);var P=r.Util.applyTransform([0,0],b),y=r.Util.applyTransform([_,u],b),A=r.Util.normalizeRect([P[0],P[1],y[0],y[1]]),E=Math.round(A[2]-A[0])||1,C=Math.round(A[3]-A[1])||1,M=this.cachedCanvases.getCanvas("fillCanvas",E,C),k=M.context,O=Math.min(P[0],y[0]),D=Math.min(P[1],y[1]);k.translate(-O,-D),k.transform.apply(k,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(b)),s||(s=(s=this._scaleImage(l.canvas,(0,i.getCurrentTransformInverse)(k))).img,t&&p&&t.set(a,s)),k.imageSmoothingEnabled=g((0,i.getCurrentTransform)(k),e.interpolate),c(k,s,0,0,s.width,s.height,0,0,_,u),k.globalCompositeOperation="source-in";var F=r.Util.transform((0,i.getCurrentTransformInverse)(k),[1,0,0,1,-O,-D]);return k.fillStyle=p?h.getPattern(o,this,F,n.PathType.FILL):h,k.fillRect(0,0,_,u),t&&!p&&(this.cachedCanvases.delete("fillCanvas"),t.set(a,M.canvas)),{canvas:M.canvas,offsetX:Math.round(O),offsetY:Math.round(D)}}},{key:"setLineWidth",value:function(e){e!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=e,this.ctx.lineWidth=e}},{key:"setLineCap",value:function(e){this.ctx.lineCap=P[e]}},{key:"setLineJoin",value:function(e){this.ctx.lineJoin=y[e]}},{key:"setMiterLimit",value:function(e){this.ctx.miterLimit=e}},{key:"setDash",value:function(e,t){var a=this.ctx;void 0!==a.setLineDash&&(a.setLineDash(e),a.lineDashOffset=t)}},{key:"setRenderingIntent",value:function(e){}},{key:"setFlatness",value:function(e){}},{key:"setGState",value:function(e){var t,a=_createForOfIteratorHelper(e);try{for(a.s();!(t=a.n()).done;){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(t.value,2),i=r[0],n=r[1];switch(i){case"LW":this.setLineWidth(n);break;case"LC":this.setLineCap(n);break;case"LJ":this.setLineJoin(n);break;case"ML":this.setMiterLimit(n);break;case"D":this.setDash(n[0],n[1]);break;case"RI":this.setRenderingIntent(n);break;case"FL":this.setFlatness(n);break;case"Font":this.setFont(n[0],n[1]);break;case"CA":this.current.strokeAlpha=n;break;case"ca":this.current.fillAlpha=n,this.ctx.globalAlpha=n;break;case"BM":this.ctx.globalCompositeOperation=n;break;case"SMask":this.current.activeSMask=n?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(n)}}}catch(e){a.e(e)}finally{a.f()}}},{key:"inSMaskMode",get:function(){return!!this.suspendedCtx}},{key:"checkSMaskState",value:function(){var e=this.inSMaskMode;this.current.activeSMask&&!e?this.beginSMaskMode():!this.current.activeSMask&&e&&this.endSMaskMode()}},{key:"beginSMaskMode",value:function(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");var e=this.ctx.canvas.width,t=this.ctx.canvas.height,a="smaskGroupAt"+this.groupLevel,r=this.cachedCanvases.getCanvas(a,e,t);this.suspendedCtx=this.ctx,this.ctx=r.context;var n=this.ctx;n.setTransform.apply(n,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)((0,i.getCurrentTransform)(this.suspendedCtx))),p(this.suspendedCtx,n),function(e,t){if(e._removeMirroring)throw new Error("Context is already forwarding operations.");e.__originalSave=e.save,e.__originalRestore=e.restore,e.__originalRotate=e.rotate,e.__originalScale=e.scale,e.__originalTranslate=e.translate,e.__originalTransform=e.transform,e.__originalSetTransform=e.setTransform,e.__originalResetTransform=e.resetTransform,e.__originalClip=e.clip,e.__originalMoveTo=e.moveTo,e.__originalLineTo=e.lineTo,e.__originalBezierCurveTo=e.bezierCurveTo,e.__originalRect=e.rect,e.__originalClosePath=e.closePath,e.__originalBeginPath=e.beginPath,e._removeMirroring=function(){e.save=e.__originalSave,e.restore=e.__originalRestore,e.rotate=e.__originalRotate,e.scale=e.__originalScale,e.translate=e.__originalTranslate,e.transform=e.__originalTransform,e.setTransform=e.__originalSetTransform,e.resetTransform=e.__originalResetTransform,e.clip=e.__originalClip,e.moveTo=e.__originalMoveTo,e.lineTo=e.__originalLineTo,e.bezierCurveTo=e.__originalBezierCurveTo,e.rect=e.__originalRect,e.closePath=e.__originalClosePath,e.beginPath=e.__originalBeginPath,delete e._removeMirroring},e.save=function(){t.save(),this.__originalSave()},e.restore=function(){t.restore(),this.__originalRestore()},e.translate=function(e,a){t.translate(e,a),this.__originalTranslate(e,a)},e.scale=function(e,a){t.scale(e,a),this.__originalScale(e,a)},e.transform=function(e,a,r,i,n,s){t.transform(e,a,r,i,n,s),this.__originalTransform(e,a,r,i,n,s)},e.setTransform=function(e,a,r,i,n,s){t.setTransform(e,a,r,i,n,s),this.__originalSetTransform(e,a,r,i,n,s)},e.resetTransform=function(){t.resetTransform(),this.__originalResetTransform()},e.rotate=function(e){t.rotate(e),this.__originalRotate(e)},e.clip=function(e){t.clip(e),this.__originalClip(e)},e.moveTo=function(e,a){t.moveTo(e,a),this.__originalMoveTo(e,a)},e.lineTo=function(e,a){t.lineTo(e,a),this.__originalLineTo(e,a)},e.bezierCurveTo=function(e,a,r,i,n,s){t.bezierCurveTo(e,a,r,i,n,s),this.__originalBezierCurveTo(e,a,r,i,n,s)},e.rect=function(e,a,r,i){t.rect(e,a,r,i),this.__originalRect(e,a,r,i)},e.closePath=function(){t.closePath(),this.__originalClosePath()},e.beginPath=function(){t.beginPath(),this.__originalBeginPath()}}(n,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}},{key:"endSMaskMode",value:function(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),p(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}},{key:"compose",value:function(e){if(this.current.activeSMask){e?(e[0]=Math.floor(e[0]),e[1]=Math.floor(e[1]),e[2]=Math.ceil(e[2]),e[3]=Math.ceil(e[3])):e=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];var t=this.current.activeSMask;(function(e,t,a,r){var i=r[0],n=r[1],s=r[2]-i,l=r[3]-n;0!==s&&0!==l&&(function(e,t,a,r,i,n,s,l,o,_,c){for(var u=!!n,h=u?n[0]:0,d=u?n[1]:0,p=u?n[2]:0,v="Luminosity"===i?b:m,g=Math.min(r,Math.ceil(1048576/a)),P=0;P<r;P+=g){var y=Math.min(g,r-P),A=e.getImageData(l-_,P+(o-c),a,y),E=t.getImageData(l,P+o,a,y);u&&f(A.data,h,d,p),v(A.data,E.data,s),t.putImageData(E,l,P+o)}}(t.context,a,s,l,t.subtype,t.backdrop,t.transferMap,i,n,t.offsetX,t.offsetY),e.save(),e.globalAlpha=1,e.globalCompositeOperation="source-over",e.setTransform(1,0,0,1,0,0),e.drawImage(a.canvas,0,0),e.restore())})(this.suspendedCtx,t,this.ctx,e),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}}},{key:"save",value:function(){this.inSMaskMode?(p(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();var e=this.current;this.stateStack.push(e),this.current=e.clone()}},{key:"restore",value:function(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode(),0!==this.stateStack.length&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),p(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}},{key:"transform",value:function(e,t,a,r,i,n){this.ctx.transform(e,t,a,r,i,n),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}},{key:"constructPath",value:function(e,t,a){for(var n,s,l=this.ctx,o=this.current,_=o.x,c=o.y,u=(0,i.getCurrentTransform)(l),h=0===u[0]&&0===u[3]||0===u[1]&&0===u[2],d=h?a.slice(0):null,p=0,v=0,f=e.length;p<f;p++)switch(0|e[p]){case r.OPS.rectangle:_=t[v++],c=t[v++];var m=t[v++],b=t[v++],g=_+m,P=c+b;l.moveTo(_,c),0===m||0===b?l.lineTo(g,P):(l.lineTo(g,c),l.lineTo(g,P),l.lineTo(_,P)),h||o.updateRectMinMax(u,[_,c,g,P]),l.closePath();break;case r.OPS.moveTo:_=t[v++],c=t[v++],l.moveTo(_,c),h||o.updatePathMinMax(u,_,c);break;case r.OPS.lineTo:_=t[v++],c=t[v++],l.lineTo(_,c),h||o.updatePathMinMax(u,_,c);break;case r.OPS.curveTo:n=_,s=c,_=t[v+4],c=t[v+5],l.bezierCurveTo(t[v],t[v+1],t[v+2],t[v+3],_,c),o.updateCurvePathMinMax(u,n,s,t[v],t[v+1],t[v+2],t[v+3],_,c,d),v+=6;break;case r.OPS.curveTo2:n=_,s=c,l.bezierCurveTo(_,c,t[v],t[v+1],t[v+2],t[v+3]),o.updateCurvePathMinMax(u,n,s,_,c,t[v],t[v+1],t[v+2],t[v+3],d),_=t[v+2],c=t[v+3],v+=4;break;case r.OPS.curveTo3:n=_,s=c,_=t[v+2],c=t[v+3],l.bezierCurveTo(t[v],t[v+1],_,c,_,c),o.updateCurvePathMinMax(u,n,s,t[v],t[v+1],_,c,_,c,d),v+=4;break;case r.OPS.closePath:l.closePath()}h&&o.updateScalingPathMinMax(u,d),o.setCurrentPoint(_,c)}},{key:"closePath",value:function(){this.ctx.closePath()}},{key:"stroke",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=this.ctx,a=this.current.strokeColor;t.globalAlpha=this.current.strokeAlpha,this.contentVisible&&("object"===(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(a)&&null!=a&&a.getPattern?(t.save(),t.strokeStyle=a.getPattern(t,this,(0,i.getCurrentTransformInverse)(t),n.PathType.STROKE),this.rescaleAndStroke(!1),t.restore()):this.rescaleAndStroke(!0)),e&&this.consumePath(this.current.getClippedPathBoundingBox()),t.globalAlpha=this.current.fillAlpha}},{key:"closeStroke",value:function(){this.closePath(),this.stroke()}},{key:"fill",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=this.ctx,a=this.current.fillColor,r=!1;this.current.patternFill&&(t.save(),t.fillStyle=a.getPattern(t,this,(0,i.getCurrentTransformInverse)(t),n.PathType.FILL),r=!0);var s=this.current.getClippedPathBoundingBox();this.contentVisible&&null!==s&&(this.pendingEOFill?(t.fill("evenodd"),this.pendingEOFill=!1):t.fill()),r&&t.restore(),e&&this.consumePath(s)}},{key:"eoFill",value:function(){this.pendingEOFill=!0,this.fill()}},{key:"fillStroke",value:function(){this.fill(!1),this.stroke(!1),this.consumePath()}},{key:"eoFillStroke",value:function(){this.pendingEOFill=!0,this.fillStroke()}},{key:"closeFillStroke",value:function(){this.closePath(),this.fillStroke()}},{key:"closeEOFillStroke",value:function(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}},{key:"endPath",value:function(){this.consumePath()}},{key:"clip",value:function(){this.pendingClip=A}},{key:"eoClip",value:function(){this.pendingClip=E}},{key:"beginText",value:function(){this.current.textMatrix=r.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}},{key:"endText",value:function(){var e=this.pendingTextPaths,t=this.ctx;if(void 0!==e){t.save(),t.beginPath();var a,r=_createForOfIteratorHelper(e);try{for(r.s();!(a=r.n()).done;){var i=a.value;t.setTransform.apply(t,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(i.transform)),t.translate(i.x,i.y),i.addToPath(t,i.fontSize)}}catch(e){r.e(e)}finally{r.f()}t.restore(),t.clip(),t.beginPath(),delete this.pendingTextPaths}else t.beginPath()}},{key:"setCharSpacing",value:function(e){this.current.charSpacing=e}},{key:"setWordSpacing",value:function(e){this.current.wordSpacing=e}},{key:"setHScale",value:function(e){this.current.textHScale=e/100}},{key:"setLeading",value:function(e){this.current.leading=-e}},{key:"setFont",value:function(e,t){var a,i=this.commonObjs.get(e),n=this.current;if(!i)throw new Error("Can't find font for ".concat(e));if(n.fontMatrix=i.fontMatrix||r.FONT_IDENTITY_MATRIX,0!==n.fontMatrix[0]&&0!==n.fontMatrix[3]||(0,r.warn)("Invalid font matrix for font "+e),t<0?(t=-t,n.fontDirection=-1):n.fontDirection=1,this.current.font=i,this.current.fontSize=t,!i.isType3Font){var s=i.loadedName||"sans-serif",l=(null===(a=i.systemFontInfo)||void 0===a?void 0:a.css)||'"'.concat(s,'", ').concat(i.fallbackName),o="normal";i.black?o="900":i.bold&&(o="bold");var _=i.italic?"italic":"normal",c=t;t<16?c=16:t>100&&(c=100),this.current.fontSizeScale=t/c,this.ctx.font="".concat(_," ").concat(o," ").concat(c,"px ").concat(l)}}},{key:"setTextRenderingMode",value:function(e){this.current.textRenderingMode=e}},{key:"setTextRise",value:function(e){this.current.textRise=e}},{key:"moveText",value:function(e,t){this.current.x=this.current.lineX+=e,this.current.y=this.current.lineY+=t}},{key:"setLeadingMoveText",value:function(e,t){this.setLeading(-t),this.moveText(e,t)}},{key:"setTextMatrix",value:function(e,t,a,r,i,n){this.current.textMatrix=[e,t,a,r,i,n],this.current.textMatrixScale=Math.hypot(e,t),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}},{key:"nextLine",value:function(){this.moveText(0,this.current.leading)}},{key:"paintChar",value:function(e,t,a,n){var s,l=this.ctx,o=this.current,_=o.font,c=o.textRenderingMode,u=o.fontSize/o.fontSizeScale,h=c&r.TextRenderingMode.FILL_STROKE_MASK,d=!!(c&r.TextRenderingMode.ADD_TO_PATH_FLAG),p=o.patternFill&&!_.missingFile;(_.disableFontFace||d||p)&&(s=_.getPathGenerator(this.commonObjs,e)),_.disableFontFace||p?(l.save(),l.translate(t,a),l.beginPath(),s(l,u),n&&l.setTransform.apply(l,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(n)),h!==r.TextRenderingMode.FILL&&h!==r.TextRenderingMode.FILL_STROKE||l.fill(),h!==r.TextRenderingMode.STROKE&&h!==r.TextRenderingMode.FILL_STROKE||l.stroke(),l.restore()):(h!==r.TextRenderingMode.FILL&&h!==r.TextRenderingMode.FILL_STROKE||l.fillText(e,t,a),h!==r.TextRenderingMode.STROKE&&h!==r.TextRenderingMode.FILL_STROKE||l.strokeText(e,t,a)),d&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:(0,i.getCurrentTransform)(l),x:t,y:a,fontSize:u,addToPath:s})}},{key:"isFontSubpixelAAEnabled",get:function(){var e=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10).context;e.scale(1.5,1),e.fillText("I",0,10);for(var t=e.getImageData(0,0,10,10).data,a=!1,i=3;i<t.length;i+=4)if(t[i]>0&&t[i]<255){a=!0;break}return(0,r.shadow)(this,"isFontSubpixelAAEnabled",a)}},{key:"showText",value:function(e){var t=this.current,a=t.font;if(a.isType3Font)return this.showType3Text(e);var s=t.fontSize;if(0!==s){var l,o=this.ctx,_=t.fontSizeScale,c=t.charSpacing,u=t.wordSpacing,h=t.fontDirection,d=t.textHScale*h,p=e.length,v=a.vertical,f=v?1:-1,m=a.defaultVMetrics,b=s*t.fontMatrix[0],g=t.textRenderingMode===r.TextRenderingMode.FILL&&!a.disableFontFace&&!t.patternFill;if(o.save(),o.transform.apply(o,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(t.textMatrix)),o.translate(t.x,t.y+t.textRise),h>0?o.scale(d,-1):o.scale(d,1),t.patternFill){o.save();var P=t.fillColor.getPattern(o,this,(0,i.getCurrentTransformInverse)(o),n.PathType.FILL);l=(0,i.getCurrentTransform)(o),o.restore(),o.fillStyle=P}var y=t.lineWidth,A=t.textMatrixScale;if(0===A||0===y){var E=t.textRenderingMode&r.TextRenderingMode.FILL_STROKE_MASK;E!==r.TextRenderingMode.STROKE&&E!==r.TextRenderingMode.FILL_STROKE||(y=this.getSinglePixelWidth())}else y/=A;if(1!==_&&(o.scale(_,_),y/=_),o.lineWidth=y,a.isInvalidPDFjsFont){var C,M=[],k=0,O=_createForOfIteratorHelper(e);try{for(O.s();!(C=O.n()).done;){var D=C.value;M.push(D.unicode),k+=D.width}}catch(e){O.e(e)}finally{O.f()}return o.fillText(M.join(""),0,0),t.x+=k*b*d,o.restore(),void this.compose()}var F,S=0;for(F=0;F<p;++F){var T=e[F];if("number"!=typeof T){var I=!1,w=(T.isSpace?u:0)+c,x=T.fontChar,R=T.accent,L=void 0,B=void 0,W=T.width;if(v){var U=T.vmetric||m,G=-(T.vmetric?U[1]:.5*W)*b,K=U[2]*b;W=U?-U[0]:W,L=G/_,B=(S+K)/_}else L=S/_,B=0;if(a.remeasure&&W>0){var N=1e3*o.measureText(x).width/s*_;if(W<N&&this.isFontSubpixelAAEnabled){var j=W/N;I=!0,o.save(),o.scale(j,1),L/=j}else W!==N&&(L+=(W-N)/2e3*s/_)}if(this.contentVisible&&(T.isInFont||a.missingFile))if(g&&!R)o.fillText(x,L,B);else if(this.paintChar(x,L,B,l),R){var H=L+s*R.offset.x/_,q=B-s*R.offset.y/_;this.paintChar(R.fontChar,H,q,l)}S+=v?W*b-w*h:W*b+w*h,I&&o.restore()}else S+=f*T*s/1e3}v?t.y-=S:t.x+=S*d,o.restore(),this.compose()}}},{key:"showType3Text",value:function(e){var t,a,i,n,s=this.ctx,l=this.current,o=l.font,_=l.fontSize,c=l.fontDirection,u=o.vertical?1:-1,h=l.charSpacing,d=l.wordSpacing,p=l.textHScale*c,v=l.fontMatrix||r.FONT_IDENTITY_MATRIX,f=e.length;if(l.textRenderingMode!==r.TextRenderingMode.INVISIBLE&&0!==_){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,s.save(),s.transform.apply(s,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(l.textMatrix)),s.translate(l.x,l.y),s.scale(p,c),t=0;t<f;++t)if("number"!=typeof(a=e[t])){var m=(a.isSpace?d:0)+h,b=o.charProcOperatorList[a.operatorListId];b?(this.contentVisible&&(this.processingType3=a,this.save(),s.scale(_,_),s.transform.apply(s,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(v)),this.executeOperatorList(b),this.restore()),i=r.Util.applyTransform([a.width,0],v)[0]*_+m,s.translate(i,0),l.x+=i*p):(0,r.warn)('Type3 character "'.concat(a.operatorListId,'" is not available.'))}else n=u*a*_/1e3,this.ctx.translate(n,0),l.x+=n*p;s.restore(),this.processingType3=null}}},{key:"setCharWidth",value:function(e,t){}},{key:"setCharWidthAndBounds",value:function(e,t,a,r,i,n){this.ctx.rect(a,r,i-a,n-r),this.ctx.clip(),this.endPath()}},{key:"getColorN_Pattern",value:function(t){var a,r=this;if("TilingPattern"===t[0]){var s=t[1],l=this.baseTransform||(0,i.getCurrentTransform)(this.ctx),o={createCanvasGraphics:function(t){return new e(t,r.commonObjs,r.objs,r.canvasFactory,r.filterFactory,{optionalContentConfig:r.optionalContentConfig,markedContentStack:r.markedContentStack})}};a=new n.TilingPattern(t,s,this.ctx,o,l)}else a=this._getPattern(t[1],t[2]);return a}},{key:"setStrokeColorN",value:function(){this.current.strokeColor=this.getColorN_Pattern(arguments)}},{key:"setFillColorN",value:function(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}},{key:"setStrokeRGBColor",value:function(e,t,a){var i=r.Util.makeHexColor(e,t,a);this.ctx.strokeStyle=i,this.current.strokeColor=i}},{key:"setFillRGBColor",value:function(e,t,a){var i=r.Util.makeHexColor(e,t,a);this.ctx.fillStyle=i,this.current.fillColor=i,this.current.patternFill=!1}},{key:"_getPattern",value:function(e){var t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return this.cachedPatterns.has(e)?t=this.cachedPatterns.get(e):(t=(0,n.getShadingPattern)(this.getObject(e)),this.cachedPatterns.set(e,t)),a&&(t.matrix=a),t}},{key:"shadingFill",value:function(e){if(this.contentVisible){var t=this.ctx;this.save();var a=this._getPattern(e);t.fillStyle=a.getPattern(t,this,(0,i.getCurrentTransformInverse)(t),n.PathType.SHADING);var s=(0,i.getCurrentTransformInverse)(t);if(s){var l=t.canvas,o=l.width,_=l.height,c=r.Util.getAxialAlignedBoundingBox([0,0,o,_],s),u=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(c,4),h=u[0],d=u[1],p=u[2],v=u[3];this.ctx.fillRect(h,d,p-h,v-d)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}}},{key:"beginInlineImage",value:function(){(0,r.unreachable)("Should not call beginInlineImage")}},{key:"beginImageData",value:function(){(0,r.unreachable)("Should not call beginImageData")}},{key:"paintFormXObjectBegin",value:function(e,t){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(e)&&6===e.length&&this.transform.apply(this,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(e)),this.baseTransform=(0,i.getCurrentTransform)(this.ctx),t)){var a=t[2]-t[0],r=t[3]-t[1];this.ctx.rect(t[0],t[1],a,r),this.current.updateRectMinMax((0,i.getCurrentTransform)(this.ctx),t),this.clip(),this.endPath()}}},{key:"paintFormXObjectEnd",value:function(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}},{key:"beginGroup",value:function(e){if(this.contentVisible){this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);var t=this.ctx;e.isolated||(0,r.info)("TODO: Support non-isolated groups."),e.knockout&&(0,r.warn)("Knockout groups not supported.");var a=(0,i.getCurrentTransform)(t);if(e.matrix&&t.transform.apply(t,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(e.matrix)),!e.bbox)throw new Error("Bounding box is required.");var n=r.Util.getAxialAlignedBoundingBox(e.bbox,(0,i.getCurrentTransform)(t)),s=[0,0,t.canvas.width,t.canvas.height];n=r.Util.intersect(n,s)||[0,0,0,0];var o=Math.floor(n[0]),_=Math.floor(n[1]),c=Math.max(Math.ceil(n[2])-o,1),u=Math.max(Math.ceil(n[3])-_,1),h=1,d=1;c>l&&(h=c/l,c=l),u>l&&(d=u/l,u=l),this.current.startNewPathAndClipBox([0,0,c,u]);var v="groupAt"+this.groupLevel;e.smask&&(v+="_smask_"+this.smaskCounter++%2);var f=this.cachedCanvases.getCanvas(v,c,u),m=f.context;m.scale(1/h,1/d),m.translate(-o,-_),m.transform.apply(m,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(a)),e.smask?this.smaskStack.push({canvas:f.canvas,context:m,offsetX:o,offsetY:_,scaleX:h,scaleY:d,subtype:e.smask.subtype,backdrop:e.smask.backdrop,transferMap:e.smask.transferMap||null,startTransformInverse:null}):(t.setTransform(1,0,0,1,0,0),t.translate(o,_),t.scale(h,d),t.save()),p(t,m),this.ctx=m,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(t),this.groupLevel++}}},{key:"endGroup",value:function(e){if(this.contentVisible){this.groupLevel--;var t=this.ctx,a=this.groupStack.pop();if(this.ctx=a,this.ctx.imageSmoothingEnabled=!1,e.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{var n;this.ctx.restore();var s=(0,i.getCurrentTransform)(this.ctx);this.restore(),this.ctx.save(),(n=this.ctx).setTransform.apply(n,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(s));var l=r.Util.getAxialAlignedBoundingBox([0,0,t.canvas.width,t.canvas.height],s);this.ctx.drawImage(t.canvas,0,0),this.ctx.restore(),this.compose(l)}}}},{key:"beginAnnotation",value:function(e,t,a,n,s){var l;if(_assertClassBrand(C,this,k).call(this),v(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&(l=this.ctx).setTransform.apply(l,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(this.baseTransform)),Array.isArray(t)&&4===t.length){var o=t[2]-t[0],_=t[3]-t[1];if(s&&this.annotationCanvasMap){(a=a.slice())[4]-=t[0],a[5]-=t[1],(t=t.slice())[0]=t[1]=0,t[2]=o,t[3]=_;var c=r.Util.singularValueDecompose2dScale((0,i.getCurrentTransform)(this.ctx)),h=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(c,2),d=h[0],p=h[1],f=this.viewportScale,m=Math.ceil(o*this.outputScaleX*f),b=Math.ceil(_*this.outputScaleY*f);this.annotationCanvas=this.canvasFactory.create(m,b);var g=this.annotationCanvas,P=g.canvas,y=g.context;this.annotationCanvasMap.set(e,P),this.annotationCanvas.savedCtx=this.ctx,this.ctx=y,this.ctx.save(),this.ctx.setTransform(d,0,0,-p,0,_*p),v(this.ctx)}else v(this.ctx),this.ctx.rect(t[0],t[1],o,_),this.ctx.clip(),this.endPath()}this.current=new u(this.ctx.canvas.width,this.ctx.canvas.height),this.transform.apply(this,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(a)),this.transform.apply(this,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(n))}},{key:"endAnnotation",value:function(){this.annotationCanvas&&(this.ctx.restore(),_assertClassBrand(C,this,O).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}},{key:"paintImageMaskXObject",value:function(e){if(this.contentVisible){var t=e.count;(e=this.getObject(e.data,e)).count=t;var a=this.ctx,r=this.processingType3;if(r&&(void 0===r.compiled&&(r.compiled=function(e){var t=e.width,a=e.height;if(t>1e3||a>1e3)return null;var r,i,n,s,l=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),o=t+1,_=new Uint8Array(o*(a+1)),c=t+7&-8,u=new Uint8Array(c*a),h=0,d=_createForOfIteratorHelper(e.data);try{for(d.s();!(s=d.n()).done;)for(var p=s.value,v=128;v>0;)u[h++]=p&v?0:255,v>>=1}catch(e){d.e(e)}finally{d.f()}var f=0;for(0!==u[h=0]&&(_[0]=1,++f),i=1;i<t;i++)u[h]!==u[h+1]&&(_[i]=u[h]?2:1,++f),h++;for(0!==u[h]&&(_[i]=2,++f),r=1;r<a;r++){n=r*o,u[(h=r*c)-c]!==u[h]&&(_[n]=u[h]?1:8,++f);var m=(u[h]?4:0)+(u[h-c]?8:0);for(i=1;i<t;i++)l[m=(m>>2)+(u[h+1]?4:0)+(u[h-c+1]?8:0)]&&(_[n+i]=l[m],++f),h++;if(u[h-c]!==u[h]&&(_[n+i]=u[h]?2:4,++f),f>1e3)return null}for(n=r*o,0!==u[h=c*(a-1)]&&(_[n]=8,++f),i=1;i<t;i++)u[h]!==u[h+1]&&(_[n+i]=u[h]?4:8,++f),h++;if(0!==u[h]&&(_[n+i]=4,++f),f>1e3)return null;var b=new Int32Array([0,o,-1,0,-o,0,0,0,1]),g=new Path2D;for(r=0;f&&r<=a;r++){for(var P=r*o,y=P+t;P<y&&!_[P];)P++;if(P!==y){g.moveTo(P%o,r);var A=P,E=_[P];do{var C=b[E];do{P+=C}while(!_[P]);var M=_[P];5!==M&&10!==M?(E=M,_[P]=0):(E=M&51*E>>4,_[P]&=E>>2|E<<2),g.lineTo(P%o,P/o|0),_[P]||--f}while(A!==P);--r}}return u=null,_=null,function(e){e.save(),e.scale(1/t,-1/a),e.translate(0,-a),e.fill(g),e.beginPath(),e.restore()}}(e)),r.compiled))r.compiled(a);else{var i=this._createMaskCanvas(e),n=i.canvas;a.save(),a.setTransform(1,0,0,1,0,0),a.drawImage(n,i.offsetX,i.offsetY),a.restore(),this.compose()}}}},{key:"paintImageMaskXObjectRepeat",value:function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=arguments.length>4?arguments[4]:void 0,l=arguments.length>5?arguments[5]:void 0;if(this.contentVisible){e=this.getObject(e.data,e);var o=this.ctx;o.save();var _=(0,i.getCurrentTransform)(o);o.transform(t,a,n,s,0,0);var c=this._createMaskCanvas(e);o.setTransform(1,0,0,1,c.offsetX-_[4],c.offsetY-_[5]);for(var u=0,h=l.length;u<h;u+=2){var d=r.Util.transform(_,[t,a,n,s,l[u],l[u+1]]),p=r.Util.applyTransform([0,0],d),v=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(p,2),f=v[0],m=v[1];o.drawImage(c.canvas,f,m)}o.restore(),this.compose()}}},{key:"paintImageMaskXObjectGroup",value:function(e){if(this.contentVisible){var t,a=this.ctx,r=this.current.fillColor,s=this.current.patternFill,l=_createForOfIteratorHelper(e);try{for(l.s();!(t=l.n()).done;){var o=t.value,_=o.data,u=o.width,h=o.height,p=o.transform,v=this.cachedCanvases.getCanvas("maskCanvas",u,h),f=v.context;f.save(),d(f,this.getObject(_,o)),f.globalCompositeOperation="source-in",f.fillStyle=s?r.getPattern(f,this,(0,i.getCurrentTransformInverse)(a),n.PathType.FILL):r,f.fillRect(0,0,u,h),f.restore(),a.save(),a.transform.apply(a,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(p)),a.scale(1,-1),c(a,v.canvas,0,0,u,h,0,-1,1,1),a.restore()}}catch(e){l.e(e)}finally{l.f()}this.compose()}}},{key:"paintImageXObject",value:function(e){if(this.contentVisible){var t=this.getObject(e);t?this.paintInlineImageXObject(t):(0,r.warn)("Dependent image isn't ready yet")}}},{key:"paintImageXObjectRepeat",value:function(e,t,a,i){if(this.contentVisible){var n=this.getObject(e);if(n){for(var s=n.width,l=n.height,o=[],_=0,c=i.length;_<c;_+=2)o.push({transform:[t,0,0,a,i[_],i[_+1]],x:0,y:0,w:s,h:l});this.paintInlineImageXObjectGroup(n,o)}else(0,r.warn)("Dependent image isn't ready yet")}}},{key:"applyTransferMapsToCanvas",value:function(e){return"none"!==this.current.transferMaps&&(e.filter=this.current.transferMaps,e.drawImage(e.canvas,0,0),e.filter="none"),e.canvas}},{key:"applyTransferMapsToBitmap",value:function(e){if("none"===this.current.transferMaps)return e.bitmap;var t=e.bitmap,a=e.width,r=e.height,i=this.cachedCanvases.getCanvas("inlineImage",a,r),n=i.context;return n.filter=this.current.transferMaps,n.drawImage(t,0,0),n.filter="none",i.canvas}},{key:"paintInlineImageXObject",value:function(e){if(this.contentVisible){var t,a=e.width,n=e.height,s=this.ctx;if(this.save(),!r.isNodeJS){var l=s.filter;"none"!==l&&""!==l&&(s.filter="none")}if(s.scale(1/a,-1/n),e.bitmap)t=this.applyTransferMapsToBitmap(e);else if("function"==typeof HTMLElement&&e instanceof HTMLElement||!e.data)t=e;else{var o=this.cachedCanvases.getCanvas("inlineImage",a,n).context;h(o,e),t=this.applyTransferMapsToCanvas(o)}var _=this._scaleImage(t,(0,i.getCurrentTransformInverse)(s));s.imageSmoothingEnabled=g((0,i.getCurrentTransform)(s),e.interpolate),c(s,_.img,0,0,_.paintWidth,_.paintHeight,0,-n,a,n),this.compose(),this.restore()}}},{key:"paintInlineImageXObjectGroup",value:function(e,t){if(this.contentVisible){var a,r=this.ctx;if(e.bitmap)a=e.bitmap;else{var i=e.width,n=e.height,s=this.cachedCanvases.getCanvas("inlineImage",i,n).context;h(s,e),a=this.applyTransferMapsToCanvas(s)}var l,o=_createForOfIteratorHelper(t);try{for(o.s();!(l=o.n()).done;){var _=l.value;r.save(),r.transform.apply(r,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(_.transform)),r.scale(1,-1),c(r,a,_.x,_.y,_.w,_.h,0,-1,1,1),r.restore()}}catch(e){o.e(e)}finally{o.f()}this.compose()}}},{key:"paintSolidColorImageMask",value:function(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}},{key:"markPoint",value:function(e){}},{key:"markPointProps",value:function(e,t){}},{key:"beginMarkedContent",value:function(e){this.markedContentStack.push({visible:!0})}},{key:"beginMarkedContentProps",value:function(e,t){"OC"===e?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(t)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}},{key:"endMarkedContent",value:function(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}},{key:"beginCompat",value:function(){}},{key:"endCompat",value:function(){}},{key:"consumePath",value:function(e){var t=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(e);var a=this.ctx;this.pendingClip&&(t||(this.pendingClip===E?a.clip("evenodd"):a.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),a.beginPath()}},{key:"getSinglePixelWidth",value:function(){if(!this._cachedGetSinglePixelWidth){var e=(0,i.getCurrentTransform)(this.ctx);if(0===e[1]&&0===e[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(e[0]),Math.abs(e[3]));else{var t=Math.abs(e[0]*e[3]-e[2]*e[1]),a=Math.hypot(e[0],e[2]),r=Math.hypot(e[1],e[3]);this._cachedGetSinglePixelWidth=Math.max(a,r)/t}}return this._cachedGetSinglePixelWidth}},{key:"getScaleForStroking",value:function(){if(-1===this._cachedScaleForStroking[0]){var e,t,a=this.current.lineWidth,r=this.ctx.getTransform(),i=r.a,n=r.b,s=r.c,l=r.d;if(0===n&&0===s){var o=Math.abs(i),_=Math.abs(l);if(o===_)if(0===a)e=t=1/o;else{var c=o*a;e=t=c<1?1/c:1}else if(0===a)e=1/o,t=1/_;else{var u=o*a,h=_*a;e=u<1?1/u:1,t=h<1?1/h:1}}else{var d=Math.abs(i*l-n*s),p=Math.hypot(i,n),v=Math.hypot(s,l);if(0===a)e=v/d,t=p/d;else{var f=a*d;e=v>f?v/f:1,t=p>f?p/f:1}}this._cachedScaleForStroking[0]=e,this._cachedScaleForStroking[1]=t}return this._cachedScaleForStroking}},{key:"rescaleAndStroke",value:function(e){var t=this.ctx,a=this.current.lineWidth,r=this.getScaleForStroking(),i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(r,2),n=i[0],s=i[1];if(t.lineWidth=a||1,1!==n||1!==s){var l=t.getLineDash();if(e&&t.save(),t.scale(n,s),l.length>0){var o=Math.max(n,s);t.setLineDash(l.map((function(e){return e/o}))),t.lineDashOffset/=o}t.stroke(),e&&t.restore()}else t.stroke()}},{key:"isContentVisible",value:function(){for(var e=this.markedContentStack.length-1;e>=0;e--)if(!this.markedContentStack[e].visible)return!1;return!0}}])}();function k(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)}function O(){if(this.pageColors){var e=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==e){var t=this.ctx.filter;this.ctx.filter=e,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=t}}}for(var D in t.CanvasGraphics=M,r.OPS)void 0!==M.prototype[D]&&(M.prototype[r.OPS[D]]=M.prototype[D])},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.TilingPattern=t.PathType=void 0,t.getShadingPattern=function(e){switch(e[0]){case"RadialAxial":return new o(e);case"Mesh":return new u(e);case"Dummy":return new h}throw new Error("Unknown IR type: ".concat(e[0]))};var r=a(1),i=a(6),n={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function s(e,t){if(t){var a=t[2]-t[0],r=t[3]-t[1],i=new Path2D;i.rect(t[0],t[1],a,r),e.clip(i)}}t.PathType=n;var l=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.constructor===e&&(0,r.unreachable)("Cannot initialize BaseShadingPattern.")}),[{key:"getPattern",value:function(){(0,r.unreachable)("Abstract method `getPattern` called.")}}])}(),o=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(a=_callSuper(this,t))._type=e[1],a._bbox=e[2],a._colorStops=e[3],a._p0=e[4],a._p1=e[5],a._r0=e[6],a._r1=e[7],a.matrix=null,a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"_createGradient",value:function(e){var t;"axial"===this._type?t=e.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(t=e.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));var a,r=_createForOfIteratorHelper(this._colorStops);try{for(r.s();!(a=r.n()).done;){var i=a.value;t.addColorStop(i[0],i[1])}}catch(e){r.e(e)}finally{r.f()}return t}},{key:"getPattern",value:function(e,t,a,l){var o;if(l===n.STROKE||l===n.FILL){var _=t.current.getClippedPathBoundingBox(l,(0,i.getCurrentTransform)(e))||[0,0,0,0],c=Math.ceil(_[2]-_[0])||1,u=Math.ceil(_[3]-_[1])||1,h=t.cachedCanvases.getCanvas("pattern",c,u,!0),d=h.context;d.clearRect(0,0,d.canvas.width,d.canvas.height),d.beginPath(),d.rect(0,0,d.canvas.width,d.canvas.height),d.translate(-_[0],-_[1]),a=r.Util.transform(a,[1,0,0,1,_[0],_[1]]),d.transform.apply(d,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(t.baseTransform)),this.matrix&&d.transform.apply(d,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(this.matrix)),s(d,this._bbox),d.fillStyle=this._createGradient(d),d.fill(),o=e.createPattern(h.canvas,"no-repeat");var p=new DOMMatrix(a);o.setTransform(p)}else s(e,this._bbox),o=this._createGradient(e);return o}}])}(l);function _(e,t,a,r,i,n,s,l){var o,_=t.coords,c=t.colors,u=e.data,h=4*e.width;_[a+1]>_[r+1]&&(o=a,a=r,r=o,o=n,n=s,s=o),_[r+1]>_[i+1]&&(o=r,r=i,i=o,o=s,s=l,l=o),_[a+1]>_[r+1]&&(o=a,a=r,r=o,o=n,n=s,s=o);var d=(_[a]+t.offsetX)*t.scaleX,p=(_[a+1]+t.offsetY)*t.scaleY,v=(_[r]+t.offsetX)*t.scaleX,f=(_[r+1]+t.offsetY)*t.scaleY,m=(_[i]+t.offsetX)*t.scaleX,b=(_[i+1]+t.offsetY)*t.scaleY;if(!(p>=b))for(var g,P,y,A,E,C,M,k,O=c[n],D=c[n+1],F=c[n+2],S=c[s],T=c[s+1],I=c[s+2],w=c[l],x=c[l+1],R=c[l+2],L=Math.round(p),B=Math.round(b),W=L;W<=B;W++){if(W<f){var U=W<p?0:(p-W)/(p-f);g=d-(d-v)*U,P=O-(O-S)*U,y=D-(D-T)*U,A=F-(F-I)*U}else{var G;g=v-(v-m)*(G=W>b?1:f===b?0:(f-W)/(f-b)),P=S-(S-w)*G,y=T-(T-x)*G,A=I-(I-R)*G}var K=void 0;E=d-(d-m)*(K=W<p?0:W>b?1:(p-W)/(p-b)),C=O-(O-w)*K,M=D-(D-x)*K,k=F-(F-R)*K;for(var N=Math.round(Math.min(g,E)),j=Math.round(Math.max(g,E)),H=h*W+4*N,q=N;q<=j;q++)(K=(g-q)/(g-E))<0?K=0:K>1&&(K=1),u[H++]=P-(P-C)*K|0,u[H++]=y-(y-M)*K|0,u[H++]=A-(A-k)*K|0,u[H++]=255}}function c(e,t,a){var r,i,n=t.coords,s=t.colors;switch(t.type){case"lattice":var l=t.verticesPerRow,o=Math.floor(n.length/l)-1,c=l-1;for(r=0;r<o;r++)for(var u=r*l,h=0;h<c;h++,u++)_(e,a,n[u],n[u+1],n[u+l],s[u],s[u+1],s[u+l]),_(e,a,n[u+l+1],n[u+1],n[u+l],s[u+l+1],s[u+1],s[u+l]);break;case"triangles":for(r=0,i=n.length;r<i;r+=3)_(e,a,n[r],n[r+1],n[r+2],s[r],s[r+1],s[r+2]);break;default:throw new Error("illegal figure")}}var u=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(a=_callSuper(this,t))._coords=e[2],a._colors=e[3],a._figures=e[4],a._bounds=e[5],a._bbox=e[7],a._background=e[8],a.matrix=null,a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"_createMeshCanvas",value:function(e,t,a){var r=Math.floor(this._bounds[0]),i=Math.floor(this._bounds[1]),n=Math.ceil(this._bounds[2])-r,s=Math.ceil(this._bounds[3])-i,l=Math.min(Math.ceil(Math.abs(n*e[0]*1.1)),3e3),o=Math.min(Math.ceil(Math.abs(s*e[1]*1.1)),3e3),_=n/l,u=s/o,h={coords:this._coords,colors:this._colors,offsetX:-r,offsetY:-i,scaleX:1/_,scaleY:1/u},d=l+4,p=o+4,v=a.getCanvas("mesh",d,p,!1),f=v.context,m=f.createImageData(l,o);if(t)for(var b=m.data,g=0,P=b.length;g<P;g+=4)b[g]=t[0],b[g+1]=t[1],b[g+2]=t[2],b[g+3]=255;var y,A=_createForOfIteratorHelper(this._figures);try{for(A.s();!(y=A.n()).done;)c(m,y.value,h)}catch(e){A.e(e)}finally{A.f()}return f.putImageData(m,2,2),{canvas:v.canvas,offsetX:r-2*_,offsetY:i-2*u,scaleX:_,scaleY:u}}},{key:"getPattern",value:function(e,t,a,l){var o;if(s(e,this._bbox),l===n.SHADING)o=r.Util.singularValueDecompose2dScale((0,i.getCurrentTransform)(e));else if(o=r.Util.singularValueDecompose2dScale(t.baseTransform),this.matrix){var _=r.Util.singularValueDecompose2dScale(this.matrix);o=[o[0]*_[0],o[1]*_[1]]}var c=this._createMeshCanvas(o,l===n.SHADING?null:this._background,t.cachedCanvases);return l!==n.SHADING&&(e.setTransform.apply(e,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(t.baseTransform)),this.matrix&&e.transform.apply(e,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(this.matrix))),e.translate(c.offsetX,c.offsetY),e.scale(c.scaleX,c.scaleY),e.createPattern(c.canvas,"no-repeat")}}])}(l),h=function(e){function t(){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,arguments)}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"getPattern",value:function(){return"hotpink"}}])}(l),d=function(){function e(t,a,r,i,n){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.operatorList=t[2],this.matrix=t[3]||[1,0,0,1,0,0],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.color=a,this.ctx=r,this.canvasGraphicsFactory=i,this.baseTransform=n}return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(e,[{key:"createPatternCanvas",value:function(e){var t=this.operatorList,a=this.bbox,n=this.xstep,s=this.ystep,l=this.paintType,o=this.tilingType,_=this.color,c=this.canvasGraphicsFactory;(0,r.info)("TilingType: "+o);var u=a[0],h=a[1],d=a[2],p=a[3],v=r.Util.singularValueDecompose2dScale(this.matrix),f=r.Util.singularValueDecompose2dScale(this.baseTransform),m=[v[0]*f[0],v[1]*f[1]],b=this.getSizeAndScale(n,this.ctx.canvas.width,m[0]),g=this.getSizeAndScale(s,this.ctx.canvas.height,m[1]),P=e.cachedCanvases.getCanvas("pattern",b.size,g.size,!0),y=P.context,A=c.createCanvasGraphics(y);A.groupLevel=e.groupLevel,this.setFillAndStrokeStyleToContext(A,l,_);var E=u,C=h,M=d,k=p;return u<0&&(E=0,M+=Math.abs(u)),h<0&&(C=0,k+=Math.abs(h)),y.translate(-b.scale*E,-g.scale*C),A.transform(b.scale,0,0,g.scale,0,0),y.save(),this.clipBbox(A,E,C,M,k),A.baseTransform=(0,i.getCurrentTransform)(A.ctx),A.executeOperatorList(t),A.endDrawing(),{canvas:P.canvas,scaleX:b.scale,scaleY:g.scale,offsetX:E,offsetY:C}}},{key:"getSizeAndScale",value:function(t,a,r){t=Math.abs(t);var i=Math.max(e.MAX_PATTERN_SIZE,a),n=Math.ceil(t*r);return n>=i?n=i:r=n/t,{scale:r,size:n}}},{key:"clipBbox",value:function(e,t,a,r,n){var s=r-t,l=n-a;e.ctx.rect(t,a,s,l),e.current.updateRectMinMax((0,i.getCurrentTransform)(e.ctx),[t,a,r,n]),e.clip(),e.endPath()}},{key:"setFillAndStrokeStyleToContext",value:function(e,t,a){var i=e.ctx,n=e.current;switch(t){case 1:var s=this.ctx;i.fillStyle=s.fillStyle,i.strokeStyle=s.strokeStyle,n.fillColor=s.fillStyle,n.strokeColor=s.strokeStyle;break;case 2:var l=r.Util.makeHexColor(a[0],a[1],a[2]);i.fillStyle=l,i.strokeStyle=l,n.fillColor=l,n.strokeColor=l;break;default:throw new r.FormatError("Unsupported paint type: ".concat(t))}}},{key:"getPattern",value:function(e,t,a,i){var s=a;i!==n.SHADING&&(s=r.Util.transform(s,t.baseTransform),this.matrix&&(s=r.Util.transform(s,this.matrix)));var l=this.createPatternCanvas(t),o=new DOMMatrix(s);o=(o=o.translate(l.offsetX,l.offsetY)).scale(1/l.scaleX,1/l.scaleY);var _=e.createPattern(l.canvas,"repeat");return _.setTransform(o),_}}])}();(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(d,"MAX_PATTERN_SIZE",3e3),t.TilingPattern=d},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.convertBlackAndWhiteToRGBA=i,t.convertToRGBA=function(e){switch(e.kind){case r.ImageKind.GRAYSCALE_1BPP:return i(e);case r.ImageKind.RGB_24BPP:return function(e){var t=e.src,a=e.srcPos,i=void 0===a?0:a,n=e.dest,s=e.destPos,l=void 0===s?0:s,o=(e.width,e.height,0),_=t.length>>2,c=new Uint32Array(t.buffer,i,_);if(r.FeatureTest.isLittleEndian){for(;o<_-2;o+=3,l+=4){var u=c[o],h=c[o+1],d=c[o+2];n[l]=4278190080|u,n[l+1]=u>>>24|h<<8|4278190080,n[l+2]=h>>>16|d<<16|4278190080,n[l+3]=d>>>8|4278190080}for(var p=4*o,v=t.length;p<v;p+=3)n[l++]=t[p]|t[p+1]<<8|t[p+2]<<16|4278190080}else{for(;o<_-2;o+=3,l+=4){var f=c[o],m=c[o+1],b=c[o+2];n[l]=255|f,n[l+1]=f<<24|m>>>8|255,n[l+2]=m<<16|b>>>16|255,n[l+3]=b<<8|255}for(var g=4*o,P=t.length;g<P;g+=3)n[l++]=t[g]<<24|t[g+1]<<16|t[g+2]<<8|255}return{srcPos:i,destPos:l}}(e)}return null},t.grayToRGBA=function(e,t){if(r.FeatureTest.isLittleEndian)for(var a=0,i=e.length;a<i;a++)t[a]=65793*e[a]|4278190080;else for(var n=0,s=e.length;n<s;n++)t[n]=16843008*e[n]|255};var r=a(1);function i(e){var t=e.src,a=e.srcPos,i=void 0===a?0:a,n=e.dest,s=e.width,l=e.height,o=e.nonBlackColor,_=void 0===o?4294967295:o,c=e.inverseDecode,u=void 0!==c&&c,h=r.FeatureTest.isLittleEndian?4278190080:255,d=u?[_,h]:[h,_],p=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(d,2),v=p[0],f=p[1],m=s>>3,b=7&s,g=t.length;n=new Uint32Array(n.buffer);for(var P=0,y=0;y<l;y++){for(var A=i+m;i<A;i++){var E=i<g?t[i]:255;n[P++]=128&E?f:v,n[P++]=64&E?f:v,n[P++]=32&E?f:v,n[P++]=16&E?f:v,n[P++]=8&E?f:v,n[P++]=4&E?f:v,n[P++]=2&E?f:v,n[P++]=1&E?f:v}if(0!==b)for(var C=i<g?t[i++]:255,M=0;M<b;M++)n[P++]=C&1<<7-M?f:v}return{srcPos:i,destPos:P}}},function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.GlobalWorkerOptions=void 0;var a=Object.create(null);t.GlobalWorkerOptions=a,a.workerPort=null,a.workerSrc=""},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.MessageHandler=void 0;var r=a(1);function i(e){switch(e instanceof Error||"object"===(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(e)&&null!==e||(0,r.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),e.name){case"AbortException":return new r.AbortException(e.message);case"MissingPDFException":return new r.MissingPDFException(e.message);case"PasswordException":return new r.PasswordException(e.message,e.code);case"UnexpectedResponseException":return new r.UnexpectedResponseException(e.message,e.status);case"UnknownErrorException":return new r.UnknownErrorException(e.message,e.details);default:return new r.UnknownErrorException(e.message,e.toString())}}var n=new WeakSet,s=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a,r){var s=this;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,n),this.sourceName=t,this.targetName=a,this.comObj=r,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=function(e){var t=e.data;if(t.targetName===s.sourceName)if(t.stream)_assertClassBrand(n,s,o).call(s,t);else if(t.callback){var a=t.callbackId,_=s.callbackCapabilities[a];if(!_)throw new Error("Cannot resolve callback ".concat(a));if(delete s.callbackCapabilities[a],1===t.callback)_.resolve(t.data);else{if(2!==t.callback)throw new Error("Unexpected callback case");_.reject(i(t.reason))}}else{var c=s.actionHandler[t.action];if(!c)throw new Error("Unknown action from worker: ".concat(t.action));if(t.callbackId){var u=s.sourceName,h=t.sourceName;new Promise((function(e){e(c(t.data))})).then((function(e){r.postMessage({sourceName:u,targetName:h,callback:1,callbackId:t.callbackId,data:e})}),(function(e){r.postMessage({sourceName:u,targetName:h,callback:2,callbackId:t.callbackId,reason:i(e)})}))}else t.streamId?_assertClassBrand(n,s,l).call(s,t):c(t.data)}},r.addEventListener("message",this._onComObjOnMessage)}),[{key:"on",value:function(e,t){var a=this.actionHandler;if(a[e])throw new Error('There is already an actionName called "'.concat(e,'"'));a[e]=t}},{key:"send",value:function(e,t,a){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:e,data:t},a)}},{key:"sendWithPromise",value:function(e,t,a){var i=this.callbackId++,n=new r.PromiseCapability;this.callbackCapabilities[i]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:e,callbackId:i,data:t},a)}catch(e){n.reject(e)}return n.promise}},{key:"sendWithStream",value:function(e,t,a,n){var s=this,l=this.streamId++,o=this.sourceName,_=this.targetName,c=this.comObj;return new ReadableStream({start:function(a){var i=new r.PromiseCapability;return s.streamControllers[l]={controller:a,startCall:i,pullCall:null,cancelCall:null,isClosed:!1},c.postMessage({sourceName:o,targetName:_,action:e,streamId:l,data:t,desiredSize:a.desiredSize},n),i.promise},pull:function(e){var t=new r.PromiseCapability;return s.streamControllers[l].pullCall=t,c.postMessage({sourceName:o,targetName:_,stream:6,streamId:l,desiredSize:e.desiredSize}),t.promise},cancel:function(e){(0,r.assert)(e instanceof Error,"cancel must have a valid reason");var t=new r.PromiseCapability;return s.streamControllers[l].cancelCall=t,s.streamControllers[l].isClosed=!0,c.postMessage({sourceName:o,targetName:_,stream:1,streamId:l,reason:i(e)}),t.promise}},a)}},{key:"destroy",value:function(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}])}();function l(e){var t=e.streamId,a=this.sourceName,n=e.sourceName,s=this.comObj,l=this,o=this.actionHandler[e.action],_={enqueue:function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,l=arguments.length>2?arguments[2]:void 0;if(!this.isCancelled){var o=this.desiredSize;this.desiredSize-=i,o>0&&this.desiredSize<=0&&(this.sinkCapability=new r.PromiseCapability,this.ready=this.sinkCapability.promise),s.postMessage({sourceName:a,targetName:n,stream:4,streamId:t,chunk:e},l)}},close:function(){this.isCancelled||(this.isCancelled=!0,s.postMessage({sourceName:a,targetName:n,stream:3,streamId:t}),delete l.streamSinks[t])},error:function(e){(0,r.assert)(e instanceof Error,"error must have a valid reason"),this.isCancelled||(this.isCancelled=!0,s.postMessage({sourceName:a,targetName:n,stream:5,streamId:t,reason:i(e)}))},sinkCapability:new r.PromiseCapability,onPull:null,onCancel:null,isCancelled:!1,desiredSize:e.desiredSize,ready:null};_.sinkCapability.resolve(),_.ready=_.sinkCapability.promise,this.streamSinks[t]=_,new Promise((function(t){t(o(e.data,_))})).then((function(){s.postMessage({sourceName:a,targetName:n,stream:8,streamId:t,success:!0})}),(function(e){s.postMessage({sourceName:a,targetName:n,stream:8,streamId:t,reason:i(e)})}))}function o(e){var t=e.streamId,a=this.sourceName,s=e.sourceName,l=this.comObj,o=this.streamControllers[t],c=this.streamSinks[t];switch(e.stream){case 8:e.success?o.startCall.resolve():o.startCall.reject(i(e.reason));break;case 7:e.success?o.pullCall.resolve():o.pullCall.reject(i(e.reason));break;case 6:if(!c){l.postMessage({sourceName:a,targetName:s,stream:7,streamId:t,success:!0});break}c.desiredSize<=0&&e.desiredSize>0&&c.sinkCapability.resolve(),c.desiredSize=e.desiredSize,new Promise((function(e){var t;e(null===(t=c.onPull)||void 0===t?void 0:t.call(c))})).then((function(){l.postMessage({sourceName:a,targetName:s,stream:7,streamId:t,success:!0})}),(function(e){l.postMessage({sourceName:a,targetName:s,stream:7,streamId:t,reason:i(e)})}));break;case 4:if((0,r.assert)(o,"enqueue should have stream controller"),o.isClosed)break;o.controller.enqueue(e.chunk);break;case 3:if((0,r.assert)(o,"close should have stream controller"),o.isClosed)break;o.isClosed=!0,o.controller.close(),_assertClassBrand(n,this,_).call(this,o,t);break;case 5:(0,r.assert)(o,"error should have stream controller"),o.controller.error(i(e.reason)),_assertClassBrand(n,this,_).call(this,o,t);break;case 2:e.success?o.cancelCall.resolve():o.cancelCall.reject(i(e.reason)),_assertClassBrand(n,this,_).call(this,o,t);break;case 1:if(!c)break;new Promise((function(t){var a;t(null===(a=c.onCancel)||void 0===a?void 0:a.call(c,i(e.reason)))})).then((function(){l.postMessage({sourceName:a,targetName:s,stream:2,streamId:t,success:!0})}),(function(e){l.postMessage({sourceName:a,targetName:s,stream:2,streamId:t,reason:i(e)})})),c.sinkCapability.reject(i(e.reason)),c.isCancelled=!0,delete this.streamSinks[t];break;default:throw new Error("Unexpected stream case")}}function _(e,t){return c.apply(this,arguments)}function c(){return(c=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t,a){var r,i,n;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=1,Promise.allSettled([null===(r=t.startCall)||void 0===r?void 0:r.promise,null===(i=t.pullCall)||void 0===i?void 0:i.promise,null===(n=t.cancelCall)||void 0===n?void 0:n.promise]);case 1:delete this.streamControllers[a];case 2:case"end":return e.stop()}}),e,this)})))).apply(this,arguments)}t.MessageHandler=s},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.Metadata=void 0;var r=a(1),i=new WeakMap,n=new WeakMap,s=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a=t.parsedData,r=t.rawData;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateFieldInitSpec(this,i,void 0),_classPrivateFieldInitSpec(this,n,void 0),_classPrivateFieldSet(i,this,a),_classPrivateFieldSet(n,this,r)}),[{key:"getRaw",value:function(){return _classPrivateFieldGet(n,this)}},{key:"get",value:function(e){var t;return null!==(t=_classPrivateFieldGet(i,this).get(e))&&void 0!==t?t:null}},{key:"getAll",value:function(){return(0,r.objectFromMap)(_classPrivateFieldGet(i,this))}},{key:"has",value:function(e){return _classPrivateFieldGet(i,this).has(e)}}])}();t.Metadata=s},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.OptionalContentConfig=void 0;var r=a(1),i=a(8),n=Symbol("INTERNAL"),s=new WeakMap,l=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateFieldInitSpec(this,s,!0),this.name=t,this.intent=a}),[{key:"visible",get:function(){return _classPrivateFieldGet(s,this)}},{key:"_setVisible",value:function(e,t){e!==n&&(0,r.unreachable)("Internal method `_setVisible` called."),_classPrivateFieldSet(s,this,t)}}])}(),o=new WeakMap,_=new WeakMap,c=new WeakMap,u=new WeakMap,h=new WeakSet,d=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){if((0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,h),_classPrivateFieldInitSpec(this,o,null),_classPrivateFieldInitSpec(this,_,new Map),_classPrivateFieldInitSpec(this,c,null),_classPrivateFieldInitSpec(this,u,null),this.name=null,this.creator=null,null!==t){this.name=t.name,this.creator=t.creator,_classPrivateFieldSet(u,this,t.order);var a,r=_createForOfIteratorHelper(t.groups);try{for(r.s();!(a=r.n()).done;){var i=a.value;_classPrivateFieldGet(_,this).set(i.id,new l(i.name,i.intent))}}catch(e){r.e(e)}finally{r.f()}if("OFF"===t.baseState){var s,d=_createForOfIteratorHelper(_classPrivateFieldGet(_,this).values());try{for(d.s();!(s=d.n()).done;)s.value._setVisible(n,!1)}catch(e){d.e(e)}finally{d.f()}}var p,v=_createForOfIteratorHelper(t.on);try{for(v.s();!(p=v.n()).done;){var f=p.value;_classPrivateFieldGet(_,this).get(f)._setVisible(n,!0)}}catch(e){v.e(e)}finally{v.f()}var m,b=_createForOfIteratorHelper(t.off);try{for(b.s();!(m=b.n()).done;){var g=m.value;_classPrivateFieldGet(_,this).get(g)._setVisible(n,!1)}}catch(e){b.e(e)}finally{b.f()}_classPrivateFieldSet(c,this,this.getHash())}}),[{key:"isVisible",value:function(e){if(0===_classPrivateFieldGet(_,this).size)return!0;if(!e)return(0,r.warn)("Optional content group not defined."),!0;if("OCG"===e.type)return _classPrivateFieldGet(_,this).has(e.id)?_classPrivateFieldGet(_,this).get(e.id).visible:((0,r.warn)("Optional content group not found: ".concat(e.id)),!0);if("OCMD"===e.type){if(e.expression)return _assertClassBrand(h,this,p).call(this,e.expression);if(!e.policy||"AnyOn"===e.policy){var t,a=_createForOfIteratorHelper(e.ids);try{for(a.s();!(t=a.n()).done;){var i=t.value;if(!_classPrivateFieldGet(_,this).has(i))return(0,r.warn)("Optional content group not found: ".concat(i)),!0;if(_classPrivateFieldGet(_,this).get(i).visible)return!0}}catch(e){a.e(e)}finally{a.f()}return!1}if("AllOn"===e.policy){var n,s=_createForOfIteratorHelper(e.ids);try{for(s.s();!(n=s.n()).done;){var l=n.value;if(!_classPrivateFieldGet(_,this).has(l))return(0,r.warn)("Optional content group not found: ".concat(l)),!0;if(!_classPrivateFieldGet(_,this).get(l).visible)return!1}}catch(e){s.e(e)}finally{s.f()}return!0}if("AnyOff"===e.policy){var o,c=_createForOfIteratorHelper(e.ids);try{for(c.s();!(o=c.n()).done;){var u=o.value;if(!_classPrivateFieldGet(_,this).has(u))return(0,r.warn)("Optional content group not found: ".concat(u)),!0;if(!_classPrivateFieldGet(_,this).get(u).visible)return!0}}catch(e){c.e(e)}finally{c.f()}return!1}if("AllOff"===e.policy){var d,v=_createForOfIteratorHelper(e.ids);try{for(v.s();!(d=v.n()).done;){var f=d.value;if(!_classPrivateFieldGet(_,this).has(f))return(0,r.warn)("Optional content group not found: ".concat(f)),!0;if(_classPrivateFieldGet(_,this).get(f).visible)return!1}}catch(e){v.e(e)}finally{v.f()}return!0}return(0,r.warn)("Unknown optional content policy ".concat(e.policy,".")),!0}return(0,r.warn)("Unknown group type ".concat(e.type,".")),!0}},{key:"setVisibility",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];_classPrivateFieldGet(_,this).has(e)?(_classPrivateFieldGet(_,this).get(e)._setVisible(n,!!t),_classPrivateFieldSet(o,this,null)):(0,r.warn)("Optional content group not found: ".concat(e))}},{key:"hasInitialVisibility",get:function(){return null===_classPrivateFieldGet(c,this)||this.getHash()===_classPrivateFieldGet(c,this)}},{key:"getOrder",value:function(){return _classPrivateFieldGet(_,this).size?_classPrivateFieldGet(u,this)?_classPrivateFieldGet(u,this).slice():(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(_classPrivateFieldGet(_,this).keys()):null}},{key:"getGroups",value:function(){return _classPrivateFieldGet(_,this).size>0?(0,r.objectFromMap)(_classPrivateFieldGet(_,this)):null}},{key:"getGroup",value:function(e){return _classPrivateFieldGet(_,this).get(e)||null}},{key:"getHash",value:function(){if(null!==_classPrivateFieldGet(o,this))return _classPrivateFieldGet(o,this);var e,t=new i.MurmurHash3_64,a=_createForOfIteratorHelper(_classPrivateFieldGet(_,this));try{for(a.s();!(e=a.n()).done;){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e.value,2),n=r[0],s=r[1];t.update("".concat(n,":").concat(s.visible))}}catch(e){a.e(e)}finally{a.f()}return _classPrivateFieldSet(o,this,t.hexdigest())}}])}();function p(e){var t=e.length;if(t<2)return!0;for(var a=e[0],i=1;i<t;i++){var n=e[i],s=void 0;if(Array.isArray(n))s=_assertClassBrand(h,this,p).call(this,n);else{if(!_classPrivateFieldGet(_,this).has(n))return(0,r.warn)("Optional content group not found: ".concat(n)),!0;s=_classPrivateFieldGet(_,this).get(n).visible}switch(a){case"And":if(!s)return!1;break;case"Or":if(s)return!0;break;case"Not":return!s;default:return!0}}return"And"===a}t.OptionalContentConfig=d},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.PDFDataTransportStream=void 0;var r=a(1),i=a(6),n=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a){var i=this,n=t.length,s=t.initialData,l=t.progressiveDone,o=void 0!==l&&l,_=t.contentDispositionFilename,c=void 0===_?null:_,u=t.disableRange,h=void 0!==u&&u,d=t.disableStream,p=void 0!==d&&d;if((0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),(0,r.assert)(a,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.'),this._queuedChunks=[],this._progressiveDone=o,this._contentDispositionFilename=c,(null==s?void 0:s.length)>0){var v=s instanceof Uint8Array&&s.byteLength===s.buffer.byteLength?s.buffer:new Uint8Array(s).buffer;this._queuedChunks.push(v)}this._pdfDataRangeTransport=a,this._isStreamingSupported=!p,this._isRangeSupported=!h,this._contentLength=n,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener((function(e,t){i._onReceiveData({begin:e,chunk:t})})),this._pdfDataRangeTransport.addProgressListener((function(e,t){i._onProgress({loaded:e,total:t})})),this._pdfDataRangeTransport.addProgressiveReadListener((function(e){i._onReceiveData({chunk:e})})),this._pdfDataRangeTransport.addProgressiveDoneListener((function(){i._onProgressiveDone()})),this._pdfDataRangeTransport.transportReady()}),[{key:"_onReceiveData",value:function(e){var t=e.begin,a=e.chunk,i=a instanceof Uint8Array&&a.byteLength===a.buffer.byteLength?a.buffer:new Uint8Array(a).buffer;if(void 0===t)this._fullRequestReader?this._fullRequestReader._enqueue(i):this._queuedChunks.push(i);else{var n=this._rangeReaders.some((function(e){return e._begin===t&&(e._enqueue(i),!0)}));(0,r.assert)(n,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}},{key:"_progressiveDataLength",get:function(){var e,t;return null!==(e=null===(t=this._fullRequestReader)||void 0===t?void 0:t._loaded)&&void 0!==e?e:0}},{key:"_onProgress",value:function(e){var t,a,r,i;void 0===e.total?null===(t=this._rangeReaders[0])||void 0===t||null===(a=t.onProgress)||void 0===a||a.call(t,{loaded:e.loaded}):null===(r=this._fullRequestReader)||void 0===r||null===(i=r.onProgress)||void 0===i||i.call(r,{loaded:e.loaded,total:e.total})}},{key:"_onProgressiveDone",value:function(){var e;null===(e=this._fullRequestReader)||void 0===e||e.progressiveDone(),this._progressiveDone=!0}},{key:"_removeRangeReader",value:function(e){var t=this._rangeReaders.indexOf(e);t>=0&&this._rangeReaders.splice(t,1)}},{key:"getFullReader",value:function(){(0,r.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");var e=this._queuedChunks;return this._queuedChunks=null,new s(this,e,this._progressiveDone,this._contentDispositionFilename)}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var a=new l(this,e,t);return this._pdfDataRangeTransport.requestDataRange(e,t),this._rangeReaders.push(a),a}},{key:"cancelAllRequests",value:function(e){var t;null===(t=this._fullRequestReader)||void 0===t||t.cancel(e);var a,r=_createForOfIteratorHelper(this._rangeReaders.slice(0));try{for(r.s();!(a=r.n()).done;)a.value.cancel(e)}catch(e){r.e(e)}finally{r.f()}this._pdfDataRangeTransport.abort()}}])}();t.PDFDataTransportStream=n;var s=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._stream=t,this._done=r||!1,this._filename=(0,i.isPdfFile)(n)?n:null,this._queuedChunks=a||[],this._loaded=0;var s,l=_createForOfIteratorHelper(this._queuedChunks);try{for(l.s();!(s=l.n()).done;){var o=s.value;this._loaded+=o.byteLength}}catch(e){l.e(e)}finally{l.f()}this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}),[{key:"_enqueue",value:function(e){this._done||(this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunks.push(e),this._loaded+=e.byteLength)}},{key:"headersReady",get:function(){return this._headersReady}},{key:"filename",get:function(){return this._filename}},{key:"isRangeSupported",get:function(){return this._stream._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._stream._isStreamingSupported}},{key:"contentLength",get:function(){return this._stream._contentLength}},{key:"read",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(this._queuedChunks.length>0)){e.next=1;break}return t=this._queuedChunks.shift(),e.abrupt("return",{value:t,done:!1});case 1:if(!this._done){e.next=2;break}return e.abrupt("return",{value:void 0,done:!0});case 2:return a=new r.PromiseCapability,this._requests.push(a),e.abrupt("return",a.promise);case 3:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"cancel",value:function(e){this._done=!0;var t,a=_createForOfIteratorHelper(this._requests);try{for(a.s();!(t=a.n()).done;)t.value.resolve({value:void 0,done:!0})}catch(e){a.e(e)}finally{a.f()}this._requests.length=0}},{key:"progressiveDone",value:function(){this._done||(this._done=!0)}}]);var e}(),l=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a,r){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._stream=t,this._begin=a,this._end=r,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}),[{key:"_enqueue",value:function(e){if(!this._done){if(0===this._requests.length)this._queuedChunk=e;else{this._requests.shift().resolve({value:e,done:!1});var t,a=_createForOfIteratorHelper(this._requests);try{for(a.s();!(t=a.n()).done;)t.value.resolve({value:void 0,done:!0})}catch(e){a.e(e)}finally{a.f()}this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}},{key:"isStreamingSupported",get:function(){return!1}},{key:"read",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._queuedChunk){e.next=1;break}return t=this._queuedChunk,this._queuedChunk=null,e.abrupt("return",{value:t,done:!1});case 1:if(!this._done){e.next=2;break}return e.abrupt("return",{value:void 0,done:!0});case 2:return a=new r.PromiseCapability,this._requests.push(a),e.abrupt("return",a.promise);case 3:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"cancel",value:function(e){this._done=!0;var t,a=_createForOfIteratorHelper(this._requests);try{for(a.s();!(t=a.n()).done;)t.value.resolve({value:void 0,done:!0})}catch(e){a.e(e)}finally{a.f()}this._requests.length=0,this._stream._removeRangeReader(this)}}]);var e}()},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFetchStream=void 0;var r=a(1),i=a(20);function n(e,t,a){return{method:"GET",headers:e,signal:a.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}function s(e){var t=new Headers;for(var a in e){var r=e[a];void 0!==r&&t.append(a,r)}return t}function l(e){return e instanceof Uint8Array?e.buffer:e instanceof ArrayBuffer?e:((0,r.warn)("getArrayBuffer - unexpected data format: ".concat(e)),new Uint8Array(e).buffer)}var o=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.source=t,this.isHttp=/^https?:/i.test(t.url),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}),[{key:"_progressiveDataLength",get:function(){var e,t;return null!==(e=null===(t=this._fullRequestReader)||void 0===t?void 0:t._loaded)&&void 0!==e?e:0}},{key:"getFullReader",value:function(){return(0,r.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new _(this),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var a=new c(this,e,t);return this._rangeRequestReaders.push(a),a}},{key:"cancelAllRequests",value:function(e){var t;null===(t=this._fullRequestReader)||void 0===t||t.cancel(e);var a,r=_createForOfIteratorHelper(this._rangeRequestReaders.slice(0));try{for(r.s();!(a=r.n()).done;)a.value.cancel(e)}catch(e){r.e(e)}finally{r.f()}}}])}();t.PDFFetchStream=o;var _=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a=this;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._stream=t,this._reader=null,this._loaded=0,this._filename=null;var l=t.source;this._withCredentials=l.withCredentials||!1,this._contentLength=l.length,this._headersCapability=new r.PromiseCapability,this._disableRange=l.disableRange||!1,this._rangeChunkSize=l.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!l.disableStream,this._isRangeSupported=!l.disableRange,this._headers=s(this._stream.httpHeaders);var o=l.url;fetch(o,n(this._headers,this._withCredentials,this._abortController)).then((function(e){if(!(0,i.validateResponseStatus)(e.status))throw(0,i.createResponseStatusError)(e.status,o);a._reader=e.body.getReader(),a._headersCapability.resolve();var t=function(t){return e.headers.get(t)},n=(0,i.validateRangeRequestCapabilities)({getResponseHeader:t,isHttp:a._stream.isHttp,rangeChunkSize:a._rangeChunkSize,disableRange:a._disableRange}),s=n.allowRangeRequests,l=n.suggestedLength;a._isRangeSupported=s,a._contentLength=l||a._contentLength,a._filename=(0,i.extractFilenameFromHeader)(t),!a._isStreamingSupported&&a._isRangeSupported&&a.cancel(new r.AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject),this.onProgress=null}),[{key:"headersReady",get:function(){return this._headersCapability.promise}},{key:"filename",get:function(){return this._filename}},{key:"contentLength",get:function(){return this._contentLength}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}},{key:"read",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a,r,i;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=1,this._headersCapability.promise;case 1:return e.next=2,this._reader.read();case 2:if(a=e.sent,r=a.value,!(i=a.done)){e.next=3;break}return e.abrupt("return",{value:r,done:i});case 3:return this._loaded+=r.byteLength,null===(t=this.onProgress)||void 0===t||t.call(this,{loaded:this._loaded,total:this._contentLength}),e.abrupt("return",{value:l(r),done:!1});case 4:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"cancel",value:function(e){var t;null===(t=this._reader)||void 0===t||t.cancel(e),this._abortController.abort()}}]);var e}(),c=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a,l){var o=this;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._stream=t,this._reader=null,this._loaded=0;var _=t.source;this._withCredentials=_.withCredentials||!1,this._readCapability=new r.PromiseCapability,this._isStreamingSupported=!_.disableStream,this._abortController=new AbortController,this._headers=s(this._stream.httpHeaders),this._headers.append("Range","bytes=".concat(a,"-").concat(l-1));var c=_.url;fetch(c,n(this._headers,this._withCredentials,this._abortController)).then((function(e){if(!(0,i.validateResponseStatus)(e.status))throw(0,i.createResponseStatusError)(e.status,c);o._readCapability.resolve(),o._reader=e.body.getReader()})).catch(this._readCapability.reject),this.onProgress=null}),[{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}},{key:"read",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a,r,i;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=1,this._readCapability.promise;case 1:return e.next=2,this._reader.read();case 2:if(a=e.sent,r=a.value,!(i=a.done)){e.next=3;break}return e.abrupt("return",{value:r,done:i});case 3:return this._loaded+=r.byteLength,null===(t=this.onProgress)||void 0===t||t.call(this,{loaded:this._loaded}),e.abrupt("return",{value:l(r),done:!1});case 4:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"cancel",value:function(e){var t;null===(t=this._reader)||void 0===t||t.cancel(e),this._abortController.abort()}}]);var e}()},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.createResponseStatusError=function(e,t){return 404===e||0===e&&t.startsWith("file:")?new r.MissingPDFException('Missing PDF "'+t+'".'):new r.UnexpectedResponseException("Unexpected server response (".concat(e,') while retrieving PDF "').concat(t,'".'),e)},t.extractFilenameFromHeader=function(e){var t=e("Content-Disposition");if(t){var a=(0,i.getFilenameFromContentDispositionHeader)(t);if(a.includes("%"))try{a=decodeURIComponent(a)}catch(e){}if((0,n.isPdfFile)(a))return a}return null},t.validateRangeRequestCapabilities=function(e){var t=e.getResponseHeader,a=e.isHttp,r=e.rangeChunkSize,i=e.disableRange,n={allowRangeRequests:!1,suggestedLength:void 0},s=parseInt(t("Content-Length"),10);return Number.isInteger(s)?(n.suggestedLength=s,s<=2*r||i||!a||"bytes"!==t("Accept-Ranges")||"identity"!==(t("Content-Encoding")||"identity")||(n.allowRangeRequests=!0),n):n},t.validateResponseStatus=function(e){return 200===e||206===e};var r=a(1),i=a(21),n=a(6)},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.getFilenameFromContentDispositionHeader=function(e){var t=!0,a=s("filename\\*","i").exec(e);if(a){var i=_(a=a[1]);return o(i=u(i=c(i=unescape(i))))}if(a=function(e){for(var t,a=[],r=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");null!==(t=r.exec(e));){var i=t,n=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(i,4),l=n[1],o=n[2],u=n[3];if((l=parseInt(l,10))in a){if(0===l)break}else a[l]=[o,u]}for(var h=[],d=0;d<a.length&&d in a;++d){var p=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(a[d],2),v=p[0],f=p[1];f=_(f),v&&(f=unescape(f),0===d&&(f=c(f))),h.push(f)}return h.join("")}(e))return o(u(a));if(a=s("filename","i").exec(e)){var n=_(a=a[1]);return o(n=u(n))}function s(e,t){return new RegExp("(?:^|;)\\s*"+e+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',t)}function l(e,a){if(e){if(!/^[\x00-\xFF]+$/.test(a))return a;try{var i=new TextDecoder(e,{fatal:!0}),n=(0,r.stringToBytes)(a);a=i.decode(n),t=!1}catch(e){}}return a}function o(e){return t&&/[\x80-\xff]/.test(e)&&(e=l("utf-8",e),t&&(e=l("iso-8859-1",e))),e}function _(e){if(e.startsWith('"')){for(var t=e.slice(1).split('\\"'),a=0;a<t.length;++a){var r=t[a].indexOf('"');-1!==r&&(t[a]=t[a].slice(0,r),t.length=a+1),t[a]=t[a].replaceAll(/\\(.)/g,"$1")}e=t.join('"')}return e}function c(e){var t=e.indexOf("'");return-1===t?e:l(e.slice(0,t),e.slice(t+1).replace(/^[^']*'/,""))}function u(e){return!e.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(e)?e:e.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(e,t,a,r){if("q"===a||"Q"===a)return l(t,r=(r=r.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,(function(e,t){return String.fromCharCode(parseInt(t,16))})));try{r=atob(r)}catch(e){}return l(t,r)}))}return""};var r=a(1)},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.PDFNetworkStream=void 0;var r=a(1),i=a(20),n=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.url=t,this.isHttp=/^https?:/i.test(t),this.httpHeaders=this.isHttp&&a.httpHeaders||Object.create(null),this.withCredentials=a.withCredentials||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}),[{key:"requestRange",value:function(e,t,a){var r={begin:e,end:t};for(var i in a)r[i]=a[i];return this.request(r)}},{key:"requestFull",value:function(e){return this.request(e)}},{key:"request",value:function(e){var t=new XMLHttpRequest,a=this.currXhrId++,r=this.pendingRequests[a]={xhr:t};for(var i in t.open("GET",this.url),t.withCredentials=this.withCredentials,this.httpHeaders){var n=this.httpHeaders[i];void 0!==n&&t.setRequestHeader(i,n)}return this.isHttp&&"begin"in e&&"end"in e?(t.setRequestHeader("Range","bytes=".concat(e.begin,"-").concat(e.end-1)),r.expectedStatus=206):r.expectedStatus=200,t.responseType="arraybuffer",e.onError&&(t.onerror=function(a){e.onError(t.status)}),t.onreadystatechange=this.onStateChange.bind(this,a),t.onprogress=this.onProgress.bind(this,a),r.onHeadersReceived=e.onHeadersReceived,r.onDone=e.onDone,r.onError=e.onError,r.onProgress=e.onProgress,t.send(null),a}},{key:"onProgress",value:function(e,t){var a,r=this.pendingRequests[e];r&&(null===(a=r.onProgress)||void 0===a||a.call(r,t))}},{key:"onStateChange",value:function(e,t){var a=this.pendingRequests[e];if(a){var i=a.xhr;if(i.readyState>=2&&a.onHeadersReceived&&(a.onHeadersReceived(),delete a.onHeadersReceived),4===i.readyState&&e in this.pendingRequests)if(delete this.pendingRequests[e],0===i.status&&this.isHttp){var n;null===(n=a.onError)||void 0===n||n.call(a,i.status)}else{var s=i.status||200;if(200===s&&206===a.expectedStatus||s===a.expectedStatus){var l=function(e){var t=e.response;return"string"!=typeof t?t:(0,r.stringToBytes)(t).buffer}(i);if(206===s){var o=i.getResponseHeader("Content-Range"),_=/bytes (\d+)-(\d+)\/(\d+)/.exec(o);a.onDone({begin:parseInt(_[1],10),chunk:l})}else if(l)a.onDone({begin:0,chunk:l});else{var c;null===(c=a.onError)||void 0===c||c.call(a,i.status)}}else{var u;null===(u=a.onError)||void 0===u||u.call(a,i.status)}}}}},{key:"getRequestXhr",value:function(e){return this.pendingRequests[e].xhr}},{key:"isPendingRequest",value:function(e){return e in this.pendingRequests}},{key:"abortRequest",value:function(e){var t=this.pendingRequests[e].xhr;delete this.pendingRequests[e],t.abort()}}])}(),s=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._source=t,this._manager=new n(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials}),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}),[{key:"_onRangeRequestReaderClosed",value:function(e){var t=this._rangeRequestReaders.indexOf(e);t>=0&&this._rangeRequestReaders.splice(t,1)}},{key:"getFullReader",value:function(){return(0,r.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new l(this._manager,this._source),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){var a=new o(this._manager,e,t);return a.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(a),a}},{key:"cancelAllRequests",value:function(e){var t;null===(t=this._fullRequestReader)||void 0===t||t.cancel(e);var a,r=_createForOfIteratorHelper(this._rangeRequestReaders.slice(0));try{for(r.s();!(a=r.n()).done;)a.value.cancel(e)}catch(e){r.e(e)}finally{r.f()}}}])}();t.PDFNetworkStream=s;var l=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._manager=t;var i={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=a.url,this._fullRequestId=t.requestFull(i),this._headersReceivedCapability=new r.PromiseCapability,this._disableRange=a.disableRange||!1,this._contentLength=a.length,this._rangeChunkSize=a.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}),[{key:"_onHeadersReceived",value:function(){var e=this._fullRequestId,t=this._manager.getRequestXhr(e),a=function(e){return t.getResponseHeader(e)},r=(0,i.validateRangeRequestCapabilities)({getResponseHeader:a,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange}),n=r.allowRangeRequests,s=r.suggestedLength;n&&(this._isRangeSupported=!0),this._contentLength=s||this._contentLength,this._filename=(0,i.extractFilenameFromHeader)(a),this._isRangeSupported&&this._manager.abortRequest(e),this._headersReceivedCapability.resolve()}},{key:"_onDone",value:function(e){if(e&&(this._requests.length>0?this._requests.shift().resolve({value:e.chunk,done:!1}):this._cachedChunks.push(e.chunk)),this._done=!0,!(this._cachedChunks.length>0)){var t,a=_createForOfIteratorHelper(this._requests);try{for(a.s();!(t=a.n()).done;)t.value.resolve({value:void 0,done:!0})}catch(e){a.e(e)}finally{a.f()}this._requests.length=0}}},{key:"_onError",value:function(e){this._storedError=(0,i.createResponseStatusError)(e,this._url),this._headersReceivedCapability.reject(this._storedError);var t,a=_createForOfIteratorHelper(this._requests);try{for(a.s();!(t=a.n()).done;)t.value.reject(this._storedError)}catch(e){a.e(e)}finally{a.f()}this._requests.length=0,this._cachedChunks.length=0}},{key:"_onProgress",value:function(e){var t;null===(t=this.onProgress)||void 0===t||t.call(this,{loaded:e.loaded,total:e.lengthComputable?e.total:this._contentLength})}},{key:"filename",get:function(){return this._filename}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}},{key:"contentLength",get:function(){return this._contentLength}},{key:"headersReady",get:function(){return this._headersReceivedCapability.promise}},{key:"read",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._storedError){e.next=1;break}throw this._storedError;case 1:if(!(this._cachedChunks.length>0)){e.next=2;break}return t=this._cachedChunks.shift(),e.abrupt("return",{value:t,done:!1});case 2:if(!this._done){e.next=3;break}return e.abrupt("return",{value:void 0,done:!0});case 3:return a=new r.PromiseCapability,this._requests.push(a),e.abrupt("return",a.promise);case 4:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"cancel",value:function(e){this._done=!0,this._headersReceivedCapability.reject(e);var t,a=_createForOfIteratorHelper(this._requests);try{for(a.s();!(t=a.n()).done;)t.value.resolve({value:void 0,done:!0})}catch(e){a.e(e)}finally{a.f()}this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}]);var e}(),o=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a,r){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._manager=t;var i={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url,this._requestId=t.requestRange(a,r,i),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}),[{key:"_close",value:function(){var e;null===(e=this.onClosed)||void 0===e||e.call(this,this)}},{key:"_onDone",value:function(e){var t=e.chunk;this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunk=t,this._done=!0;var a,r=_createForOfIteratorHelper(this._requests);try{for(r.s();!(a=r.n()).done;)a.value.resolve({value:void 0,done:!0})}catch(e){r.e(e)}finally{r.f()}this._requests.length=0,this._close()}},{key:"_onError",value:function(e){this._storedError=(0,i.createResponseStatusError)(e,this._url);var t,a=_createForOfIteratorHelper(this._requests);try{for(a.s();!(t=a.n()).done;)t.value.reject(this._storedError)}catch(e){a.e(e)}finally{a.f()}this._requests.length=0,this._queuedChunk=null}},{key:"_onProgress",value:function(e){var t;this.isStreamingSupported||null===(t=this.onProgress)||void 0===t||t.call(this,{loaded:e.loaded})}},{key:"isStreamingSupported",get:function(){return!1}},{key:"read",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._storedError){e.next=1;break}throw this._storedError;case 1:if(null===this._queuedChunk){e.next=2;break}return t=this._queuedChunk,this._queuedChunk=null,e.abrupt("return",{value:t,done:!1});case 2:if(!this._done){e.next=3;break}return e.abrupt("return",{value:void 0,done:!0});case 3:return a=new r.PromiseCapability,this._requests.push(a),e.abrupt("return",a.promise);case 4:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"cancel",value:function(e){this._done=!0;var t,a=_createForOfIteratorHelper(this._requests);try{for(a.s();!(t=a.n()).done;)t.value.resolve({value:void 0,done:!0})}catch(e){a.e(e)}finally{a.f()}this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}]);var e}()},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.PDFNodeStream=void 0;var r=a(1),i=a(20),n=/^file:\/\/\/[a-zA-Z]:\//,s=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a,r,i;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.source=t,this.url=(a=t.url,"file:"===(i=(r=__webpack_require__(6233)).parse(a)).protocol||i.host?i:/^[a-z]:[/\\]/i.test(a)?r.parse("file:///".concat(a)):(i.host||(i.protocol="file:"),i)),this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol,this.isFsUrl="file:"===this.url.protocol,this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}),[{key:"_progressiveDataLength",get:function(){var e,t;return null!==(e=null===(t=this._fullRequestReader)||void 0===t?void 0:t._loaded)&&void 0!==e?e:0}},{key:"getFullReader",value:function(){return(0,r.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new h(this):new c(this),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var a=this.isFsUrl?new d(this,e,t):new u(this,e,t);return this._rangeRequestReaders.push(a),a}},{key:"cancelAllRequests",value:function(e){var t;null===(t=this._fullRequestReader)||void 0===t||t.cancel(e);var a,r=_createForOfIteratorHelper(this._rangeRequestReaders.slice(0));try{for(r.s();!(a=r.n()).done;)a.value.cancel(e)}catch(e){r.e(e)}finally{r.f()}}}])}();t.PDFNodeStream=s;var l=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;var a=t.source;this._contentLength=a.length,this._loaded=0,this._filename=null,this._disableRange=a.disableRange||!1,this._rangeChunkSize=a.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!a.disableStream,this._isRangeSupported=!a.disableRange,this._readableStream=null,this._readCapability=new r.PromiseCapability,this._headersCapability=new r.PromiseCapability}),[{key:"headersReady",get:function(){return this._headersCapability.promise}},{key:"filename",get:function(){return this._filename}},{key:"contentLength",get:function(){return this._contentLength}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}},{key:"read",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a,i;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=1,this._readCapability.promise;case 1:if(!this._done){e.next=2;break}return e.abrupt("return",{value:void 0,done:!0});case 2:if(!this._storedError){e.next=3;break}throw this._storedError;case 3:if(null!==(a=this._readableStream.read())){e.next=4;break}return this._readCapability=new r.PromiseCapability,e.abrupt("return",this.read());case 4:return this._loaded+=a.length,null===(t=this.onProgress)||void 0===t||t.call(this,{loaded:this._loaded,total:this._contentLength}),i=new Uint8Array(a).buffer,e.abrupt("return",{value:i,done:!1});case 5:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"cancel",value:function(e){this._readableStream?this._readableStream.destroy(e):this._error(e)}},{key:"_error",value:function(e){this._storedError=e,this._readCapability.resolve()}},{key:"_setReadableStream",value:function(e){var t=this;this._readableStream=e,e.on("readable",(function(){t._readCapability.resolve()})),e.on("end",(function(){e.destroy(),t._done=!0,t._readCapability.resolve()})),e.on("error",(function(e){t._error(e)})),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new r.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}]);var e}(),o=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=new r.PromiseCapability;var a=t.source;this._isStreamingSupported=!a.disableStream}),[{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}},{key:"read",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){var t,a,i;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=1,this._readCapability.promise;case 1:if(!this._done){e.next=2;break}return e.abrupt("return",{value:void 0,done:!0});case 2:if(!this._storedError){e.next=3;break}throw this._storedError;case 3:if(null!==(a=this._readableStream.read())){e.next=4;break}return this._readCapability=new r.PromiseCapability,e.abrupt("return",this.read());case 4:return this._loaded+=a.length,null===(t=this.onProgress)||void 0===t||t.call(this,{loaded:this._loaded}),i=new Uint8Array(a).buffer,e.abrupt("return",{value:i,done:!1});case 5:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"cancel",value:function(e){this._readableStream?this._readableStream.destroy(e):this._error(e)}},{key:"_error",value:function(e){this._storedError=e,this._readCapability.resolve()}},{key:"_setReadableStream",value:function(e){var t=this;this._readableStream=e,e.on("readable",(function(){t._readCapability.resolve()})),e.on("end",(function(){e.destroy(),t._done=!0,t._readCapability.resolve()})),e.on("error",(function(e){t._error(e)})),this._storedError&&this._readableStream.destroy(this._storedError)}}]);var e}();function _(e,t){return{protocol:e.protocol,auth:e.auth,host:e.hostname,port:e.port,path:e.path,method:"GET",headers:t}}var c=function(e){function t(e){var a;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t);var n=function(t){if(404===t.statusCode){var n=new r.MissingPDFException('Missing PDF "'.concat(a._url,'".'));return a._storedError=n,void a._headersCapability.reject(n)}a._headersCapability.resolve(),a._setReadableStream(t);var s=function(e){return a._readableStream.headers[e.toLowerCase()]},l=(0,i.validateRangeRequestCapabilities)({getResponseHeader:s,isHttp:e.isHttp,rangeChunkSize:a._rangeChunkSize,disableRange:a._disableRange}),o=l.allowRangeRequests,_=l.suggestedLength;a._isRangeSupported=o,a._contentLength=_||a._contentLength,a._filename=(0,i.extractFilenameFromHeader)(s)};if((a=_callSuper(this,t,[e]))._request=null,"http:"===a._url.protocol){var s=__webpack_require__(179);a._request=s.request(_(a._url,e.httpHeaders),n)}else{var l=__webpack_require__(2706);a._request=l.request(_(a._url,e.httpHeaders),n)}return a._request.on("error",(function(e){a._storedError=e,a._headersCapability.reject(e)})),a._request.end(),a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(l),u=function(e){function t(e,a,i){var n;for(var s in(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(n=_callSuper(this,t,[e]))._httpHeaders={},e.httpHeaders){var l=e.httpHeaders[s];void 0!==l&&(n._httpHeaders[s]=l)}n._httpHeaders.Range="bytes=".concat(a,"-").concat(i-1);var o=function(e){if(404!==e.statusCode)n._setReadableStream(e);else{var t=new r.MissingPDFException('Missing PDF "'.concat(n._url,'".'));n._storedError=t}};if(n._request=null,"http:"===n._url.protocol){var c=__webpack_require__(179);n._request=c.request(_(n._url,n._httpHeaders),o)}else{var u=__webpack_require__(2706);n._request=u.request(_(n._url,n._httpHeaders),o)}return n._request.on("error",(function(e){n._storedError=e})),n._request.end(),n}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(o),h=function(e){function t(e){var a;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),a=_callSuper(this,t,[e]);var i=decodeURIComponent(a._url.path);n.test(a._url.href)&&(i=i.replace(/^\//,""));var s=__webpack_require__(950);return s.lstat(i,(function(e,t){if(e)return"ENOENT"===e.code&&(e=new r.MissingPDFException('Missing PDF "'.concat(i,'".'))),a._storedError=e,void a._headersCapability.reject(e);a._contentLength=t.size,a._setReadableStream(s.createReadStream(i)),a._headersCapability.resolve()})),a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(l),d=function(e){function t(e,a,r){var i;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),i=_callSuper(this,t,[e]);var s=decodeURIComponent(i._url.path);n.test(i._url.href)&&(s=s.replace(/^\//,""));var l=__webpack_require__(950);return i._setReadableStream(l.createReadStream(s,{start:a,end:r-1})),i}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(o)},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.SVGGraphics=void 0;var r=a(6),i=a(1),n="normal",s="#000000",l=["butt","round","square"],o=["miter","round","bevel"],_=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(URL.createObjectURL&&"undefined"!=typeof Blob&&!a)return URL.createObjectURL(new Blob([e],{type:t}));for(var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",i="data:".concat(t,";base64,"),n=0,s=e.length;n<s;n+=3){var l=255&e[n],o=255&e[n+1],_=255&e[n+2];i+=r[l>>2]+r[(3&l)<<4|o>>4]+r[n+1<s?(15&o)<<2|_>>6:64]+r[n+2<s?63&_:64]}return i},c=function(){for(var e=new Uint8Array([137,80,78,71,13,10,26,10]),t=new Int32Array(256),a=0;a<256;a++){for(var r=a,n=0;n<8;n++)r=1&r?3988292384^r>>1&2147483647:r>>1&2147483647;t[a]=r}function s(e,a,r,i){var n=i,s=a.length;r[n]=s>>24&255,r[n+1]=s>>16&255,r[n+2]=s>>8&255,r[n+3]=255&s,r[n+=4]=255&e.charCodeAt(0),r[n+1]=255&e.charCodeAt(1),r[n+2]=255&e.charCodeAt(2),r[n+3]=255&e.charCodeAt(3),n+=4,r.set(a,n);var l=function(e,a,r){for(var i=-1,n=a;n<r;n++){var s=255&(i^e[n]);i=i>>>8^t[s]}return~i}(r,i+4,n+=a.length);r[n]=l>>24&255,r[n+1]=l>>16&255,r[n+2]=l>>8&255,r[n+3]=255&l}function l(e){var t=e.length,a=65535,r=Math.ceil(t/a),i=new Uint8Array(2+t+5*r+4),n=0;i[n++]=120,i[n++]=156;for(var s=0;t>a;)i[n++]=0,i[n++]=255,i[n++]=255,i[n++]=0,i[n++]=0,i.set(e.subarray(s,s+a),n),n+=a,s+=a,t-=a;i[n++]=1,i[n++]=255&t,i[n++]=t>>8&255,i[n++]=255&~t,i[n++]=(65535&~t)>>8&255,i.set(e.subarray(s),n),n+=e.length-s;var l=function(e,t,a){for(var r=1,i=0,n=0;n<a;++n)i=(i+(r=(r+(255&e[n]))%65521))%65521;return i<<16|r}(e,0,e.length);return i[n++]=l>>24&255,i[n++]=l>>16&255,i[n++]=l>>8&255,i[n++]=255&l,i}return function(t,a,r){return function(t,a,r,n){var o,c,u,h=t.width,d=t.height,p=t.data;switch(a){case i.ImageKind.GRAYSCALE_1BPP:c=0,o=1,u=h+7>>3;break;case i.ImageKind.RGB_24BPP:c=2,o=8,u=3*h;break;case i.ImageKind.RGBA_32BPP:c=6,o=8,u=4*h;break;default:throw new Error("invalid format")}for(var v=new Uint8Array((1+u)*d),f=0,m=0,b=0;b<d;++b)v[f++]=0,v.set(p.subarray(m,m+u),f),m+=u,f+=u;if(a===i.ImageKind.GRAYSCALE_1BPP&&n){f=0;for(var g=0;g<d;g++){f++;for(var P=0;P<u;P++)v[f++]^=255}}var y=new Uint8Array([h>>24&255,h>>16&255,h>>8&255,255&h,d>>24&255,d>>16&255,d>>8&255,255&d,o,c,0,0,0]),A=function(e){if(!i.isNodeJS)return l(e);try{var t=parseInt(process.versions.node)>=8?e:Buffer.from(e),a=__webpack_require__(8680).deflateSync(t,{level:9});return a instanceof Uint8Array?a:new Uint8Array(a)}catch(e){(0,i.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+e)}return l(e)}(v),E=e.length+36+y.length+A.length,C=new Uint8Array(E),M=0;return C.set(e,M),s("IHDR",y,C,M+=e.length),s("IDATA",A,C,M+=12+y.length),M+=12+A.length,s("IEND",new Uint8Array(0),C,M),_(C,"image/png",r)}(t,void 0===t.kind?i.ImageKind.GRAYSCALE_1BPP:t.kind,a,r)}}(),u=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this.fontSizeScale=1,this.fontWeight=n,this.fontSize=0,this.textMatrix=i.IDENTITY_MATRIX,this.fontMatrix=i.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=i.TextRenderingMode.FILL,this.textMatrixScale=1,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=s,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}),[{key:"clone",value:function(){return Object.create(this)}},{key:"setCurrentPoint",value:function(e,t){this.x=e,this.y=t}}])}();function h(e){if(Number.isInteger(e))return e.toString();var t=e.toFixed(10),a=t.length-1;if("0"!==t[a])return t;do{a--}while("0"===t[a]);return t.substring(0,"."===t[a]?a:a+1)}function d(e){if(0===e[4]&&0===e[5]){if(0===e[1]&&0===e[2])return 1===e[0]&&1===e[3]?"":"scale(".concat(h(e[0])," ").concat(h(e[3]),")");if(e[0]===e[3]&&e[1]===-e[2]){var t=180*Math.acos(e[0])/Math.PI;return"rotate(".concat(h(t),")")}}else if(1===e[0]&&0===e[1]&&0===e[2]&&1===e[3])return"translate(".concat(h(e[4])," ").concat(h(e[5]),")");return"matrix(".concat(h(e[0])," ").concat(h(e[1])," ").concat(h(e[2])," ").concat(h(e[3])," ").concat(h(e[4])," ")+"".concat(h(e[5]),")")}var p=0,v=0,f=0,m=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t,a){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(var s in(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),(0,r.deprecated)("The SVG back-end is no longer maintained and *may* be removed in the future."),this.svgFactory=new r.DOMSVGFactory,this.current=new u,this.transformMatrix=i.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=t,this.objs=a,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!n,this._operatorIdMapping=[],i.OPS)this._operatorIdMapping[i.OPS[s]]=s}),[{key:"getObject",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return"string"==typeof e?e.startsWith("g_")?this.commonObjs.get(e):this.objs.get(e):t}},{key:"save",value:function(){this.transformStack.push(this.transformMatrix);var e=this.current;this.extraStack.push(e),this.current=e.clone()}},{key:"restore",value:function(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null}},{key:"group",value:function(e){this.save(),this.executeOpTree(e),this.restore()}},{key:"loadDependencies",value:function(e){for(var t=this,a=e.fnArray,r=e.argsArray,n=0,s=a.length;n<s;n++)if(a[n]===i.OPS.dependency){var l,o=_createForOfIteratorHelper(r[n]);try{var _=function(){var e=l.value,a=e.startsWith("g_")?t.commonObjs:t.objs,r=new Promise((function(t){a.get(e,t)}));t.current.dependencies.push(r)};for(o.s();!(l=o.n()).done;)_()}catch(e){o.e(e)}finally{o.f()}}return Promise.all(this.current.dependencies)}},{key:"transform",value:function(e,t,a,r,n,s){var l=[e,t,a,r,n,s];this.transformMatrix=i.Util.transform(this.transformMatrix,l),this.tgrp=null}},{key:"getSVG",value:function(e,t){var a=this;this.viewport=t;var r=this._initialize(t);return this.loadDependencies(e).then((function(){return a.transformMatrix=i.IDENTITY_MATRIX,a.executeOpTree(a.convertOpList(e)),r}))}},{key:"convertOpList",value:function(e){for(var t=this._operatorIdMapping,a=e.argsArray,r=e.fnArray,i=[],n=0,s=r.length;n<s;n++){var l=r[n];i.push({fnId:l,fn:t[l],args:a[n]})}return function(e){var t,a=[],r=[],i=_createForOfIteratorHelper(e);try{for(i.s();!(t=i.n()).done;){var n=t.value;"save"!==n.fn?"restore"===n.fn?a=r.pop():a.push(n):(a.push({fnId:92,fn:"group",items:[]}),r.push(a),a=a.at(-1).items)}}catch(e){i.e(e)}finally{i.f()}return a}(i)}},{key:"executeOpTree",value:function(e){var t,a=_createForOfIteratorHelper(e);try{for(a.s();!(t=a.n()).done;){var r=t.value,n=r.fn,s=r.fnId,l=r.args;switch(0|s){case i.OPS.beginText:this.beginText();break;case i.OPS.dependency:break;case i.OPS.setLeading:this.setLeading(l);break;case i.OPS.setLeadingMoveText:this.setLeadingMoveText(l[0],l[1]);break;case i.OPS.setFont:this.setFont(l);break;case i.OPS.showText:case i.OPS.showSpacedText:this.showText(l[0]);break;case i.OPS.endText:this.endText();break;case i.OPS.moveText:this.moveText(l[0],l[1]);break;case i.OPS.setCharSpacing:this.setCharSpacing(l[0]);break;case i.OPS.setWordSpacing:this.setWordSpacing(l[0]);break;case i.OPS.setHScale:this.setHScale(l[0]);break;case i.OPS.setTextMatrix:this.setTextMatrix(l[0],l[1],l[2],l[3],l[4],l[5]);break;case i.OPS.setTextRise:this.setTextRise(l[0]);break;case i.OPS.setTextRenderingMode:this.setTextRenderingMode(l[0]);break;case i.OPS.setLineWidth:this.setLineWidth(l[0]);break;case i.OPS.setLineJoin:this.setLineJoin(l[0]);break;case i.OPS.setLineCap:this.setLineCap(l[0]);break;case i.OPS.setMiterLimit:this.setMiterLimit(l[0]);break;case i.OPS.setFillRGBColor:this.setFillRGBColor(l[0],l[1],l[2]);break;case i.OPS.setStrokeRGBColor:this.setStrokeRGBColor(l[0],l[1],l[2]);break;case i.OPS.setStrokeColorN:this.setStrokeColorN(l);break;case i.OPS.setFillColorN:this.setFillColorN(l);break;case i.OPS.shadingFill:this.shadingFill(l[0]);break;case i.OPS.setDash:this.setDash(l[0],l[1]);break;case i.OPS.setRenderingIntent:this.setRenderingIntent(l[0]);break;case i.OPS.setFlatness:this.setFlatness(l[0]);break;case i.OPS.setGState:this.setGState(l[0]);break;case i.OPS.fill:this.fill();break;case i.OPS.eoFill:this.eoFill();break;case i.OPS.stroke:this.stroke();break;case i.OPS.fillStroke:this.fillStroke();break;case i.OPS.eoFillStroke:this.eoFillStroke();break;case i.OPS.clip:this.clip("nonzero");break;case i.OPS.eoClip:this.clip("evenodd");break;case i.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case i.OPS.paintImageXObject:this.paintImageXObject(l[0]);break;case i.OPS.paintInlineImageXObject:this.paintInlineImageXObject(l[0]);break;case i.OPS.paintImageMaskXObject:this.paintImageMaskXObject(l[0]);break;case i.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(l[0],l[1]);break;case i.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case i.OPS.closePath:this.closePath();break;case i.OPS.closeStroke:this.closeStroke();break;case i.OPS.closeFillStroke:this.closeFillStroke();break;case i.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case i.OPS.nextLine:this.nextLine();break;case i.OPS.transform:this.transform(l[0],l[1],l[2],l[3],l[4],l[5]);break;case i.OPS.constructPath:this.constructPath(l[0],l[1]);break;case i.OPS.endPath:this.endPath();break;case 92:this.group(r.items);break;default:(0,i.warn)("Unimplemented operator ".concat(n))}}}catch(e){a.e(e)}finally{a.f()}}},{key:"setWordSpacing",value:function(e){this.current.wordSpacing=e}},{key:"setCharSpacing",value:function(e){this.current.charSpacing=e}},{key:"nextLine",value:function(){this.moveText(0,this.current.leading)}},{key:"setTextMatrix",value:function(e,t,a,r,i,n){var s=this.current;s.textMatrix=s.lineMatrix=[e,t,a,r,i,n],s.textMatrixScale=Math.hypot(e,t),s.x=s.lineX=0,s.y=s.lineY=0,s.xcoords=[],s.ycoords=[],s.tspan=this.svgFactory.createElement("svg:tspan"),s.tspan.setAttributeNS(null,"font-family",s.fontFamily),s.tspan.setAttributeNS(null,"font-size","".concat(h(s.fontSize),"px")),s.tspan.setAttributeNS(null,"y",h(-s.y)),s.txtElement=this.svgFactory.createElement("svg:text"),s.txtElement.append(s.tspan)}},{key:"beginText",value:function(){var e=this.current;e.x=e.lineX=0,e.y=e.lineY=0,e.textMatrix=i.IDENTITY_MATRIX,e.lineMatrix=i.IDENTITY_MATRIX,e.textMatrixScale=1,e.tspan=this.svgFactory.createElement("svg:tspan"),e.txtElement=this.svgFactory.createElement("svg:text"),e.txtgrp=this.svgFactory.createElement("svg:g"),e.xcoords=[],e.ycoords=[]}},{key:"moveText",value:function(e,t){var a=this.current;a.x=a.lineX+=e,a.y=a.lineY+=t,a.xcoords=[],a.ycoords=[],a.tspan=this.svgFactory.createElement("svg:tspan"),a.tspan.setAttributeNS(null,"font-family",a.fontFamily),a.tspan.setAttributeNS(null,"font-size","".concat(h(a.fontSize),"px")),a.tspan.setAttributeNS(null,"y",h(-a.y))}},{key:"showText",value:function(e){var t=this.current,a=t.font,r=t.fontSize;if(0!==r){var l,o=t.fontSizeScale,_=t.charSpacing,c=t.wordSpacing,u=t.fontDirection,p=t.textHScale*u,v=a.vertical,f=v?1:-1,m=a.defaultVMetrics,b=r*t.fontMatrix[0],g=0,P=_createForOfIteratorHelper(e);try{for(P.s();!(l=P.n()).done;){var y=l.value;if(null!==y)if("number"!=typeof y){var A=(y.isSpace?c:0)+_,E=y.fontChar,C=void 0,M=void 0,k=y.width;if(v){var O=void 0,D=y.vmetric||m;O=-(O=y.vmetric?D[1]:.5*k)*b;var F=D[2]*b;k=D?-D[0]:k,C=O/o,M=(g+F)/o}else C=g/o,M=0;(y.isInFont||a.missingFile)&&(t.xcoords.push(t.x+C),v&&t.ycoords.push(-t.y+M),t.tspan.textContent+=E),g+=v?k*b-A*u:k*b+A*u}else g+=f*y*r/1e3;else g+=u*c}}catch(e){P.e(e)}finally{P.f()}t.tspan.setAttributeNS(null,"x",t.xcoords.map(h).join(" ")),v?t.tspan.setAttributeNS(null,"y",t.ycoords.map(h).join(" ")):t.tspan.setAttributeNS(null,"y",h(-t.y)),v?t.y-=g:t.x+=g*p,t.tspan.setAttributeNS(null,"font-family",t.fontFamily),t.tspan.setAttributeNS(null,"font-size","".concat(h(t.fontSize),"px")),"normal"!==t.fontStyle&&t.tspan.setAttributeNS(null,"font-style",t.fontStyle),t.fontWeight!==n&&t.tspan.setAttributeNS(null,"font-weight",t.fontWeight);var S=t.textRenderingMode&i.TextRenderingMode.FILL_STROKE_MASK;if(S===i.TextRenderingMode.FILL||S===i.TextRenderingMode.FILL_STROKE?(t.fillColor!==s&&t.tspan.setAttributeNS(null,"fill",t.fillColor),t.fillAlpha<1&&t.tspan.setAttributeNS(null,"fill-opacity",t.fillAlpha)):t.textRenderingMode===i.TextRenderingMode.ADD_TO_PATH?t.tspan.setAttributeNS(null,"fill","transparent"):t.tspan.setAttributeNS(null,"fill","none"),S===i.TextRenderingMode.STROKE||S===i.TextRenderingMode.FILL_STROKE){var T=1/(t.textMatrixScale||1);this._setStrokeAttributes(t.tspan,T)}var I=t.textMatrix;0!==t.textRise&&((I=I.slice())[5]+=t.textRise),t.txtElement.setAttributeNS(null,"transform","".concat(d(I)," scale(").concat(h(p),", -1)")),t.txtElement.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),t.txtElement.append(t.tspan),t.txtgrp.append(t.txtElement),this._ensureTransformGroup().append(t.txtElement)}}},{key:"setLeadingMoveText",value:function(e,t){this.setLeading(-t),this.moveText(e,t)}},{key:"addFontStyle",value:function(e){if(!e.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.append(this.cssStyle));var t=_(e.data,e.mimetype,this.forceDataSchema);this.cssStyle.textContent+='@font-face { font-family: "'.concat(e.loadedName,'";')+" src: url(".concat(t,"); }\n")}},{key:"setFont",value:function(e){var t=this.current,a=this.commonObjs.get(e[0]),r=e[1];t.font=a,!this.embedFonts||a.missingFile||this.embeddedFonts[a.loadedName]||(this.addFontStyle(a),this.embeddedFonts[a.loadedName]=a),t.fontMatrix=a.fontMatrix||i.FONT_IDENTITY_MATRIX;var n="normal";a.black?n="900":a.bold&&(n="bold");var s=a.italic?"italic":"normal";r<0?(r=-r,t.fontDirection=-1):t.fontDirection=1,t.fontSize=r,t.fontFamily=a.loadedName,t.fontWeight=n,t.fontStyle=s,t.tspan=this.svgFactory.createElement("svg:tspan"),t.tspan.setAttributeNS(null,"y",h(-t.y)),t.xcoords=[],t.ycoords=[]}},{key:"endText",value:function(){var e,t=this.current;t.textRenderingMode&i.TextRenderingMode.ADD_TO_PATH_FLAG&&null!==(e=t.txtElement)&&void 0!==e&&e.hasChildNodes()&&(t.element=t.txtElement,this.clip("nonzero"),this.endPath())}},{key:"setLineWidth",value:function(e){e>0&&(this.current.lineWidth=e)}},{key:"setLineCap",value:function(e){this.current.lineCap=l[e]}},{key:"setLineJoin",value:function(e){this.current.lineJoin=o[e]}},{key:"setMiterLimit",value:function(e){this.current.miterLimit=e}},{key:"setStrokeAlpha",value:function(e){this.current.strokeAlpha=e}},{key:"setStrokeRGBColor",value:function(e,t,a){this.current.strokeColor=i.Util.makeHexColor(e,t,a)}},{key:"setFillAlpha",value:function(e){this.current.fillAlpha=e}},{key:"setFillRGBColor",value:function(e,t,a){this.current.fillColor=i.Util.makeHexColor(e,t,a),this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[],this.current.ycoords=[]}},{key:"setStrokeColorN",value:function(e){this.current.strokeColor=this._makeColorN_Pattern(e)}},{key:"setFillColorN",value:function(e){this.current.fillColor=this._makeColorN_Pattern(e)}},{key:"shadingFill",value:function(e){var t=this.viewport,a=t.width,r=t.height,n=i.Util.inverseTransform(this.transformMatrix),s=i.Util.getAxialAlignedBoundingBox([0,0,a,r],n),l=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(s,4),o=l[0],_=l[1],c=l[2],u=l[3],h=this.svgFactory.createElement("svg:rect");h.setAttributeNS(null,"x",o),h.setAttributeNS(null,"y",_),h.setAttributeNS(null,"width",c-o),h.setAttributeNS(null,"height",u-_),h.setAttributeNS(null,"fill",this._makeShadingPattern(e)),this.current.fillAlpha<1&&h.setAttributeNS(null,"fill-opacity",this.current.fillAlpha),this._ensureTransformGroup().append(h)}},{key:"_makeColorN_Pattern",value:function(e){return"TilingPattern"===e[0]?this._makeTilingPattern(e):this._makeShadingPattern(e)}},{key:"_makeTilingPattern",value:function(e){var t=e[1],a=e[2],r=e[3]||i.IDENTITY_MATRIX,n=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e[4],4),s=n[0],l=n[1],o=n[2],_=n[3],c=e[5],u=e[6],h=e[7],d="shading".concat(f++),p=i.Util.normalizeRect([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(i.Util.applyTransform([s,l],r)),(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(i.Util.applyTransform([o,_],r)))),v=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(p,4),m=v[0],b=v[1],g=v[2],P=v[3],y=i.Util.singularValueDecompose2dScale(r),A=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(y,2),E=c*A[0],C=u*A[1],M=this.svgFactory.createElement("svg:pattern");M.setAttributeNS(null,"id",d),M.setAttributeNS(null,"patternUnits","userSpaceOnUse"),M.setAttributeNS(null,"width",E),M.setAttributeNS(null,"height",C),M.setAttributeNS(null,"x","".concat(m)),M.setAttributeNS(null,"y","".concat(b));var k=this.svg,O=this.transformMatrix,D=this.current.fillColor,F=this.current.strokeColor,S=this.svgFactory.create(g-m,P-b);if(this.svg=S,this.transformMatrix=r,2===h){var T,I=(T=i.Util).makeHexColor.apply(T,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(t));this.current.fillColor=I,this.current.strokeColor=I}return this.executeOpTree(this.convertOpList(a)),this.svg=k,this.transformMatrix=O,this.current.fillColor=D,this.current.strokeColor=F,M.append(S.childNodes[0]),this.defs.append(M),"url(#".concat(d,")")}},{key:"_makeShadingPattern",value:function(e){switch("string"==typeof e&&(e=this.objs.get(e)),e[0]){case"RadialAxial":var t,a="shading".concat(f++),r=e[3];switch(e[1]){case"axial":var n=e[4],s=e[5];(t=this.svgFactory.createElement("svg:linearGradient")).setAttributeNS(null,"id",a),t.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),t.setAttributeNS(null,"x1",n[0]),t.setAttributeNS(null,"y1",n[1]),t.setAttributeNS(null,"x2",s[0]),t.setAttributeNS(null,"y2",s[1]);break;case"radial":var l=e[4],o=e[5],_=e[6],c=e[7];(t=this.svgFactory.createElement("svg:radialGradient")).setAttributeNS(null,"id",a),t.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),t.setAttributeNS(null,"cx",o[0]),t.setAttributeNS(null,"cy",o[1]),t.setAttributeNS(null,"r",c),t.setAttributeNS(null,"fx",l[0]),t.setAttributeNS(null,"fy",l[1]),t.setAttributeNS(null,"fr",_);break;default:throw new Error("Unknown RadialAxial type: ".concat(e[1]))}var u,h=_createForOfIteratorHelper(r);try{for(h.s();!(u=h.n()).done;){var d=u.value,p=this.svgFactory.createElement("svg:stop");p.setAttributeNS(null,"offset",d[0]),p.setAttributeNS(null,"stop-color",d[1]),t.append(p)}}catch(e){h.e(e)}finally{h.f()}return this.defs.append(t),"url(#".concat(a,")");case"Mesh":return(0,i.warn)("Unimplemented pattern Mesh"),null;case"Dummy":return"hotpink";default:throw new Error("Unknown IR type: ".concat(e[0]))}}},{key:"setDash",value:function(e,t){this.current.dashArray=e,this.current.dashPhase=t}},{key:"constructPath",value:function(e,t){var a,r=this.current,n=r.x,s=r.y,l=[],o=0,_=_createForOfIteratorHelper(e);try{for(_.s();!(a=_.n()).done;)switch(0|a.value){case i.OPS.rectangle:n=t[o++],s=t[o++];var c=t[o++],u=t[o++],d=n+c,p=s+u;l.push("M",h(n),h(s),"L",h(d),h(s),"L",h(d),h(p),"L",h(n),h(p),"Z");break;case i.OPS.moveTo:n=t[o++],s=t[o++],l.push("M",h(n),h(s));break;case i.OPS.lineTo:n=t[o++],s=t[o++],l.push("L",h(n),h(s));break;case i.OPS.curveTo:n=t[o+4],s=t[o+5],l.push("C",h(t[o]),h(t[o+1]),h(t[o+2]),h(t[o+3]),h(n),h(s)),o+=6;break;case i.OPS.curveTo2:l.push("C",h(n),h(s),h(t[o]),h(t[o+1]),h(t[o+2]),h(t[o+3])),n=t[o+2],s=t[o+3],o+=4;break;case i.OPS.curveTo3:n=t[o+2],s=t[o+3],l.push("C",h(t[o]),h(t[o+1]),h(n),h(s),h(n),h(s)),o+=4;break;case i.OPS.closePath:l.push("Z")}}catch(e){_.e(e)}finally{_.f()}l=l.join(" "),r.path&&e.length>0&&e[0]!==i.OPS.rectangle&&e[0]!==i.OPS.moveTo?l=r.path.getAttributeNS(null,"d")+l:(r.path=this.svgFactory.createElement("svg:path"),this._ensureTransformGroup().append(r.path)),r.path.setAttributeNS(null,"d",l),r.path.setAttributeNS(null,"fill","none"),r.element=r.path,r.setCurrentPoint(n,s)}},{key:"endPath",value:function(){var e=this.current;if(e.path=null,this.pendingClip)if(e.element){var t="clippath".concat(p++),a=this.svgFactory.createElement("svg:clipPath");a.setAttributeNS(null,"id",t),a.setAttributeNS(null,"transform",d(this.transformMatrix));var r=e.element.cloneNode(!0);if("evenodd"===this.pendingClip?r.setAttributeNS(null,"clip-rule","evenodd"):r.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,a.append(r),this.defs.append(a),e.activeClipUrl){e.clipGroup=null;var i,n=_createForOfIteratorHelper(this.extraStack);try{for(n.s();!(i=n.n()).done;)i.value.clipGroup=null}catch(e){n.e(e)}finally{n.f()}a.setAttributeNS(null,"clip-path",e.activeClipUrl)}e.activeClipUrl="url(#".concat(t,")"),this.tgrp=null}else this.pendingClip=null}},{key:"clip",value:function(e){this.pendingClip=e}},{key:"closePath",value:function(){var e=this.current;if(e.path){var t="".concat(e.path.getAttributeNS(null,"d"),"Z");e.path.setAttributeNS(null,"d",t)}}},{key:"setLeading",value:function(e){this.current.leading=-e}},{key:"setTextRise",value:function(e){this.current.textRise=e}},{key:"setTextRenderingMode",value:function(e){this.current.textRenderingMode=e}},{key:"setHScale",value:function(e){this.current.textHScale=e/100}},{key:"setRenderingIntent",value:function(e){}},{key:"setFlatness",value:function(e){}},{key:"setGState",value:function(e){var t,a=_createForOfIteratorHelper(e);try{for(a.s();!(t=a.n()).done;){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(t.value,2),n=r[0],s=r[1];switch(n){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s);break;case"CA":this.setStrokeAlpha(s);break;case"ca":this.setFillAlpha(s);break;default:(0,i.warn)("Unimplemented graphic state operator ".concat(n))}}}catch(e){a.e(e)}finally{a.f()}}},{key:"fill",value:function(){var e=this.current;e.element&&(e.element.setAttributeNS(null,"fill",e.fillColor),e.element.setAttributeNS(null,"fill-opacity",e.fillAlpha),this.endPath())}},{key:"stroke",value:function(){var e=this.current;e.element&&(this._setStrokeAttributes(e.element),e.element.setAttributeNS(null,"fill","none"),this.endPath())}},{key:"_setStrokeAttributes",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=this.current,r=a.dashArray;1!==t&&r.length>0&&(r=r.map((function(e){return t*e}))),e.setAttributeNS(null,"stroke",a.strokeColor),e.setAttributeNS(null,"stroke-opacity",a.strokeAlpha),e.setAttributeNS(null,"stroke-miterlimit",h(a.miterLimit)),e.setAttributeNS(null,"stroke-linecap",a.lineCap),e.setAttributeNS(null,"stroke-linejoin",a.lineJoin),e.setAttributeNS(null,"stroke-width",h(t*a.lineWidth)+"px"),e.setAttributeNS(null,"stroke-dasharray",r.map(h).join(" ")),e.setAttributeNS(null,"stroke-dashoffset",h(t*a.dashPhase)+"px")}},{key:"eoFill",value:function(){var e;null===(e=this.current.element)||void 0===e||e.setAttributeNS(null,"fill-rule","evenodd"),this.fill()}},{key:"fillStroke",value:function(){this.stroke(),this.fill()}},{key:"eoFillStroke",value:function(){var e;null===(e=this.current.element)||void 0===e||e.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()}},{key:"closeStroke",value:function(){this.closePath(),this.stroke()}},{key:"closeFillStroke",value:function(){this.closePath(),this.fillStroke()}},{key:"closeEOFillStroke",value:function(){this.closePath(),this.eoFillStroke()}},{key:"paintSolidColorImageMask",value:function(){var e=this.svgFactory.createElement("svg:rect");e.setAttributeNS(null,"x","0"),e.setAttributeNS(null,"y","0"),e.setAttributeNS(null,"width","1px"),e.setAttributeNS(null,"height","1px"),e.setAttributeNS(null,"fill",this.current.fillColor),this._ensureTransformGroup().append(e)}},{key:"paintImageXObject",value:function(e){var t=this.getObject(e);t?this.paintInlineImageXObject(t):(0,i.warn)("Dependent image with object ID ".concat(e," is not ready yet"))}},{key:"paintInlineImageXObject",value:function(e,t){var a=e.width,r=e.height,i=c(e,this.forceDataSchema,!!t),n=this.svgFactory.createElement("svg:rect");n.setAttributeNS(null,"x","0"),n.setAttributeNS(null,"y","0"),n.setAttributeNS(null,"width",h(a)),n.setAttributeNS(null,"height",h(r)),this.current.element=n,this.clip("nonzero");var s=this.svgFactory.createElement("svg:image");s.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i),s.setAttributeNS(null,"x","0"),s.setAttributeNS(null,"y",h(-r)),s.setAttributeNS(null,"width",h(a)+"px"),s.setAttributeNS(null,"height",h(r)+"px"),s.setAttributeNS(null,"transform","scale(".concat(h(1/a)," ").concat(h(-1/r),")")),t?t.append(s):this._ensureTransformGroup().append(s)}},{key:"paintImageMaskXObject",value:function(e){var t=this.getObject(e.data,e);if(t.bitmap)(0,i.warn)("paintImageMaskXObject: ImageBitmap support is not implemented, ensure that the `isOffscreenCanvasSupported` API parameter is disabled.");else{var a=this.current,r=t.width,n=t.height,s=a.fillColor;a.maskId="mask".concat(v++);var l=this.svgFactory.createElement("svg:mask");l.setAttributeNS(null,"id",a.maskId);var o=this.svgFactory.createElement("svg:rect");o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y","0"),o.setAttributeNS(null,"width",h(r)),o.setAttributeNS(null,"height",h(n)),o.setAttributeNS(null,"fill",s),o.setAttributeNS(null,"mask","url(#".concat(a.maskId,")")),this.defs.append(l),this._ensureTransformGroup().append(o),this.paintInlineImageXObject(t,l)}}},{key:"paintFormXObjectBegin",value:function(e,t){if(Array.isArray(e)&&6===e.length&&this.transform(e[0],e[1],e[2],e[3],e[4],e[5]),t){var a=t[2]-t[0],r=t[3]-t[1],i=this.svgFactory.createElement("svg:rect");i.setAttributeNS(null,"x",t[0]),i.setAttributeNS(null,"y",t[1]),i.setAttributeNS(null,"width",h(a)),i.setAttributeNS(null,"height",h(r)),this.current.element=i,this.clip("nonzero"),this.endPath()}}},{key:"paintFormXObjectEnd",value:function(){}},{key:"_initialize",value:function(e){var t=this.svgFactory.create(e.width,e.height),a=this.svgFactory.createElement("svg:defs");t.append(a),this.defs=a;var r=this.svgFactory.createElement("svg:g");return r.setAttributeNS(null,"transform",d(e.transform)),t.append(r),this.svg=r,t}},{key:"_ensureClipGroup",value:function(){if(!this.current.clipGroup){var e=this.svgFactory.createElement("svg:g");e.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.append(e),this.current.clipGroup=e}return this.current.clipGroup}},{key:"_ensureTransformGroup",value:function(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",d(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().append(this.tgrp):this.svg.append(this.tgrp)),this.tgrp}}])}();t.SVGGraphics=m},function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.XfaText=void 0;var a=function(){function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e)}return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(e,null,[{key:"textContent",value:function(t){var a=[],r={items:a,styles:Object.create(null)};return function t(r){var i;if(r){var n=null,s=r.name;if("#text"===s)n=r.value;else{if(!e.shouldBuildText(s))return;null!=r&&null!==(i=r.attributes)&&void 0!==i&&i.textContent?n=r.attributes.textContent:r.value&&(n=r.value)}if(null!==n&&a.push({str:n}),r.children){var l,o=_createForOfIteratorHelper(r.children);try{for(o.s();!(l=o.n()).done;)t(l.value)}catch(e){o.e(e)}finally{o.f()}}}}(t),r}},{key:"shouldBuildText",value:function(e){return!("textarea"===e||"input"===e||"option"===e||"select"===e)}}])}();t.XfaText=a},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.TextLayerRenderTask=void 0,t.renderTextLayer=function(e){e.textContentSource||!e.textContent&&!e.textContentStream||((0,i.deprecated)("The TextLayerRender `textContent`/`textContentStream` parameters will be removed in the future, please use `textContentSource` instead."),e.textContentSource=e.textContent||e.textContentStream);var t=e.container,a=e.viewport,r=getComputedStyle(t),n=r.getPropertyValue("visibility"),s=parseFloat(r.getPropertyValue("--scale-factor"));"visible"===n&&(!s||Math.abs(s-a.scale)>1e-5)&&console.error("The `--scale-factor` CSS-variable must be set, to the same value as `viewport.scale`, either on the `container`-element itself or higher up in the DOM.");var l=new c(e);return l._render(),l},t.updateTextLayer=function(e){var t=e.container,a=e.viewport,r=e.textDivs,n=e.textDivProperties,s=e.isOffscreenCanvasSupported,o=e.mustRotate,c=void 0===o||o,u=e.mustRescale,h=void 0===u||u;if(c&&(0,i.setLayerDimensions)(t,{rotation:a.rotation}),h){var d,p=l(0,s),v={prevFontSize:null,prevFontFamily:null,div:null,scale:a.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:p},f=_createForOfIteratorHelper(r);try{for(f.s();!(d=f.n()).done;){var m=d.value;v.properties=n.get(m),v.div=m,_(v)}}catch(e){f.e(e)}finally{f.f()}}};var r=a(1),i=a(6),n=30,s=new Map;function l(e,t){var a;if(t&&r.FeatureTest.isOffscreenCanvasSupported)a=new OffscreenCanvas(e,e).getContext("2d",{alpha:!1});else{var i=document.createElement("canvas");i.width=i.height=e,a=i.getContext("2d",{alpha:!1})}return a}function o(e,t,a){var i=document.createElement("span"),o={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};e._textDivs.push(i);var _=r.Util.transform(e._transform,t.transform),c=Math.atan2(_[1],_[0]),u=a[t.fontName];u.vertical&&(c+=Math.PI/2);var h,d,p=Math.hypot(_[2],_[3]),v=p*function(e,t){var a=s.get(e);if(a)return a;var r=l(n,t);r.font="".concat(n,"px ").concat(e);var i=r.measureText(""),o=i.fontBoundingBoxAscent,_=Math.abs(i.fontBoundingBoxDescent);if(o){var c=o/(o+_);return s.set(e,c),r.canvas.width=r.canvas.height=0,c}r.strokeStyle="red",r.clearRect(0,0,n,n),r.strokeText("g",0,0);var u=r.getImageData(0,0,n,n).data;_=0;for(var h=u.length-1-3;h>=0;h-=4)if(u[h]>0){_=Math.ceil(h/4/n);break}r.clearRect(0,0,n,n),r.strokeText("A",0,n),o=0;for(var d=0,p=(u=r.getImageData(0,0,n,n).data).length;d<p;d+=4)if(u[d]>0){o=n-Math.floor(d/4/n);break}if(r.canvas.width=r.canvas.height=0,o){var v=o/(o+_);return s.set(e,v),v}return s.set(e,.8),.8}(u.fontFamily,e._isOffscreenCanvasSupported);0===c?(h=_[4],d=_[5]-v):(h=_[4]+v*Math.sin(c),d=_[5]-v*Math.cos(c));var f="calc(var(--scale-factor)*",m=i.style;e._container===e._rootContainer?(m.left="".concat((100*h/e._pageWidth).toFixed(2),"%"),m.top="".concat((100*d/e._pageHeight).toFixed(2),"%")):(m.left="".concat(f).concat(h.toFixed(2),"px)"),m.top="".concat(f).concat(d.toFixed(2),"px)")),m.fontSize="".concat(f).concat(p.toFixed(2),"px)"),m.fontFamily=u.fontFamily,o.fontSize=p,i.setAttribute("role","presentation"),i.textContent=t.str,i.dir=t.dir,e._fontInspectorEnabled&&(i.dataset.fontName=t.fontName),0!==c&&(o.angle=c*(180/Math.PI));var b=!1;if(t.str.length>1)b=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){var g=Math.abs(t.transform[0]),P=Math.abs(t.transform[3]);g!==P&&Math.max(g,P)/Math.min(g,P)>1.5&&(b=!0)}b&&(o.canvasWidth=u.vertical?t.height:t.width),e._textDivProperties.set(i,o),e._isReadableStream&&e._layoutText(i)}function _(e){var t=e.div,a=e.scale,r=e.properties,i=e.ctx,n=e.prevFontSize,s=e.prevFontFamily,l=t.style,o="";if(0!==r.canvasWidth&&r.hasText){var _=l.fontFamily,c=r.canvasWidth,u=r.fontSize;n===u&&s===_||(i.font="".concat(u*a,"px ").concat(_),e.prevFontSize=u,e.prevFontFamily=_);var h=i.measureText(t.textContent).width;h>0&&(o="scaleX(".concat(c*a/h,")"))}0!==r.angle&&(o="rotate(".concat(r.angle,"deg) ").concat(o)),o.length>0&&(l.transform=o)}var c=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a,n=this,s=t.textContentSource,o=t.container,_=t.viewport,c=t.textDivs,u=t.textDivProperties,h=t.textContentItemsStr,d=t.isOffscreenCanvasSupported;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),this._textContentSource=s,this._isReadableStream=s instanceof ReadableStream,this._container=this._rootContainer=o,this._textDivs=c||[],this._textContentItemsStr=h||[],this._isOffscreenCanvasSupported=d,this._fontInspectorEnabled=!(null===(a=globalThis.FontInspector)||void 0===a||!a.enabled),this._reader=null,this._textDivProperties=u||new WeakMap,this._canceled=!1,this._capability=new r.PromiseCapability,this._layoutTextParams={prevFontSize:null,prevFontFamily:null,div:null,scale:_.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:l(0,d)};var p=_.rawDims,v=p.pageWidth,f=p.pageHeight,m=p.pageX,b=p.pageY;this._transform=[1,0,0,-1,-m,b+f],this._pageWidth=v,this._pageHeight=f,(0,i.setLayerDimensions)(o,_),this._capability.promise.finally((function(){n._layoutTextParams=null})).catch((function(){}))}),[{key:"promise",get:function(){return this._capability.promise}},{key:"cancel",value:function(){this._canceled=!0,this._reader&&(this._reader.cancel(new r.AbortException("TextLayer task cancelled.")).catch((function(){})),this._reader=null),this._capability.reject(new r.AbortException("TextLayer task cancelled."))}},{key:"_processItems",value:function(e,t){var a,r=_createForOfIteratorHelper(e);try{for(r.s();!(a=r.n()).done;){var i=a.value;if(void 0!==i.str)this._textContentItemsStr.push(i.str),o(this,i,t);else if("beginMarkedContentProps"===i.type||"beginMarkedContent"===i.type){var n=this._container;this._container=document.createElement("span"),this._container.classList.add("markedContent"),null!==i.id&&this._container.setAttribute("id","".concat(i.id)),n.append(this._container)}else"endMarkedContent"===i.type&&(this._container=this._container.parentNode)}}catch(e){r.e(e)}finally{r.f()}}},{key:"_layoutText",value:function(e){var t=this._layoutTextParams.properties=this._textDivProperties.get(e);if(this._layoutTextParams.div=e,_(this._layoutTextParams),t.hasText&&this._container.append(e),t.hasEOL){var a=document.createElement("br");a.setAttribute("role","presentation"),this._container.append(a)}}},{key:"_render",value:function(){var e=this,t=new r.PromiseCapability,a=Object.create(null);if(this._isReadableStream){var i=function(){e._reader.read().then((function(r){var n=r.value;r.done?t.resolve():(Object.assign(a,n.styles),e._processItems(n.items,a),i())}),t.reject)};this._reader=this._textContentSource.getReader(),i()}else{if(!this._textContentSource)throw new Error('No "textContentSource" parameter specified.');var n=this._textContentSource,s=n.items,l=n.styles;this._processItems(s,l),t.resolve()}t.promise.then((function(){a=null,function(e){if(!e._canceled){var t=e._textDivs,a=e._capability;if(t.length>1e5)a.resolve();else{if(!e._isReadableStream){var r,i=_createForOfIteratorHelper(t);try{for(i.s();!(r=i.n()).done;){var n=r.value;e._layoutText(n)}}catch(e){i.e(e)}finally{i.f()}}a.resolve()}}}(e)}),this._capability.reject)}}])}();t.TextLayerRenderTask=c},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationEditorLayer=void 0;var r=a(1),i=a(4),n=a(28),s=a(33),l=a(6),o=a(34),_=new WeakMap,c=new WeakMap,u=new WeakMap,h=new WeakMap,d=new WeakMap,p=new WeakMap,v=new WeakMap,f=new WeakMap,m=new WeakMap,b=new WeakMap,g=new WeakSet,P=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a=t.uiManager,r=t.pageIndex,i=t.div,l=t.accessibilityManager,P=t.annotationLayer,y=t.viewport,A=t.l10n;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,g),_classPrivateFieldInitSpec(this,_,void 0),_classPrivateFieldInitSpec(this,c,!1),_classPrivateFieldInitSpec(this,u,null),_classPrivateFieldInitSpec(this,h,this.pointerup.bind(this)),_classPrivateFieldInitSpec(this,d,this.pointerdown.bind(this)),_classPrivateFieldInitSpec(this,p,new Map),_classPrivateFieldInitSpec(this,v,!1),_classPrivateFieldInitSpec(this,f,!1),_classPrivateFieldInitSpec(this,m,!1),_classPrivateFieldInitSpec(this,b,void 0);var E=[n.FreeTextEditor,s.InkEditor,o.StampEditor];if(!e._initialized){e._initialized=!0;var C,M=_createForOfIteratorHelper(E);try{for(M.s();!(C=M.n()).done;)C.value.initialize(A)}catch(e){M.e(e)}finally{M.f()}}a.registerEditorTypes(E),_classPrivateFieldSet(b,this,a),this.pageIndex=r,this.div=i,_classPrivateFieldSet(_,this,l),_classPrivateFieldSet(u,this,P),this.viewport=y,_classPrivateFieldGet(b,this).addLayer(this)}),[{key:"isEmpty",get:function(){return 0===_classPrivateFieldGet(p,this).size}},{key:"updateToolbar",value:function(e){_classPrivateFieldGet(b,this).updateToolbar(e)}},{key:"updateMode",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_classPrivateFieldGet(b,this).getMode();_assertClassBrand(g,this,C).call(this),e===r.AnnotationEditorType.INK?(this.addInkEditorIfNeeded(!1),this.disableClick()):this.enableClick(),e!==r.AnnotationEditorType.NONE&&(this.div.classList.toggle("freeTextEditing",e===r.AnnotationEditorType.FREETEXT),this.div.classList.toggle("inkEditing",e===r.AnnotationEditorType.INK),this.div.classList.toggle("stampEditing",e===r.AnnotationEditorType.STAMP),this.div.hidden=!1)}},{key:"addInkEditorIfNeeded",value:function(e){if(e||_classPrivateFieldGet(b,this).getMode()===r.AnnotationEditorType.INK){if(!e){var t,a=_createForOfIteratorHelper(_classPrivateFieldGet(p,this).values());try{for(a.s();!(t=a.n()).done;){var i=t.value;if(i.isEmpty())return void i.setInBackground()}}catch(e){a.e(e)}finally{a.f()}}_assertClassBrand(g,this,A).call(this,{offsetX:0,offsetY:0},!1).setInBackground()}}},{key:"setEditingState",value:function(e){_classPrivateFieldGet(b,this).setEditingState(e)}},{key:"addCommands",value:function(e){_classPrivateFieldGet(b,this).addCommands(e)}},{key:"enable",value:function(){this.div.style.pointerEvents="auto";var e,t=new Set,a=_createForOfIteratorHelper(_classPrivateFieldGet(p,this).values());try{for(a.s();!(e=a.n()).done;){var r=e.value;r.enableEditing(),r.annotationElementId&&t.add(r.annotationElementId)}}catch(e){a.e(e)}finally{a.f()}if(_classPrivateFieldGet(u,this)){var i,n=_createForOfIteratorHelper(_classPrivateFieldGet(u,this).getEditableAnnotations());try{for(n.s();!(i=n.n()).done;){var s=i.value;if(s.hide(),!_classPrivateFieldGet(b,this).isDeletedAnnotationElement(s.data.id)&&!t.has(s.data.id)){var l=this.deserialize(s);l&&(this.addOrRebuild(l),l.enableEditing())}}}catch(e){n.e(e)}finally{n.f()}}}},{key:"disable",value:function(){_classPrivateFieldSet(m,this,!0),this.div.style.pointerEvents="none";var e,t=new Set,a=_createForOfIteratorHelper(_classPrivateFieldGet(p,this).values());try{for(a.s();!(e=a.n()).done;){var r,i=e.value;i.disableEditing(),i.annotationElementId&&null===i.serialize()?(null===(r=this.getEditableAnnotation(i.annotationElementId))||void 0===r||r.show(),i.remove()):t.add(i.annotationElementId)}}catch(e){a.e(e)}finally{a.f()}if(_classPrivateFieldGet(u,this)){var n,s=_createForOfIteratorHelper(_classPrivateFieldGet(u,this).getEditableAnnotations());try{for(s.s();!(n=s.n()).done;){var l=n.value,o=l.data.id;t.has(o)||_classPrivateFieldGet(b,this).isDeletedAnnotationElement(o)||l.show()}}catch(e){s.e(e)}finally{s.f()}}_assertClassBrand(g,this,C).call(this),this.isEmpty&&(this.div.hidden=!0),_classPrivateFieldSet(m,this,!1)}},{key:"getEditableAnnotation",value:function(e){var t;return(null===(t=_classPrivateFieldGet(u,this))||void 0===t?void 0:t.getEditableAnnotation(e))||null}},{key:"setActiveEditor",value:function(e){_classPrivateFieldGet(b,this).getActive()!==e&&_classPrivateFieldGet(b,this).setActiveEditor(e)}},{key:"enableClick",value:function(){this.div.addEventListener("pointerdown",_classPrivateFieldGet(d,this)),this.div.addEventListener("pointerup",_classPrivateFieldGet(h,this))}},{key:"disableClick",value:function(){this.div.removeEventListener("pointerdown",_classPrivateFieldGet(d,this)),this.div.removeEventListener("pointerup",_classPrivateFieldGet(h,this))}},{key:"attach",value:function(e){_classPrivateFieldGet(p,this).set(e.id,e);var t=e.annotationElementId;t&&_classPrivateFieldGet(b,this).isDeletedAnnotationElement(t)&&_classPrivateFieldGet(b,this).removeDeletedAnnotationElement(e)}},{key:"detach",value:function(e){var t;_classPrivateFieldGet(p,this).delete(e.id),null===(t=_classPrivateFieldGet(_,this))||void 0===t||t.removePointerInTextLayer(e.contentDiv),!_classPrivateFieldGet(m,this)&&e.annotationElementId&&_classPrivateFieldGet(b,this).addDeletedAnnotationElement(e)}},{key:"remove",value:function(e){var t=this;this.detach(e),_classPrivateFieldGet(b,this).removeEditor(e),e.div.contains(document.activeElement)&&setTimeout((function(){_classPrivateFieldGet(b,t).focusMainContainer()}),0),e.div.remove(),e.isAttachedToDOM=!1,_classPrivateFieldGet(f,this)||this.addInkEditorIfNeeded(!1)}},{key:"changeParent",value:function(e){var t;e.parent!==this&&(e.annotationElementId&&(_classPrivateFieldGet(b,this).addDeletedAnnotationElement(e.annotationElementId),i.AnnotationEditor.deleteAnnotationElement(e),e.annotationElementId=null),this.attach(e),null===(t=e.parent)||void 0===t||t.detach(e),e.setParent(this),e.div&&e.isAttachedToDOM&&(e.div.remove(),this.div.append(e.div)))}},{key:"add",value:function(e){if(this.changeParent(e),_classPrivateFieldGet(b,this).addEditor(e),this.attach(e),!e.isAttachedToDOM){var t=e.render();this.div.append(t),e.isAttachedToDOM=!0}e.fixAndSetPosition(),e.onceAdded(),_classPrivateFieldGet(b,this).addToAnnotationStorage(e)}},{key:"moveEditorInDOM",value:function(e){var t;if(e.isAttachedToDOM){var a=document.activeElement;e.div.contains(a)&&(e._focusEventsAllowed=!1,setTimeout((function(){e.div.contains(document.activeElement)?e._focusEventsAllowed=!0:(e.div.addEventListener("focusin",(function(){e._focusEventsAllowed=!0}),{once:!0}),a.focus())}),0)),e._structTreeParentId=null===(t=_classPrivateFieldGet(_,this))||void 0===t?void 0:t.moveElementInDOM(this.div,e.div,e.contentDiv,!0)}}},{key:"addOrRebuild",value:function(e){e.needsToBeRebuilt()?e.rebuild():this.add(e)}},{key:"addUndoableEditor",value:function(e){this.addCommands({cmd:function(){return e._uiManager.rebuild(e)},undo:function(){e.remove()},mustExec:!1})}},{key:"getNextId",value:function(){return _classPrivateFieldGet(b,this).getId()}},{key:"pasteEditor",value:function(e,t){_classPrivateFieldGet(b,this).updateToolbar(e),_classPrivateFieldGet(b,this).updateMode(e);var a=_assertClassBrand(g,this,E).call(this),r=a.offsetX,i=a.offsetY,n=this.getNextId(),s=_assertClassBrand(g,this,y).call(this,_objectSpread({parent:this,id:n,x:r,y:i,uiManager:_classPrivateFieldGet(b,this),isCentered:!0},t));s&&this.add(s)}},{key:"deserialize",value:function(e){var t;switch(null!==(t=e.annotationType)&&void 0!==t?t:e.annotationEditorType){case r.AnnotationEditorType.FREETEXT:return n.FreeTextEditor.deserialize(e,this,_classPrivateFieldGet(b,this));case r.AnnotationEditorType.INK:return s.InkEditor.deserialize(e,this,_classPrivateFieldGet(b,this));case r.AnnotationEditorType.STAMP:return o.StampEditor.deserialize(e,this,_classPrivateFieldGet(b,this))}return null}},{key:"addNewEditor",value:function(){_assertClassBrand(g,this,A).call(this,_assertClassBrand(g,this,E).call(this),!0)}},{key:"setSelected",value:function(e){_classPrivateFieldGet(b,this).setSelected(e)}},{key:"toggleSelected",value:function(e){_classPrivateFieldGet(b,this).toggleSelected(e)}},{key:"isSelected",value:function(e){return _classPrivateFieldGet(b,this).isSelected(e)}},{key:"unselect",value:function(e){_classPrivateFieldGet(b,this).unselect(e)}},{key:"pointerup",value:function(e){var t=r.FeatureTest.platform.isMac;0!==e.button||e.ctrlKey&&t||e.target===this.div&&_classPrivateFieldGet(v,this)&&(_classPrivateFieldSet(v,this,!1),_classPrivateFieldGet(c,this)?_classPrivateFieldGet(b,this).getMode()!==r.AnnotationEditorType.STAMP?_assertClassBrand(g,this,A).call(this,e,!1):_classPrivateFieldGet(b,this).unselectAll():_classPrivateFieldSet(c,this,!0))}},{key:"pointerdown",value:function(e){if(_classPrivateFieldGet(v,this))_classPrivateFieldSet(v,this,!1);else{var t=r.FeatureTest.platform.isMac;if(!(0!==e.button||e.ctrlKey&&t)&&e.target===this.div){_classPrivateFieldSet(v,this,!0);var a=_classPrivateFieldGet(b,this).getActive();_classPrivateFieldSet(c,this,!a||a.isEmpty())}}}},{key:"findNewParent",value:function(e,t,a){var r=_classPrivateFieldGet(b,this).findParent(t,a);return null!==r&&r!==this&&(r.changeParent(e),!0)}},{key:"destroy",value:function(){var e;(null===(e=_classPrivateFieldGet(b,this).getActive())||void 0===e?void 0:e.parent)===this&&(_classPrivateFieldGet(b,this).commitOrRemove(),_classPrivateFieldGet(b,this).setActiveEditor(null));var t,a=_createForOfIteratorHelper(_classPrivateFieldGet(p,this).values());try{for(a.s();!(t=a.n()).done;){var r,i=t.value;null===(r=_classPrivateFieldGet(_,this))||void 0===r||r.removePointerInTextLayer(i.contentDiv),i.setParent(null),i.isAttachedToDOM=!1,i.div.remove()}}catch(e){a.e(e)}finally{a.f()}this.div=null,_classPrivateFieldGet(p,this).clear(),_classPrivateFieldGet(b,this).removeLayer(this)}},{key:"render",value:function(e){var t=e.viewport;this.viewport=t,(0,l.setLayerDimensions)(this.div,t);var a,r=_createForOfIteratorHelper(_classPrivateFieldGet(b,this).getEditors(this.pageIndex));try{for(r.s();!(a=r.n()).done;){var i=a.value;this.add(i)}}catch(e){r.e(e)}finally{r.f()}this.updateMode()}},{key:"update",value:function(e){var t=e.viewport;_classPrivateFieldGet(b,this).commitOrRemove(),this.viewport=t,(0,l.setLayerDimensions)(this.div,{rotation:t.rotation}),this.updateMode()}},{key:"pageDimensions",get:function(){var e=this.viewport.rawDims;return[e.pageWidth,e.pageHeight]}}])}();function y(e){switch(_classPrivateFieldGet(b,this).getMode()){case r.AnnotationEditorType.FREETEXT:return new n.FreeTextEditor(e);case r.AnnotationEditorType.INK:return new s.InkEditor(e);case r.AnnotationEditorType.STAMP:return new o.StampEditor(e)}return null}function A(e,t){var a=this.getNextId(),r=_assertClassBrand(g,this,y).call(this,{parent:this,id:a,x:e.offsetX,y:e.offsetY,uiManager:_classPrivateFieldGet(b,this),isCentered:t});return r&&this.add(r),r}function E(){var e=this.div.getBoundingClientRect(),t=e.x,a=e.y,r=e.width,i=e.height,n=Math.max(0,t),s=Math.max(0,a),l=(n+Math.min(window.innerWidth,t+r))/2-t,o=(s+Math.min(window.innerHeight,a+i))/2-a,_=this.viewport.rotation%180==0?[l,o]:[o,l],c=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(_,2);return{offsetX:c[0],offsetY:c[1]}}function C(){_classPrivateFieldSet(f,this,!0);var e,t=_createForOfIteratorHelper(_classPrivateFieldGet(p,this).values());try{for(t.s();!(e=t.n()).done;){var a=e.value;a.isEmpty()&&a.remove()}}catch(e){t.e(e)}finally{t.f()}_classPrivateFieldSet(f,this,!1)}(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(P,"_initialized",!1),t.AnnotationEditorLayer=P},function(e,t,a){var r;Object.defineProperty(t,"__esModule",{value:!0}),t.FreeTextEditor=void 0;var i=a(1),n=a(5),s=a(4),l=a(29),o=new WeakMap,_=new WeakMap,c=new WeakMap,u=new WeakMap,h=new WeakMap,d=new WeakMap,p=new WeakMap,v=new WeakMap,f=new WeakMap,m=new WeakSet,b=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateMethodInitSpec(a=_callSuper(this,t,[_objectSpread(_objectSpread({},e),{},{name:"freeTextEditor"})]),m),_classPrivateFieldInitSpec(a,o,a.editorDivBlur.bind(a)),_classPrivateFieldInitSpec(a,_,a.editorDivFocus.bind(a)),_classPrivateFieldInitSpec(a,c,a.editorDivInput.bind(a)),_classPrivateFieldInitSpec(a,u,a.editorDivKeydown.bind(a)),_classPrivateFieldInitSpec(a,h,void 0),_classPrivateFieldInitSpec(a,d,""),_classPrivateFieldInitSpec(a,p,"".concat(a.id,"-editor")),_classPrivateFieldInitSpec(a,v,void 0),_classPrivateFieldInitSpec(a,f,null),_classPrivateFieldSet(h,a,e.color||t._defaultColor||s.AnnotationEditor._defaultLineColor),_classPrivateFieldSet(v,a,e.fontSize||t._defaultFontSize),a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"updateParams",value:function(e,t){switch(e){case i.AnnotationEditorParamsType.FREETEXT_SIZE:_assertClassBrand(m,this,g).call(this,t);break;case i.AnnotationEditorParamsType.FREETEXT_COLOR:_assertClassBrand(m,this,P).call(this,t)}}},{key:"propertiesToUpdate",get:function(){return[[i.AnnotationEditorParamsType.FREETEXT_SIZE,_classPrivateFieldGet(v,this)],[i.AnnotationEditorParamsType.FREETEXT_COLOR,_classPrivateFieldGet(h,this)]]}},{key:"_translateEmpty",value:function(e,t){this._uiManager.translateSelectedEditors(e,t,!0)}},{key:"getInitialTranslation",value:function(){var e=this.parentScale;return[-t._internalPadding*e,-(t._internalPadding+_classPrivateFieldGet(v,this))*e]}},{key:"rebuild",value:function(){this.parent&&(_superPropGet(t,"rebuild",this,3)([]),null!==this.div&&(this.isAttachedToDOM||this.parent.add(this)))}},{key:"enableEditMode",value:function(){this.isInEditMode()||(this.parent.setEditingState(!1),this.parent.updateToolbar(i.AnnotationEditorType.FREETEXT),_superPropGet(t,"enableEditMode",this,3)([]),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.editorDiv.addEventListener("keydown",_classPrivateFieldGet(u,this)),this.editorDiv.addEventListener("focus",_classPrivateFieldGet(_,this)),this.editorDiv.addEventListener("blur",_classPrivateFieldGet(o,this)),this.editorDiv.addEventListener("input",_classPrivateFieldGet(c,this)))}},{key:"disableEditMode",value:function(){this.isInEditMode()&&(this.parent.setEditingState(!0),_superPropGet(t,"disableEditMode",this,3)([]),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",_classPrivateFieldGet(p,this)),this._isDraggable=!0,this.editorDiv.removeEventListener("keydown",_classPrivateFieldGet(u,this)),this.editorDiv.removeEventListener("focus",_classPrivateFieldGet(_,this)),this.editorDiv.removeEventListener("blur",_classPrivateFieldGet(o,this)),this.editorDiv.removeEventListener("input",_classPrivateFieldGet(c,this)),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freeTextEditing"))}},{key:"focusin",value:function(e){this._focusEventsAllowed&&(_superPropGet(t,"focusin",this,3)([e]),e.target!==this.editorDiv&&this.editorDiv.focus())}},{key:"onceAdded",value:function(){var e;this.width?_assertClassBrand(m,this,M).call(this):(this.enableEditMode(),this.editorDiv.focus(),null!==(e=this._initialOptions)&&void 0!==e&&e.isCentered&&this.center(),this._initialOptions=null)}},{key:"isEmpty",value:function(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}},{key:"remove",value:function(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freeTextEditing")),_superPropGet(t,"remove",this,3)([])}},{key:"commit",value:function(){var e=this;if(this.isInEditMode()){_superPropGet(t,"commit",this,3)([]),this.disableEditMode();var a=_classPrivateFieldGet(d,this),r=_classPrivateFieldSet(d,this,_assertClassBrand(m,this,y).call(this).trimEnd());if(a!==r){var i=function(t){_classPrivateFieldSet(d,e,t),t?(_assertClassBrand(m,e,E).call(e),e._uiManager.rebuild(e),_assertClassBrand(m,e,A).call(e)):e.remove()};this.addCommands({cmd:function(){i(r)},undo:function(){i(a)},mustExec:!1}),_assertClassBrand(m,this,A).call(this)}}}},{key:"shouldGetKeyboardEvents",value:function(){return this.isInEditMode()}},{key:"enterInEditMode",value:function(){this.enableEditMode(),this.editorDiv.focus()}},{key:"dblclick",value:function(e){this.enterInEditMode()}},{key:"keydown",value:function(e){e.target===this.div&&"Enter"===e.key&&(this.enterInEditMode(),e.preventDefault())}},{key:"editorDivKeydown",value:function(e){t._keyboardManager.exec(this,e)}},{key:"editorDivFocus",value:function(e){this.isEditing=!0}},{key:"editorDivBlur",value:function(e){this.isEditing=!1}},{key:"editorDivInput",value:function(e){this.parent.div.classList.toggle("freeTextEditing",this.isEmpty())}},{key:"disableEditing",value:function(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}},{key:"enableEditing",value:function(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}},{key:"render",value:function(){var e,a,r=this;if(this.div)return this.div;this.width&&(e=this.x,a=this.y),_superPropGet(t,"render",this,3)([]),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",_classPrivateFieldGet(p,this)),this.enableEditing(),s.AnnotationEditor._l10nPromise.get("editor_free_text2_aria_label").then((function(e){var t;return null===(t=r.editorDiv)||void 0===t?void 0:t.setAttribute("aria-label",e)})),s.AnnotationEditor._l10nPromise.get("free_text2_default_content").then((function(e){var t;return null===(t=r.editorDiv)||void 0===t?void 0:t.setAttribute("default-content",e)})),this.editorDiv.contentEditable=!0;var i=this.editorDiv.style;if(i.fontSize="calc(".concat(_classPrivateFieldGet(v,this),"px * var(--scale-factor))"),i.color=_classPrivateFieldGet(h,this),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),(0,n.bindEvents)(this,this.div,["dblclick","keydown"]),this.width){var l=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),o=l[0],_=l[1];if(this.annotationElementId){var c=_classPrivateFieldGet(f,this).position,u=this.getInitialTranslation(),d=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(u,2),b=d[0],g=d[1],P=this.pageTranslationToScreen(b,g),y=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(P,2);b=y[0],g=y[1];var A,C,M=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageDimensions,2),k=M[0],O=M[1],D=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageTranslation,2),F=D[0],S=D[1];switch(this.rotation){case 0:A=e+(c[0]-F)/k,C=a+this.height-(c[1]-S)/O;break;case 90:A=e+(c[0]-F)/k,C=a-(c[1]-S)/O;var T=[g,-b];b=T[0],g=T[1];break;case 180:A=e-this.width+(c[0]-F)/k,C=a-(c[1]-S)/O;var I=[-b,-g];b=I[0],g=I[1];break;case 270:A=e+(c[0]-F-this.height*O)/k,C=a+(c[1]-S-this.width*k)/O;var w=[-g,b];b=w[0],g=w[1]}this.setAt(A*o,C*_,b,g)}else this.setAt(e*o,a*_,this.width*o,this.height*_);_assertClassBrand(m,this,E).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}},{key:"contentDiv",get:function(){return this.editorDiv}},{key:"serialize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};var a=t._internalPadding*this.parentScale,r=this.getRect(a,a),n=s.AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:_classPrivateFieldGet(h,this)),l={annotationType:i.AnnotationEditorType.FREETEXT,color:n,fontSize:_classPrivateFieldGet(v,this),value:_classPrivateFieldGet(d,this),pageIndex:this.pageIndex,rect:r,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?l:this.annotationElementId&&!_assertClassBrand(m,this,C).call(this,l)?null:(l.id=this.annotationElementId,l)}}],[{key:"_keyboardManager",get:function(){var e=t.prototype,a=function(e){return e.isEmpty()},r=n.AnnotationEditorUIManager.TRANSLATE_SMALL,s=n.AnnotationEditorUIManager.TRANSLATE_BIG;return(0,i.shadow)(this,"_keyboardManager",new n.KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],e.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],e.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],e._translateEmpty,{args:[-r,0],checker:a}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],e._translateEmpty,{args:[-s,0],checker:a}],[["ArrowRight","mac+ArrowRight"],e._translateEmpty,{args:[r,0],checker:a}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],e._translateEmpty,{args:[s,0],checker:a}],[["ArrowUp","mac+ArrowUp"],e._translateEmpty,{args:[0,-r],checker:a}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],e._translateEmpty,{args:[0,-s],checker:a}],[["ArrowDown","mac+ArrowDown"],e._translateEmpty,{args:[0,r],checker:a}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],e._translateEmpty,{args:[0,s],checker:a}]]))}},{key:"initialize",value:function(e){s.AnnotationEditor.initialize(e,{strings:["free_text2_default_content","editor_free_text2_aria_label"]});var t=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(t.getPropertyValue("--freetext-padding"))}},{key:"updateDefaultParams",value:function(e,a){switch(e){case i.AnnotationEditorParamsType.FREETEXT_SIZE:t._defaultFontSize=a;break;case i.AnnotationEditorParamsType.FREETEXT_COLOR:t._defaultColor=a}}},{key:"defaultPropertiesToUpdate",get:function(){return[[i.AnnotationEditorParamsType.FREETEXT_SIZE,t._defaultFontSize],[i.AnnotationEditorParamsType.FREETEXT_COLOR,t._defaultColor||s.AnnotationEditor._defaultLineColor]]}},{key:"deserialize",value:function(e,a,r){var n,s=null;if(e instanceof l.FreeTextAnnotationElement){var o=e,_=o.data,c=_.defaultAppearanceData,u=c.fontSize,p=c.fontColor,m=_.rect,b=_.rotation,g=_.id,P=o.textContent,y=o.textPosition,A=o.parent.page.pageNumber;if(!P||0===P.length)return null;s=e={annotationType:i.AnnotationEditorType.FREETEXT,color:Array.from(p),fontSize:u,value:P.join("\n"),position:y,pageIndex:A-1,rect:m,rotation:b,id:g,deleted:!1}}var E=_superPropGet(t,"deserialize",this,2)([e,a,r]);return _classPrivateFieldSet(v,E,e.fontSize),_classPrivateFieldSet(h,E,(n=i.Util).makeHexColor.apply(n,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(e.color))),_classPrivateFieldSet(d,E,e.value),E.annotationElementId=e.id||null,_classPrivateFieldSet(f,E,s),E}}])}(s.AnnotationEditor);function g(e){var t=this,a=function(e){t.editorDiv.style.fontSize="calc(".concat(e,"px * var(--scale-factor))"),t.translate(0,-(e-_classPrivateFieldGet(v,t))*t.parentScale),_classPrivateFieldSet(v,t,e),_assertClassBrand(m,t,A).call(t)},r=_classPrivateFieldGet(v,this);this.addCommands({cmd:function(){a(e)},undo:function(){a(r)},mustExec:!0,type:i.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}function P(e){var t=this,a=_classPrivateFieldGet(h,this);this.addCommands({cmd:function(){_classPrivateFieldSet(h,t,t.editorDiv.style.color=e)},undo:function(){_classPrivateFieldSet(h,t,t.editorDiv.style.color=a)},mustExec:!0,type:i.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}function y(){var e=this.editorDiv.getElementsByTagName("div");if(0===e.length)return this.editorDiv.innerText;var t,a=[],r=_createForOfIteratorHelper(e);try{for(r.s();!(t=r.n()).done;){var i=t.value;a.push(i.innerText.replace(/\r\n?|\n/,""))}}catch(e){r.e(e)}finally{r.f()}return a.join("\n")}function A(){var e,t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),a=t[0],r=t[1];if(this.isAttachedToDOM)e=this.div.getBoundingClientRect();else{var i=this.currentLayer,n=this.div,s=n.style.display;n.style.display="hidden",i.div.append(this.div),e=n.getBoundingClientRect(),n.remove(),n.style.display=s}this.rotation%180==this.parentRotation%180?(this.width=e.width/a,this.height=e.height/r):(this.width=e.height/a,this.height=e.width/r),this.fixAndSetPosition()}function E(){if(this.editorDiv.replaceChildren(),_classPrivateFieldGet(d,this)){var e,t=_createForOfIteratorHelper(_classPrivateFieldGet(d,this).split("\n"));try{for(t.s();!(e=t.n()).done;){var a=e.value,r=document.createElement("div");r.append(a?document.createTextNode(a):document.createElement("br")),this.editorDiv.append(r)}}catch(e){t.e(e)}finally{t.f()}}}function C(e){var t=_classPrivateFieldGet(f,this),a=t.value,r=t.fontSize,i=t.color,n=t.rect,s=t.pageIndex;return e.value!==a||e.fontSize!==r||e.rect.some((function(e,t){return Math.abs(e-n[t])>=1}))||e.color.some((function(e,t){return e!==i[t]}))||e.pageIndex!==s}function M(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.annotationElementId)if(_assertClassBrand(m,this,A).call(this),t||0!==this.width&&0!==this.height){var a=r._internalPadding*this.parentScale;_classPrivateFieldGet(f,this).rect=this.getRect(a,a)}else setTimeout((function(){return _assertClassBrand(m,e,M).call(e,!0)}),0)}r=b,(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(b,"_freeTextDefaultContent",""),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(b,"_internalPadding",0),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(b,"_defaultColor",null),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(b,"_defaultFontSize",10),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(b,"_type","freetext"),t.FreeTextEditor=b},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.StampAnnotationElement=t.InkAnnotationElement=t.FreeTextAnnotationElement=t.AnnotationLayer=void 0;var r=a(1),i=a(6),n=a(3),s=a(30),l=a(31),o=a(32),_=1e3,c=new WeakSet;function u(e){return{width:e[2]-e[0],height:e[3]-e[1]}}var h=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e)}),null,[{key:"create",value:function(e){switch(e.data.annotationType){case r.AnnotationType.LINK:return new f(e);case r.AnnotationType.TEXT:return new g(e);case r.AnnotationType.WIDGET:switch(e.data.fieldType){case"Tx":return new y(e);case"Btn":return e.data.radioButton?new C(e):e.data.checkBox?new E(e):new M(e);case"Ch":return new k(e);case"Sig":return new A(e)}return new P(e);case r.AnnotationType.POPUP:return new O(e);case r.AnnotationType.FREETEXT:return new Q(e);case r.AnnotationType.LINE:return new $(e);case r.AnnotationType.SQUARE:return new te(e);case r.AnnotationType.CIRCLE:return new re(e);case r.AnnotationType.POLYLINE:return new ne(e);case r.AnnotationType.CARET:return new le(e);case r.AnnotationType.INK:return new _e(e);case r.AnnotationType.POLYGON:return new se(e);case r.AnnotationType.HIGHLIGHT:return new ce(e);case r.AnnotationType.UNDERLINE:return new ue(e);case r.AnnotationType.SQUIGGLY:return new he(e);case r.AnnotationType.STRIKEOUT:return new de(e);case r.AnnotationType.STAMP:return new pe(e);case r.AnnotationType.FILEATTACHMENT:return new me(e);default:return new p(e)}}}])}(),d=new WeakMap,p=function(){function e(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=a.isRenderable,i=void 0!==r&&r,n=a.ignoreBorder,s=void 0!==n&&n,l=a.createQuadrilaterals,o=void 0!==l&&l;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateFieldInitSpec(this,d,!1),this.isRenderable=i,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,i&&(this.container=this._createContainer(s)),o&&this._createQuadrilaterals()}return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(e,[{key:"hasPopupData",get:function(){return e._hasPopupData(this.data)}},{key:"_createContainer",value:function(e){var t=this.data,a=this.parent,i=a.page,n=a.viewport,s=document.createElement("section");s.setAttribute("data-annotation-id",t.id),this instanceof P||(s.tabIndex=_),s.style.zIndex=this.parent.zIndex++,this.data.popupRef&&s.setAttribute("aria-haspopup","dialog"),t.noRotate&&s.classList.add("norotate");var l=n.rawDims,o=l.pageWidth,c=l.pageHeight,h=l.pageX,p=l.pageY;if(!t.rect||this instanceof O){var v=t.rotation;return t.hasOwnCanvas||0===v||this.setRotation(v,s),s}var f=u(t.rect),m=f.width,b=f.height,g=r.Util.normalizeRect([t.rect[0],i.view[3]-t.rect[1]+i.view[1],t.rect[2],i.view[3]-t.rect[3]+i.view[1]]);if(!e&&t.borderStyle.width>0){s.style.borderWidth="".concat(t.borderStyle.width,"px");var y=t.borderStyle.horizontalCornerRadius,A=t.borderStyle.verticalCornerRadius;if(y>0||A>0){var E="calc(".concat(y,"px * var(--scale-factor)) / calc(").concat(A,"px * var(--scale-factor))");s.style.borderRadius=E}else if(this instanceof C){var M="calc(".concat(m,"px * var(--scale-factor)) / calc(").concat(b,"px * var(--scale-factor))");s.style.borderRadius=M}switch(t.borderStyle.style){case r.AnnotationBorderStyleType.SOLID:s.style.borderStyle="solid";break;case r.AnnotationBorderStyleType.DASHED:s.style.borderStyle="dashed";break;case r.AnnotationBorderStyleType.BEVELED:(0,r.warn)("Unimplemented border style: beveled");break;case r.AnnotationBorderStyleType.INSET:(0,r.warn)("Unimplemented border style: inset");break;case r.AnnotationBorderStyleType.UNDERLINE:s.style.borderBottomStyle="solid"}var k=t.borderColor||null;k?(_classPrivateFieldSet(d,this,!0),s.style.borderColor=r.Util.makeHexColor(0|k[0],0|k[1],0|k[2])):s.style.borderWidth=0}s.style.left="".concat(100*(g[0]-h)/o,"%"),s.style.top="".concat(100*(g[1]-p)/c,"%");var D=t.rotation;return t.hasOwnCanvas||0===D?(s.style.width="".concat(100*m/o,"%"),s.style.height="".concat(100*b/c,"%")):this.setRotation(D,s),s}},{key:"setRotation",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.container;if(this.data.rect){var a,r,i=this.parent.viewport.rawDims,n=i.pageWidth,s=i.pageHeight,l=u(this.data.rect),o=l.width,_=l.height;e%180==0?(a=100*o/n,r=100*_/s):(a=100*_/n,r=100*o/s),t.style.width="".concat(a,"%"),t.style.height="".concat(r,"%"),t.setAttribute("data-main-rotation",(360-e)%360)}}},{key:"_commonActions",get:function(){var e=this,t=function(t,a,r){var i=r.detail[t],n=i[0],l=i.slice(1);r.target.style[a]=s.ColorConverters["".concat(n,"_HTML")](l),e.annotationStorage.setValue(e.data.id,(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)({},a,s.ColorConverters["".concat(n,"_rgb")](l)))};return(0,r.shadow)(this,"_commonActions",{display:function(t){var a=t.detail.display,r=a%2==1;e.container.style.visibility=r?"hidden":"visible",e.annotationStorage.setValue(e.data.id,{noView:r,noPrint:1===a||2===a})},print:function(t){e.annotationStorage.setValue(e.data.id,{noPrint:!t.detail.print})},hidden:function(t){var a=t.detail.hidden;e.container.style.visibility=a?"hidden":"visible",e.annotationStorage.setValue(e.data.id,{noPrint:a,noView:a})},focus:function(e){setTimeout((function(){return e.target.focus({preventScroll:!1})}),0)},userName:function(e){e.target.title=e.detail.userName},readonly:function(e){e.target.disabled=e.detail.readonly},required:function(t){e._setRequired(t.target,t.detail.required)},bgColor:function(e){t("bgColor","backgroundColor",e)},fillColor:function(e){t("fillColor","backgroundColor",e)},fgColor:function(e){t("fgColor","color",e)},textColor:function(e){t("textColor","color",e)},borderColor:function(e){t("borderColor","borderColor",e)},strokeColor:function(e){t("strokeColor","borderColor",e)},rotation:function(t){var a=t.detail.rotation;e.setRotation(a),e.annotationStorage.setValue(e.data.id,{rotation:a})}})}},{key:"_dispatchEventFromSandbox",value:function(e,t){for(var a=this._commonActions,r=0,i=Object.keys(t.detail);r<i.length;r++){var n=i[r],s=e[n]||a[n];null==s||s(t)}}},{key:"_setDefaultPropertiesFromJS",value:function(e){if(this.enableScripting){var t=this.annotationStorage.getRawValue(this.data.id);if(t)for(var a=this._commonActions,r=0,i=Object.entries(t);r<i.length;r++){var n=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(i[r],2),s=n[0],l=n[1],o=a[s];o&&(o({detail:(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)({},s,l),target:e}),delete t[s])}}}},{key:"_createQuadrilaterals",value:function(){if(this.container){var e=this.data.quadPoints;if(e){var t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.data.rect,4),a=t[0],r=t[1],i=t[2],n=t[3];if(1===e.length){var s=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e[0],3),l=s[1],o=l.x,_=l.y,c=s[2],u=c.x,h=c.y;if(i===o&&n===_&&a===u&&r===h)return}var p,v=this.container.style;if(_classPrivateFieldGet(d,this)){var f=v.borderColor,m=v.borderWidth;v.borderWidth=0,p=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">','<g fill="transparent" stroke="'.concat(f,'" stroke-width="').concat(m,'">')],this.container.classList.add("hasBorder")}var b=i-a,g=n-r,P=this.svgFactory,y=P.createElement("svg");y.classList.add("quadrilateralsContainer"),y.setAttribute("width",0),y.setAttribute("height",0);var A=P.createElement("defs");y.append(A);var E=P.createElement("clipPath"),C="clippath_".concat(this.data.id);E.setAttribute("id",C),E.setAttribute("clipPathUnits","objectBoundingBox"),A.append(E);var M,k=_createForOfIteratorHelper(e);try{for(k.s();!(M=k.n()).done;){var O,D=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(M.value,3),F=D[1],S=F.x,T=F.y,I=D[2],w=I.x,x=I.y,R=P.createElement("rect"),L=(w-a)/b,B=(n-T)/g,W=(S-w)/b,U=(T-x)/g;R.setAttribute("x",L),R.setAttribute("y",B),R.setAttribute("width",W),R.setAttribute("height",U),E.append(R),null===(O=p)||void 0===O||O.push('<rect vector-effect="non-scaling-stroke" x="'.concat(L,'" y="').concat(B,'" width="').concat(W,'" height="').concat(U,'"/>'))}}catch(e){k.e(e)}finally{k.f()}_classPrivateFieldGet(d,this)&&(p.push("</g></svg>')"),v.backgroundImage=p.join("")),this.container.append(y),this.container.style.clipPath="url(#".concat(C,")")}}}},{key:"_createPopup",value:function(){var e=this.container,t=this.data;e.setAttribute("aria-haspopup","dialog");var a=new O({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:"popup_".concat(t.id),rotation:t.rotation},parent:this.parent,elements:[this]});this.parent.div.append(a.render())}},{key:"render",value:function(){(0,r.unreachable)("Abstract method `AnnotationElement.render` called")}},{key:"_getElementsByName",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=[];if(this._fieldObjects){var i=this._fieldObjects[e];if(i){var n,s=_createForOfIteratorHelper(i);try{for(s.s();!(n=s.n()).done;){var l=n.value,o=l.page,_=l.id,u=l.exportValues;if(-1!==o&&_!==t){var h="string"==typeof u?u:null,d=document.querySelector('[data-element-id="'.concat(_,'"]'));!d||c.has(d)?a.push({id:_,exportValue:h,domElement:d}):(0,r.warn)("_getElementsByName - element not allowed: ".concat(_))}}}catch(e){s.e(e)}finally{s.f()}}return a}var p,v=_createForOfIteratorHelper(document.getElementsByName(e));try{for(v.s();!(p=v.n()).done;){var f=p.value,m=f.exportValue,b=f.getAttribute("data-element-id");b!==t&&c.has(f)&&a.push({id:b,exportValue:m,domElement:f})}}catch(e){v.e(e)}finally{v.f()}return a}},{key:"show",value:function(){var e;this.container&&(this.container.hidden=!1),null===(e=this.popup)||void 0===e||e.maybeShow()}},{key:"hide",value:function(){var e;this.container&&(this.container.hidden=!0),null===(e=this.popup)||void 0===e||e.forceHide()}},{key:"getElementsToTriggerPopup",value:function(){return this.container}},{key:"addHighlightArea",value:function(){var e=this.getElementsToTriggerPopup();if(Array.isArray(e)){var t,a=_createForOfIteratorHelper(e);try{for(a.s();!(t=a.n()).done;)t.value.classList.add("highlightArea")}catch(e){a.e(e)}finally{a.f()}}else e.classList.add("highlightArea")}},{key:"_editOnDoubleClick",value:function(){var e=this,t=this.annotationEditorType,a=this.data.id;this.container.addEventListener("dblclick",(function(){var r;null===(r=e.linkService.eventBus)||void 0===r||r.dispatch("switchannotationeditormode",{source:e,mode:t,editId:a})}))}}],[{key:"_hasPopupData",value:function(e){var t=e.titleObj,a=e.contentsObj,r=e.richText;return!!(null!=t&&t.str||null!=a&&a.str||null!=r&&r.str)}}])}(),v=new WeakSet,f=function(e){function t(e){var a,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateMethodInitSpec(a=_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!(null==r||!r.ignoreBorder),createQuadrilaterals:!0}]),v),a.isTooltipOnly=e.data.isTooltipOnly,a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){var e=this.data,t=this.linkService,a=document.createElement("a");a.setAttribute("data-element-id",e.id);var r=!1;return e.url?(t.addLinkAttributes(a,e.url,e.newWindow),r=!0):e.action?(this._bindNamedAction(a,e.action),r=!0):e.attachment?(this._bindAttachment(a,e.attachment),r=!0):e.setOCGState?(_assertClassBrand(v,this,b).call(this,a,e.setOCGState),r=!0):e.dest?(this._bindLink(a,e.dest),r=!0):(e.actions&&(e.actions.Action||e.actions["Mouse Up"]||e.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(a,e),r=!0),e.resetForm?(this._bindResetFormAction(a,e.resetForm),r=!0):this.isTooltipOnly&&!r&&(this._bindLink(a,""),r=!0)),this.container.classList.add("linkAnnotation"),r&&this.container.append(a),this.container}},{key:"_bindLink",value:function(e,t){var a=this;e.href=this.linkService.getDestinationHash(t),e.onclick=function(){return t&&a.linkService.goToDestination(t),!1},(t||""===t)&&_assertClassBrand(v,this,m).call(this)}},{key:"_bindNamedAction",value:function(e,t){var a=this;e.href=this.linkService.getAnchorUrl(""),e.onclick=function(){return a.linkService.executeNamedAction(t),!1},_assertClassBrand(v,this,m).call(this)}},{key:"_bindAttachment",value:function(e,t){var a=this;e.href=this.linkService.getAnchorUrl(""),e.onclick=function(){var e;return null===(e=a.downloadManager)||void 0===e||e.openOrDownloadData(a.container,t.content,t.filename),!1},_assertClassBrand(v,this,m).call(this)}},{key:"_bindJSAction",value:function(e,t){var a=this;e.href=this.linkService.getAnchorUrl("");for(var r=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]),i=function(){var i=s[n],l=r.get(i);if(!l)return 1;e[l]=function(){var e;return null===(e=a.linkService.eventBus)||void 0===e||e.dispatch("dispatcheventinsandbox",{source:a,detail:{id:t.id,name:i}}),!1}},n=0,s=Object.keys(t.actions);n<s.length;n++)i();e.onclick||(e.onclick=function(){return!1}),_assertClassBrand(v,this,m).call(this)}},{key:"_bindResetFormAction",value:function(e,t){var a=this,i=e.onclick;if(i||(e.href=this.linkService.getAnchorUrl("")),_assertClassBrand(v,this,m).call(this),!this._fieldObjects)return(0,r.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),void(i||(e.onclick=function(){return!1}));e.onclick=function(){null==i||i();var e=t.fields,n=t.refs,s=t.include,l=[];if(0!==e.length||0!==n.length){var o,_=new Set(n),u=_createForOfIteratorHelper(e);try{for(u.s();!(o=u.n()).done;){var h,d=o.value,p=_createForOfIteratorHelper(a._fieldObjects[d]||[]);try{for(p.s();!(h=p.n()).done;){var v=h.value.id;_.add(v)}}catch(e){p.e(e)}finally{p.f()}}}catch(e){u.e(e)}finally{u.f()}for(var f=0,m=Object.values(a._fieldObjects);f<m.length;f++){var b,g=_createForOfIteratorHelper(m[f]);try{for(g.s();!(b=g.n()).done;){var P=b.value;_.has(P.id)===s&&l.push(P)}}catch(e){g.e(e)}finally{g.f()}}}else for(var y=0,A=Object.values(a._fieldObjects);y<A.length;y++){var E=A[y];l.push.apply(l,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(E))}for(var C,M=a.annotationStorage,k=[],O=0,D=l;O<D.length;O++){var F=D[O],S=F.id;switch(k.push(S),F.type){case"text":var T=F.defaultValue||"";M.setValue(S,{value:T});break;case"checkbox":case"radiobutton":var I=F.defaultValue===F.exportValues;M.setValue(S,{value:I});break;case"combobox":case"listbox":var w=F.defaultValue||"";M.setValue(S,{value:w});break;default:continue}var x=document.querySelector('[data-element-id="'.concat(S,'"]'));x&&(c.has(x)?x.dispatchEvent(new Event("resetform")):(0,r.warn)("_bindResetFormAction - element not allowed: ".concat(S)))}return a.enableScripting&&(null===(C=a.linkService.eventBus)||void 0===C||C.dispatch("dispatcheventinsandbox",{source:a,detail:{id:"app",ids:k,name:"ResetForm"}})),!1}}}])}(p);function m(){this.container.setAttribute("data-internal-link","")}function b(e,t){var a=this;e.href=this.linkService.getAnchorUrl(""),e.onclick=function(){return a.linkService.executeSetOCGState(t),!1},_assertClassBrand(v,this,m).call(this)}var g=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:!0}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){this.container.classList.add("textAnnotation");var e=document.createElement("img");return e.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",e.alt="[{{type}} Annotation]",e.dataset.l10nId="text_annotation_type",e.dataset.l10nArgs=JSON.stringify({type:this.data.name}),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(e),this.container}}])}(p),P=function(e){function t(){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,arguments)}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){return this.data.alternativeText&&(this.container.title=this.data.alternativeText),this.container}},{key:"showElementAndHideCanvas",value:function(e){var t;this.data.hasOwnCanvas&&("CANVAS"===(null===(t=e.previousSibling)||void 0===t?void 0:t.nodeName)&&(e.previousSibling.hidden=!0),e.hidden=!1)}},{key:"_getKeyModifier",value:function(e){var t=r.FeatureTest.platform,a=t.isWin,i=t.isMac;return a&&e.ctrlKey||i&&e.metaKey}},{key:"_setEventListener",value:function(e,t,a,r,i){var n=this;a.includes("mouse")?e.addEventListener(a,(function(e){var t;null===(t=n.linkService.eventBus)||void 0===t||t.dispatch("dispatcheventinsandbox",{source:n,detail:{id:n.data.id,name:r,value:i(e),shift:e.shiftKey,modifier:n._getKeyModifier(e)}})})):e.addEventListener(a,(function(e){var s;if("blur"===a){if(!t.focused||!e.relatedTarget)return;t.focused=!1}else if("focus"===a){if(t.focused)return;t.focused=!0}i&&(null===(s=n.linkService.eventBus)||void 0===s||s.dispatch("dispatcheventinsandbox",{source:n,detail:{id:n.data.id,name:r,value:i(e)}}))}))}},{key:"_setEventListeners",value:function(e,t,a,r){var i,n=_createForOfIteratorHelper(a);try{for(n.s();!(i=n.n()).done;){var s,l,o,_=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(i.value,2),c=_[0],u=_[1];("Action"===u||null!==(s=this.data.actions)&&void 0!==s&&s[u])&&("Focus"!==u&&"Blur"!==u||t||(t={focused:!1}),this._setEventListener(e,t,c,u,r),"Focus"!==u||null!==(l=this.data.actions)&&void 0!==l&&l.Blur?"Blur"!==u||null!==(o=this.data.actions)&&void 0!==o&&o.Focus||this._setEventListener(e,t,"focus","Focus",null):this._setEventListener(e,t,"blur","Blur",null))}}catch(e){n.e(e)}finally{n.f()}}},{key:"_setBackgroundColor",value:function(e){var t=this.data.backgroundColor||null;e.style.backgroundColor=null===t?"transparent":r.Util.makeHexColor(t[0],t[1],t[2])}},{key:"_setTextStyle",value:function(e){var t,a=this.data.defaultAppearanceData.fontColor,i=this.data.defaultAppearanceData.fontSize||9,n=e.style,s=function(e){return Math.round(10*e)/10};if(this.data.multiLine){var l=Math.abs(this.data.rect[3]-this.data.rect[1]-2),o=l/(Math.round(l/(r.LINE_FACTOR*i))||1);t=Math.min(i,s(o/r.LINE_FACTOR))}else{var _=Math.abs(this.data.rect[3]-this.data.rect[1]-2);t=Math.min(i,s(_/r.LINE_FACTOR))}n.fontSize="calc(".concat(t,"px * var(--scale-factor))"),n.color=r.Util.makeHexColor(a[0],a[1],a[2]),null!==this.data.textAlignment&&(n.textAlign=["left","center","right"][this.data.textAlignment])}},{key:"_setRequired",value:function(e,t){t?e.setAttribute("required",!0):e.removeAttribute("required"),e.setAttribute("aria-required",t)}}])}(p),y=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:e.renderForms||!e.data.hasAppearance&&!!e.data.fieldValue}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"setPropertyOnSiblings",value:function(e,t,a,r){var i,n=this.annotationStorage,s=_createForOfIteratorHelper(this._getElementsByName(e.name,e.id));try{for(s.s();!(i=s.n()).done;){var l=i.value;l.domElement&&(l.domElement[t]=a),n.setValue(l.id,(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)({},r,a))}}catch(e){s.e(e)}finally{s.f()}}},{key:"render",value:function(){var e=this,t=this.annotationStorage,a=this.data.id;this.container.classList.add("textWidgetAnnotation");var r=null;if(this.renderForms){var i,n=t.getValue(a,{value:this.data.fieldValue}),s=n.value||"",l=t.getValue(a,{charLimit:this.data.maxLen}).charLimit;l&&s.length>l&&(s=s.slice(0,l));var o=n.formattedValue||(null===(i=this.data.textContent)||void 0===i?void 0:i.join("\n"))||null;o&&this.data.comb&&(o=o.replaceAll(/\s+/g,""));var u={userValue:s,formattedValue:o,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?((r=document.createElement("textarea")).textContent=null!=o?o:s,this.data.doNotScroll&&(r.style.overflowY="hidden")):((r=document.createElement("input")).type="text",r.setAttribute("value",null!=o?o:s),this.data.doNotScroll&&(r.style.overflowX="hidden")),this.data.hasOwnCanvas&&(r.hidden=!0),c.add(r),r.setAttribute("data-element-id",a),r.disabled=this.data.readOnly,r.name=this.data.fieldName,r.tabIndex=_,this._setRequired(r,this.data.required),l&&(r.maxLength=l),r.addEventListener("input",(function(i){t.setValue(a,{value:i.target.value}),e.setPropertyOnSiblings(r,"value",i.target.value,"value"),u.formattedValue=null})),r.addEventListener("resetform",(function(t){var a,i=null!==(a=e.data.defaultFieldValue)&&void 0!==a?a:"";r.value=u.userValue=i,u.formattedValue=null}));var h=function(e){var t=u.formattedValue;null!=t&&(e.target.value=t),e.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){var d;r.addEventListener("focus",(function(e){if(!u.focused){var t=e.target;u.userValue&&(t.value=u.userValue),u.lastCommittedValue=t.value,u.commitKey=1,u.focused=!0}})),r.addEventListener("updatefromsandbox",(function(r){e.showElementAndHideCanvas(r.target);var i={value:function(e){var r;u.userValue=null!==(r=e.detail.value)&&void 0!==r?r:"",t.setValue(a,{value:u.userValue.toString()}),e.target.value=u.userValue},formattedValue:function(e){var r=e.detail.formattedValue;u.formattedValue=r,null!=r&&e.target!==document.activeElement&&(e.target.value=r),t.setValue(a,{formattedValue:r})},selRange:function(e){var t;(t=e.target).setSelectionRange.apply(t,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(e.detail.selRange))},charLimit:function(r){var i,n=r.detail.charLimit,s=r.target;if(0!==n){s.setAttribute("maxLength",n);var l=u.userValue;!l||l.length<=n||(l=l.slice(0,n),s.value=u.userValue=l,t.setValue(a,{value:l}),null===(i=e.linkService.eventBus)||void 0===i||i.dispatch("dispatcheventinsandbox",{source:e,detail:{id:a,name:"Keystroke",value:l,willCommit:!0,commitKey:1,selStart:s.selectionStart,selEnd:s.selectionEnd}}))}else s.removeAttribute("maxLength")}};e._dispatchEventFromSandbox(i,r)})),r.addEventListener("keydown",(function(t){var r;u.commitKey=1;var i=-1;if("Escape"===t.key?i=0:"Enter"!==t.key||e.data.multiLine?"Tab"===t.key&&(u.commitKey=3):i=2,-1!==i){var n=t.target.value;u.lastCommittedValue!==n&&(u.lastCommittedValue=n,u.userValue=n,null===(r=e.linkService.eventBus)||void 0===r||r.dispatch("dispatcheventinsandbox",{source:e,detail:{id:a,name:"Keystroke",value:n,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}))}}));var p=h;h=null,r.addEventListener("blur",(function(t){if(u.focused&&t.relatedTarget){u.focused=!1;var r,i=t.target.value;u.userValue=i,u.lastCommittedValue!==i&&(null===(r=e.linkService.eventBus)||void 0===r||r.dispatch("dispatcheventinsandbox",{source:e,detail:{id:a,name:"Keystroke",value:i,willCommit:!0,commitKey:u.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})),p(t)}})),null!==(d=this.data.actions)&&void 0!==d&&d.Keystroke&&r.addEventListener("beforeinput",(function(t){var r;u.lastCommittedValue=null;var i=t.data,n=t.target,s=n.value,l=n.selectionStart,o=n.selectionEnd,_=l,c=o;switch(t.inputType){case"deleteWordBackward":var h=s.substring(0,l).match(/\w*[^\w]*$/);h&&(_-=h[0].length);break;case"deleteWordForward":var d=s.substring(l).match(/^[^\w]*\w*/);d&&(c+=d[0].length);break;case"deleteContentBackward":l===o&&(_-=1);break;case"deleteContentForward":l===o&&(c+=1)}t.preventDefault(),null===(r=e.linkService.eventBus)||void 0===r||r.dispatch("dispatcheventinsandbox",{source:e,detail:{id:a,name:"Keystroke",value:s,change:i||"",willCommit:!1,selStart:_,selEnd:c}})})),this._setEventListeners(r,u,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(function(e){return e.target.value}))}if(h&&r.addEventListener("blur",h),this.data.comb){var v=(this.data.rect[2]-this.data.rect[0])/l;r.classList.add("comb"),r.style.letterSpacing="calc(".concat(v,"px * var(--scale-factor) - 1ch)")}}else(r=document.createElement("div")).textContent=this.data.fieldValue,r.style.verticalAlign="middle",r.style.display="table-cell";return this._setTextStyle(r),this._setBackgroundColor(r),this._setDefaultPropertiesFromJS(r),this.container.append(r),this.container}}])}(P),A=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:!!e.data.hasOwnCanvas}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(P),E=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:e.renderForms}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){var e=this,t=this.annotationStorage,a=this.data,r=a.id,i=t.getValue(r,{value:a.exportValue===a.fieldValue}).value;"string"==typeof i&&(i="Off"!==i,t.setValue(r,{value:i})),this.container.classList.add("buttonWidgetAnnotation","checkBox");var n=document.createElement("input");return c.add(n),n.setAttribute("data-element-id",r),n.disabled=a.readOnly,this._setRequired(n,this.data.required),n.type="checkbox",n.name=a.fieldName,i&&n.setAttribute("checked",!0),n.setAttribute("exportValue",a.exportValue),n.tabIndex=_,n.addEventListener("change",(function(i){var n,s=i.target,l=s.name,o=s.checked,_=_createForOfIteratorHelper(e._getElementsByName(l,r));try{for(_.s();!(n=_.n()).done;){var c=n.value,u=o&&c.exportValue===a.exportValue;c.domElement&&(c.domElement.checked=u),t.setValue(c.id,{value:u})}}catch(e){_.e(e)}finally{_.f()}t.setValue(r,{value:o})})),n.addEventListener("resetform",(function(e){var t=a.defaultFieldValue||"Off";e.target.checked=t===a.exportValue})),this.enableScripting&&this.hasJSActions&&(n.addEventListener("updatefromsandbox",(function(a){var i={value:function(e){e.target.checked="Off"!==e.detail.value,t.setValue(r,{value:e.target.checked})}};e._dispatchEventFromSandbox(i,a)})),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(function(e){return e.target.checked}))),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}])}(P),C=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:e.renderForms}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){var e=this;this.container.classList.add("buttonWidgetAnnotation","radioButton");var t=this.annotationStorage,a=this.data,r=a.id,i=t.getValue(r,{value:a.fieldValue===a.buttonValue}).value;"string"==typeof i&&(i=i!==a.buttonValue,t.setValue(r,{value:i}));var n=document.createElement("input");if(c.add(n),n.setAttribute("data-element-id",r),n.disabled=a.readOnly,this._setRequired(n,this.data.required),n.type="radio",n.name=a.fieldName,i&&n.setAttribute("checked",!0),n.tabIndex=_,n.addEventListener("change",(function(a){var i,n=a.target,s=n.name,l=n.checked,o=_createForOfIteratorHelper(e._getElementsByName(s,r));try{for(o.s();!(i=o.n()).done;){var _=i.value;t.setValue(_.id,{value:!1})}}catch(e){o.e(e)}finally{o.f()}t.setValue(r,{value:l})})),n.addEventListener("resetform",(function(e){var t=a.defaultFieldValue;e.target.checked=null!=t&&t===a.buttonValue})),this.enableScripting&&this.hasJSActions){var s=a.buttonValue;n.addEventListener("updatefromsandbox",(function(a){var i={value:function(a){var i,n=s===a.detail.value,l=_createForOfIteratorHelper(e._getElementsByName(a.target.name));try{for(l.s();!(i=l.n()).done;){var o=i.value,_=n&&o.id===r;o.domElement&&(o.domElement.checked=_),t.setValue(o.id,{value:_})}}catch(e){l.e(e)}finally{l.f()}}};e._dispatchEventFromSandbox(i,a)})),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(function(e){return e.target.checked}))}return this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}])}(P),M=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{ignoreBorder:e.data.hasAppearance}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){var e=this,a=_superPropGet(t,"render",this,3)([]);a.classList.add("buttonWidgetAnnotation","pushButton"),this.data.alternativeText&&(a.title=this.data.alternativeText);var r=a.lastChild;return this.enableScripting&&this.hasJSActions&&r&&(this._setDefaultPropertiesFromJS(r),r.addEventListener("updatefromsandbox",(function(t){e._dispatchEventFromSandbox({},t)}))),a}}])}(f),k=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:e.renderForms}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){var e=this;this.container.classList.add("choiceWidgetAnnotation");var t=this.annotationStorage,a=this.data.id,r=t.getValue(a,{value:this.data.fieldValue}),i=document.createElement("select");c.add(i),i.setAttribute("data-element-id",a),i.disabled=this.data.readOnly,this._setRequired(i,this.data.required),i.name=this.data.fieldName,i.tabIndex=_;var n=this.data.combo&&this.data.options.length>0;this.data.combo||(i.size=this.data.options.length,this.data.multiSelect&&(i.multiple=!0)),i.addEventListener("resetform",(function(t){var a,r=e.data.defaultFieldValue,n=_createForOfIteratorHelper(i.options);try{for(n.s();!(a=n.n()).done;){var s=a.value;s.selected=s.value===r}}catch(e){n.e(e)}finally{n.f()}}));var s,l=_createForOfIteratorHelper(this.data.options);try{for(l.s();!(s=l.n()).done;){var o=s.value,u=document.createElement("option");u.textContent=o.displayValue,u.value=o.exportValue,r.value.includes(o.exportValue)&&(u.setAttribute("selected",!0),n=!1),i.append(u)}}catch(e){l.e(e)}finally{l.f()}var h=null;if(n){var d=document.createElement("option");d.value=" ",d.setAttribute("hidden",!0),d.setAttribute("selected",!0),i.prepend(d),h=function(){d.remove(),i.removeEventListener("input",h),h=null},i.addEventListener("input",h)}var p=function(e){var t=e?"value":"textContent",a=i.options;return i.multiple?Array.prototype.filter.call(a,(function(e){return e.selected})).map((function(e){return e[t]})):-1===a.selectedIndex?null:a[a.selectedIndex][t]},v=p(!1),f=function(e){var t=e.target.options;return Array.prototype.map.call(t,(function(e){return{displayValue:e.textContent,exportValue:e.value}}))};return this.enableScripting&&this.hasJSActions?(i.addEventListener("updatefromsandbox",(function(r){var n={value:function(e){var r;null===(r=h)||void 0===r||r();var n,s=e.detail.value,l=new Set(Array.isArray(s)?s:[s]),o=_createForOfIteratorHelper(i.options);try{for(o.s();!(n=o.n()).done;){var _=n.value;_.selected=l.has(_.value)}}catch(e){o.e(e)}finally{o.f()}t.setValue(a,{value:p(!0)}),v=p(!1)},multipleSelection:function(e){i.multiple=!0},remove:function(e){var r=i.options,n=e.detail.remove;if(r[n].selected=!1,i.remove(n),r.length>0){var s=Array.prototype.findIndex.call(r,(function(e){return e.selected}));-1===s&&(r[0].selected=!0)}t.setValue(a,{value:p(!0),items:f(e)}),v=p(!1)},clear:function(e){for(;0!==i.length;)i.remove(0);t.setValue(a,{value:null,items:[]}),v=p(!1)},insert:function(e){var r=e.detail.insert,n=r.index,s=r.displayValue,l=r.exportValue,o=i.children[n],_=document.createElement("option");_.textContent=s,_.value=l,o?o.before(_):i.append(_),t.setValue(a,{value:p(!0),items:f(e)}),v=p(!1)},items:function(e){for(var r=e.detail.items;0!==i.length;)i.remove(0);var n,s=_createForOfIteratorHelper(r);try{for(s.s();!(n=s.n()).done;){var l=n.value,o=l.displayValue,_=l.exportValue,c=document.createElement("option");c.textContent=o,c.value=_,i.append(c)}}catch(e){s.e(e)}finally{s.f()}i.options.length>0&&(i.options[0].selected=!0),t.setValue(a,{value:p(!0),items:f(e)}),v=p(!1)},indices:function(e){var r,i=new Set(e.detail.indices),n=_createForOfIteratorHelper(e.target.options);try{for(n.s();!(r=n.n()).done;){var s=r.value;s.selected=i.has(s.index)}}catch(e){n.e(e)}finally{n.f()}t.setValue(a,{value:p(!0)}),v=p(!1)},editable:function(e){e.target.disabled=!e.detail.editable}};e._dispatchEventFromSandbox(n,r)})),i.addEventListener("input",(function(r){var i,n=p(!0);t.setValue(a,{value:n}),r.preventDefault(),null===(i=e.linkService.eventBus)||void 0===i||i.dispatch("dispatcheventinsandbox",{source:e,detail:{id:a,name:"Keystroke",value:v,changeEx:n,willCommit:!1,commitKey:1,keyDown:!1}})})),this._setEventListeners(i,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(function(e){return e.target.value}))):i.addEventListener("input",(function(e){t.setValue(a,{value:p(!0)})})),this.data.combo&&this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}])}(P),O=function(e){function t(e){var a;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t);var r=e.data,i=e.elements;return(a=_callSuper(this,t,[e,{isRenderable:p._hasPopupData(r)}])).elements=i,a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){this.container.classList.add("popupAnnotation");var e,t=new z({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),a=[],i=_createForOfIteratorHelper(this.elements);try{for(i.s();!(e=i.n()).done;){var n=e.value;n.popup=t,a.push(n.data.id),n.addHighlightArea()}}catch(e){i.e(e)}finally{i.f()}return this.container.setAttribute("aria-controls",a.map((function(e){return"".concat(r.AnnotationPrefix).concat(e)})).join(",")),this.container}}])}(p),D=new WeakMap,F=new WeakMap,S=new WeakMap,T=new WeakMap,I=new WeakMap,w=new WeakMap,x=new WeakMap,R=new WeakMap,L=new WeakMap,B=new WeakMap,W=new WeakMap,U=new WeakMap,G=new WeakMap,K=new WeakMap,N=new WeakMap,j=new WeakMap,H=new WeakMap,q=new WeakSet,z=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a=t.container,r=t.color,n=t.elements,s=t.titleObj,l=t.modificationDate,o=t.contentsObj,_=t.richText,c=t.parent,u=t.rect,h=t.parentRect,d=t.open;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,q),_classPrivateFieldInitSpec(this,D,null),_classPrivateFieldInitSpec(this,F,_assertClassBrand(q,this,V).bind(this)),_classPrivateFieldInitSpec(this,S,_assertClassBrand(q,this,J).bind(this)),_classPrivateFieldInitSpec(this,T,_assertClassBrand(q,this,Y).bind(this)),_classPrivateFieldInitSpec(this,I,_assertClassBrand(q,this,X).bind(this)),_classPrivateFieldInitSpec(this,w,null),_classPrivateFieldInitSpec(this,x,null),_classPrivateFieldInitSpec(this,R,null),_classPrivateFieldInitSpec(this,L,null),_classPrivateFieldInitSpec(this,B,null),_classPrivateFieldInitSpec(this,W,null),_classPrivateFieldInitSpec(this,U,!1),_classPrivateFieldInitSpec(this,G,null),_classPrivateFieldInitSpec(this,K,null),_classPrivateFieldInitSpec(this,N,null),_classPrivateFieldInitSpec(this,j,null),_classPrivateFieldInitSpec(this,H,!1),_classPrivateFieldSet(x,this,a),_classPrivateFieldSet(j,this,s),_classPrivateFieldSet(R,this,o),_classPrivateFieldSet(N,this,_),_classPrivateFieldSet(B,this,c),_classPrivateFieldSet(w,this,r),_classPrivateFieldSet(K,this,u),_classPrivateFieldSet(W,this,h),_classPrivateFieldSet(L,this,n);var p=i.PDFDateString.toDateObject(l);p&&_classPrivateFieldSet(D,this,c.l10n.get("annotation_date_string",{date:p.toLocaleDateString(),time:p.toLocaleTimeString()})),this.trigger=n.flatMap((function(e){return e.getElementsToTriggerPopup()}));var v,f=_createForOfIteratorHelper(this.trigger);try{for(f.s();!(v=f.n()).done;){var m=v.value;m.addEventListener("click",_classPrivateFieldGet(I,this)),m.addEventListener("mouseenter",_classPrivateFieldGet(T,this)),m.addEventListener("mouseleave",_classPrivateFieldGet(S,this)),m.classList.add("popupTriggerArea")}}catch(e){f.e(e)}finally{f.f()}var b,g=_createForOfIteratorHelper(n);try{for(g.s();!(b=g.n()).done;){var P;null===(P=b.value.container)||void 0===P||P.addEventListener("keydown",_classPrivateFieldGet(F,this))}}catch(e){g.e(e)}finally{g.f()}_classPrivateFieldGet(x,this).hidden=!0,d&&_assertClassBrand(q,this,X).call(this)}),[{key:"render",value:function(){if(!_classPrivateFieldGet(G,this)){var e=_classPrivateFieldGet(B,this),t=e.page.view,a=e.viewport.rawDims,i=a.pageWidth,n=a.pageHeight,s=a.pageX,l=a.pageY,_=_classPrivateFieldSet(G,this,document.createElement("div"));if(_.className="popup",_classPrivateFieldGet(w,this)){var c,u,h=_.style.outlineColor=(c=r.Util).makeHexColor.apply(c,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(_classPrivateFieldGet(w,this)));CSS.supports("background-color","color-mix(in srgb, red 30%, white)")?_.style.backgroundColor="color-mix(in srgb, ".concat(h," 30%, white)"):_.style.backgroundColor=(u=r.Util).makeHexColor.apply(u,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(_classPrivateFieldGet(w,this).map((function(e){return Math.floor(.7*(255-e)+e)}))))}var d=document.createElement("span");d.className="header";var p=document.createElement("h1");d.append(p);var v=_classPrivateFieldGet(j,this);if(p.dir=v.dir,p.textContent=v.str,_.append(d),_classPrivateFieldGet(D,this)){var f=document.createElement("span");f.classList.add("popupDate"),_classPrivateFieldGet(D,this).then((function(e){f.textContent=e})),d.append(f)}var m=_classPrivateFieldGet(R,this),b=_classPrivateFieldGet(N,this);if(null==b||!b.str||null!=m&&m.str&&m.str!==b.str){var g=this._formatContents(m);_.append(g)}else o.XfaLayer.render({xfaHtml:b.html,intent:"richText",div:_}),_.lastChild.classList.add("richText","popupContent");var P,y=!!_classPrivateFieldGet(W,this),A=_classPrivateFieldGet(y?W:K,this),E=_createForOfIteratorHelper(_classPrivateFieldGet(L,this));try{for(E.s();!(P=E.n()).done;){var C=P.value;if(!A||null!==r.Util.intersect(C.data.rect,A)){A=C.data.rect,y=!0;break}}}catch(e){E.e(e)}finally{E.f()}var M=r.Util.normalizeRect([A[0],t[3]-A[1]+t[1],A[2],t[3]-A[3]+t[1]]),k=y?A[2]-A[0]+5:0,O=M[0]+k,F=M[1],S=_classPrivateFieldGet(x,this).style;S.left="".concat(100*(O-s)/i,"%"),S.top="".concat(100*(F-l)/n,"%"),_classPrivateFieldGet(x,this).append(_)}}},{key:"_formatContents",value:function(e){var t=e.str,a=e.dir,r=document.createElement("p");r.classList.add("popupContent"),r.dir=a;for(var i=t.split(/(?:\r\n?|\n)/),n=0,s=i.length;n<s;++n){var l=i[n];r.append(document.createTextNode(l)),n<s-1&&r.append(document.createElement("br"))}return r}},{key:"forceHide",value:function(){_classPrivateFieldSet(H,this,this.isVisible),_classPrivateFieldGet(H,this)&&(_classPrivateFieldGet(x,this).hidden=!0)}},{key:"maybeShow",value:function(){_classPrivateFieldGet(H,this)&&(_classPrivateFieldSet(H,this,!1),_classPrivateFieldGet(x,this).hidden=!1)}},{key:"isVisible",get:function(){return!1===_classPrivateFieldGet(x,this).hidden}}])}();function V(e){e.altKey||e.shiftKey||e.ctrlKey||e.metaKey||("Enter"===e.key||"Escape"===e.key&&_classPrivateFieldGet(U,this))&&_assertClassBrand(q,this,X).call(this)}function X(){_classPrivateFieldSet(U,this,!_classPrivateFieldGet(U,this)),_classPrivateFieldGet(U,this)?(_assertClassBrand(q,this,Y).call(this),_classPrivateFieldGet(x,this).addEventListener("click",_classPrivateFieldGet(I,this)),_classPrivateFieldGet(x,this).addEventListener("keydown",_classPrivateFieldGet(F,this))):(_assertClassBrand(q,this,J).call(this),_classPrivateFieldGet(x,this).removeEventListener("click",_classPrivateFieldGet(I,this)),_classPrivateFieldGet(x,this).removeEventListener("keydown",_classPrivateFieldGet(F,this)))}function Y(){_classPrivateFieldGet(G,this)||this.render(),this.isVisible?_classPrivateFieldGet(U,this)&&_classPrivateFieldGet(x,this).classList.add("focused"):(_classPrivateFieldGet(x,this).hidden=!1,_classPrivateFieldGet(x,this).style.zIndex=parseInt(_classPrivateFieldGet(x,this).style.zIndex)+1e3)}function J(){_classPrivateFieldGet(x,this).classList.remove("focused"),!_classPrivateFieldGet(U,this)&&this.isVisible&&(_classPrivateFieldGet(x,this).hidden=!0,_classPrivateFieldGet(x,this).style.zIndex=parseInt(_classPrivateFieldGet(x,this).style.zIndex)-1e3)}var Q=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(a=_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0}])).textContent=e.data.textContent,a.textPosition=e.data.textPosition,a.annotationEditorType=r.AnnotationEditorType.FREETEXT,a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){var e=document.createElement("div");e.classList.add("annotationTextContent"),e.setAttribute("role","comment");var t,a=_createForOfIteratorHelper(this.textContent);try{for(a.s();!(t=a.n()).done;){var r=t.value,i=document.createElement("span");i.textContent=r,e.append(i)}}catch(e){a.e(e)}finally{a.f()}this.container.append(e)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}])}(p);t.FreeTextAnnotationElement=Q;var Z=new WeakMap,$=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateFieldInitSpec(a=_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0}]),Z,null),a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){this.container.classList.add("lineAnnotation");var e=this.data,t=u(e.rect),a=t.width,r=t.height,i=this.svgFactory.create(a,r,!0),n=_classPrivateFieldSet(Z,this,this.svgFactory.createElement("svg:line"));return n.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),n.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),n.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),n.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),n.setAttribute("stroke-width",e.borderStyle.width||1),n.setAttribute("stroke","transparent"),n.setAttribute("fill","transparent"),i.append(n),this.container.append(i),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}},{key:"getElementsToTriggerPopup",value:function(){return _classPrivateFieldGet(Z,this)}},{key:"addHighlightArea",value:function(){this.container.classList.add("highlightArea")}}])}(p),ee=new WeakMap,te=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateFieldInitSpec(a=_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0}]),ee,null),a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){this.container.classList.add("squareAnnotation");var e=this.data,t=u(e.rect),a=t.width,r=t.height,i=this.svgFactory.create(a,r,!0),n=e.borderStyle.width,s=_classPrivateFieldSet(ee,this,this.svgFactory.createElement("svg:rect"));return s.setAttribute("x",n/2),s.setAttribute("y",n/2),s.setAttribute("width",a-n),s.setAttribute("height",r-n),s.setAttribute("stroke-width",n||1),s.setAttribute("stroke","transparent"),s.setAttribute("fill","transparent"),i.append(s),this.container.append(i),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}},{key:"getElementsToTriggerPopup",value:function(){return _classPrivateFieldGet(ee,this)}},{key:"addHighlightArea",value:function(){this.container.classList.add("highlightArea")}}])}(p),ae=new WeakMap,re=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateFieldInitSpec(a=_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0}]),ae,null),a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){this.container.classList.add("circleAnnotation");var e=this.data,t=u(e.rect),a=t.width,r=t.height,i=this.svgFactory.create(a,r,!0),n=e.borderStyle.width,s=_classPrivateFieldSet(ae,this,this.svgFactory.createElement("svg:ellipse"));return s.setAttribute("cx",a/2),s.setAttribute("cy",r/2),s.setAttribute("rx",a/2-n/2),s.setAttribute("ry",r/2-n/2),s.setAttribute("stroke-width",n||1),s.setAttribute("stroke","transparent"),s.setAttribute("fill","transparent"),i.append(s),this.container.append(i),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}},{key:"getElementsToTriggerPopup",value:function(){return _classPrivateFieldGet(ae,this)}},{key:"addHighlightArea",value:function(){this.container.classList.add("highlightArea")}}])}(p),ie=new WeakMap,ne=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateFieldInitSpec(a=_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0}]),ie,null),a.containerClassName="polylineAnnotation",a.svgElementName="svg:polyline",a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){this.container.classList.add(this.containerClassName);var e,t=this.data,a=u(t.rect),r=a.width,i=a.height,n=this.svgFactory.create(r,i,!0),s=[],l=_createForOfIteratorHelper(t.vertices);try{for(l.s();!(e=l.n()).done;){var o=e.value,_=o.x-t.rect[0],c=t.rect[3]-o.y;s.push(_+","+c)}}catch(e){l.e(e)}finally{l.f()}s=s.join(" ");var h=_classPrivateFieldSet(ie,this,this.svgFactory.createElement(this.svgElementName));return h.setAttribute("points",s),h.setAttribute("stroke-width",t.borderStyle.width||1),h.setAttribute("stroke","transparent"),h.setAttribute("fill","transparent"),n.append(h),this.container.append(n),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}},{key:"getElementsToTriggerPopup",value:function(){return _classPrivateFieldGet(ie,this)}},{key:"addHighlightArea",value:function(){this.container.classList.add("highlightArea")}}])}(p),se=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),(a=_callSuper(this,t,[e])).containerClassName="polygonAnnotation",a.svgElementName="svg:polygon",a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t)}(ne),le=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}])}(p),oe=new WeakMap,_e=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateFieldInitSpec(a=_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0}]),oe,[]),a.containerClassName="inkAnnotation",a.svgElementName="svg:polyline",a.annotationEditorType=r.AnnotationEditorType.INK,a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){this.container.classList.add(this.containerClassName);var e,t=this.data,a=u(t.rect),r=a.width,i=a.height,n=this.svgFactory.create(r,i,!0),s=_createForOfIteratorHelper(t.inkLists);try{for(s.s();!(e=s.n()).done;){var l,o=e.value,_=[],c=_createForOfIteratorHelper(o);try{for(c.s();!(l=c.n()).done;){var h=l.value,d=h.x-t.rect[0],p=t.rect[3]-h.y;_.push("".concat(d,",").concat(p))}}catch(e){c.e(e)}finally{c.f()}_=_.join(" ");var v=this.svgFactory.createElement(this.svgElementName);_classPrivateFieldGet(oe,this).push(v),v.setAttribute("points",_),v.setAttribute("stroke-width",t.borderStyle.width||1),v.setAttribute("stroke","transparent"),v.setAttribute("fill","transparent"),!t.popupRef&&this.hasPopupData&&this._createPopup(),n.append(v)}}catch(e){s.e(e)}finally{s.f()}return this.container.append(n),this.container}},{key:"getElementsToTriggerPopup",value:function(){return _classPrivateFieldGet(oe,this)}},{key:"addHighlightArea",value:function(){this.container.classList.add("highlightArea")}}])}(p);t.InkAnnotationElement=_e;var ce=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this.container}}])}(p),ue=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}])}(p),he=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}])}(p),de=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}])}(p),pe=function(e){function t(e){return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_callSuper(this,t,[e,{isRenderable:!0,ignoreBorder:!0}])}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){return this.container.classList.add("stampAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}])}(p);t.StampAnnotationElement=pe;var ve=new WeakMap,fe=new WeakSet,me=function(e){function t(e){var a,r;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateMethodInitSpec(r=_callSuper(this,t,[e,{isRenderable:!0}]),fe),_classPrivateFieldInitSpec(r,ve,null);var n=r.data.file,s=n.filename,l=n.content;return r.filename=(0,i.getFilenameFromUrl)(s,!0),r.content=l,null===(a=r.linkService.eventBus)||void 0===a||a.dispatch("fileattachmentannotation",{source:r,filename:s,content:l}),r}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"render",value:function(){var e=this;this.container.classList.add("fileAttachmentAnnotation");var t,a=this.container,i=this.data;i.hasAppearance||0===i.fillAlpha?t=document.createElement("div"):((t=document.createElement("img")).src="".concat(this.imageResourcesPath,"annotation-").concat(/paperclip/i.test(i.name)?"paperclip":"pushpin",".svg"),i.fillAlpha&&i.fillAlpha<1&&(t.style="filter: opacity(".concat(Math.round(100*i.fillAlpha),"%);"))),t.addEventListener("dblclick",_assertClassBrand(fe,this,be).bind(this)),_classPrivateFieldSet(ve,this,t);var n=r.FeatureTest.platform.isMac;return a.addEventListener("keydown",(function(t){"Enter"===t.key&&(n?t.metaKey:t.ctrlKey)&&_assertClassBrand(fe,e,be).call(e)})),!i.popupRef&&this.hasPopupData?this._createPopup():t.classList.add("popupTriggerArea"),a.append(t),a}},{key:"getElementsToTriggerPopup",value:function(){return _classPrivateFieldGet(ve,this)}},{key:"addHighlightArea",value:function(){this.container.classList.add("highlightArea")}}])}(p);function be(){var e;null===(e=this.downloadManager)||void 0===e||e.openOrDownloadData(this.container,this.content,this.filename)}var ge=new WeakMap,Pe=new WeakMap,ye=new WeakMap,Ae=new WeakSet,Ee=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(t){var a=t.div,r=t.accessibilityManager,i=t.annotationCanvasMap,n=t.l10n,s=t.page,o=t.viewport;(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e),_classPrivateMethodInitSpec(this,Ae),_classPrivateFieldInitSpec(this,ge,null),_classPrivateFieldInitSpec(this,Pe,null),_classPrivateFieldInitSpec(this,ye,new Map),this.div=a,_classPrivateFieldSet(ge,this,r),_classPrivateFieldSet(Pe,this,i),this.l10n=n,this.page=s,this.viewport=o,this.zIndex=0,this.l10n||(this.l10n=l.NullL10n)}),[{key:"render",value:(e=(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(t){var a,s,l,o,_,c,d,p,v,f,m,b,g,P,y,A;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:a=t.annotations,s=this.div,(0,i.setLayerDimensions)(s,this.viewport),l=new Map,o={data:null,layer:s,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new i.DOMSVGFactory,annotationStorage:t.annotationStorage||new n.AnnotationStorage,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null},_=_createForOfIteratorHelper(a),e.prev=1,_.s();case 2:if((c=_.n()).done){e.next=10;break}if(!(d=c.value).noHTML){e.next=3;break}return e.abrupt("continue",9);case 3:if(p=d.annotationType===r.AnnotationType.POPUP){e.next=5;break}if(v=u(d.rect),f=v.width,m=v.height,!(f<=0||m<=0)){e.next=4;break}return e.abrupt("continue",9);case 4:e.next=7;break;case 5:if(b=l.get(d.id)){e.next=6;break}return e.abrupt("continue",9);case 6:o.elements=b;case 7:if(o.data=d,(g=h.create(o)).isRenderable){e.next=8;break}return e.abrupt("continue",9);case 8:!p&&d.popupRef&&((P=l.get(d.popupRef))?P.push(g):l.set(d.popupRef,[g])),g.annotationEditorType>0&&_classPrivateFieldGet(ye,this).set(g.data.id,g),y=g.render(),d.hidden&&(y.style.visibility="hidden"),_assertClassBrand(Ae,this,Ce).call(this,y,d.id);case 9:e.next=2;break;case 10:e.next=12;break;case 11:e.prev=11,A=e.catch(1),_.e(A);case 12:return e.prev=12,_.f(),e.finish(12);case 13:return _assertClassBrand(Ae,this,Me).call(this),e.next=14,this.l10n.translate(s);case 14:case"end":return e.stop()}}),e,this,[[1,11,12,13]])}))),function(t){return e.apply(this,arguments)})},{key:"update",value:function(e){var t=e.viewport,a=this.div;this.viewport=t,(0,i.setLayerDimensions)(a,{rotation:t.rotation}),_assertClassBrand(Ae,this,Me).call(this),a.hidden=!1}},{key:"getEditableAnnotations",value:function(){return Array.from(_classPrivateFieldGet(ye,this).values())}},{key:"getEditableAnnotation",value:function(e){return _classPrivateFieldGet(ye,this).get(e)}}]);var e}();function Ce(e,t){var a,i=e.firstChild||e;i.id="".concat(r.AnnotationPrefix).concat(t),this.div.append(e),null===(a=_classPrivateFieldGet(ge,this))||void 0===a||a.moveElementInDOM(this.div,e,i,!1)}function Me(){if(_classPrivateFieldGet(Pe,this)){var e,t=this.div,a=_createForOfIteratorHelper(_classPrivateFieldGet(Pe,this));try{for(a.s();!(e=a.n()).done;){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e.value,2),i=r[0],n=r[1],s=t.querySelector('[data-annotation-id="'.concat(i,'"]'));if(s){var l=s.firstChild;l?"CANVAS"===l.nodeName?l.replaceWith(n):l.before(n):s.append(n)}}}catch(e){a.e(e)}finally{a.f()}_classPrivateFieldGet(Pe,this).clear()}}t.AnnotationLayer=Ee},function(e,t){function a(e){return Math.floor(255*Math.max(0,Math.min(1,e))).toString(16).padStart(2,"0")}function r(e){return Math.max(0,Math.min(255,255*e))}Object.defineProperty(t,"__esModule",{value:!0}),t.ColorConverters=void 0;var i=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e)}),null,[{key:"CMYK_G",value:function(e){var t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,4),a=t[0],r=t[1],i=t[2],n=t[3];return["G",1-Math.min(1,.3*a+.59*i+.11*r+n)]}},{key:"G_CMYK",value:function(e){return["CMYK",0,0,0,1-(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,1)[0]]}},{key:"G_RGB",value:function(e){var t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,1)[0];return["RGB",t,t,t]}},{key:"G_rgb",value:function(e){var t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,1)[0];return[t=r(t),t,t]}},{key:"G_HTML",value:function(e){var t=a((0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,1)[0]);return"#".concat(t).concat(t).concat(t)}},{key:"RGB_G",value:function(e){var t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,3);return["G",.3*t[0]+.59*t[1]+.11*t[2]]}},{key:"RGB_rgb",value:function(e){return e.map(r)}},{key:"RGB_HTML",value:function(e){return"#".concat(e.map(a).join(""))}},{key:"T_HTML",value:function(){return"#00000000"}},{key:"T_rgb",value:function(){return[null]}},{key:"CMYK_RGB",value:function(e){var t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,4),a=t[0],r=t[1],i=t[2],n=t[3];return["RGB",1-Math.min(1,a+n),1-Math.min(1,i+n),1-Math.min(1,r+n)]}},{key:"CMYK_rgb",value:function(e){var t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,4),a=t[0],i=t[1],n=t[2],s=t[3];return[r(1-Math.min(1,a+s)),r(1-Math.min(1,n+s)),r(1-Math.min(1,i+s))]}},{key:"CMYK_HTML",value:function(e){var t=this.CMYK_RGB(e).slice(1);return this.RGB_HTML(t)}},{key:"RGB_CMYK",value:function(e){var t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e,3),a=1-t[0],r=1-t[1],i=1-t[2];return["CMYK",a,r,i,Math.min(a,r,i)]}}])}();t.ColorConverters=i},function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.NullL10n=void 0,t.getL10nFallback=r;var a={of_pages:"of {{pagesCount}}",page_of_pages:"({{pageNumber}} of {{pagesCount}})",document_properties_kb:"{{size_kb}} KB ({{size_b}} bytes)",document_properties_mb:"{{size_mb}} MB ({{size_b}} bytes)",document_properties_date_string:"{{date}}, {{time}}",document_properties_page_size_unit_inches:"in",document_properties_page_size_unit_millimeters:"mm",document_properties_page_size_orientation_portrait:"portrait",document_properties_page_size_orientation_landscape:"landscape",document_properties_page_size_name_a3:"A3",document_properties_page_size_name_a4:"A4",document_properties_page_size_name_letter:"Letter",document_properties_page_size_name_legal:"Legal",document_properties_page_size_dimension_string:"{{width}} × {{height}} {{unit}} ({{orientation}})",document_properties_page_size_dimension_name_string:"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})",document_properties_linearized_yes:"Yes",document_properties_linearized_no:"No",additional_layers:"Additional Layers",page_landmark:"Page {{page}}",thumb_page_title:"Page {{page}}",thumb_page_canvas:"Thumbnail of Page {{page}}",find_reached_top:"Reached top of document, continued from bottom",find_reached_bottom:"Reached end of document, continued from top","find_match_count[one]":"{{current}} of {{total}} match","find_match_count[other]":"{{current}} of {{total}} matches","find_match_count_limit[one]":"More than {{limit}} match","find_match_count_limit[other]":"More than {{limit}} matches",find_not_found:"Phrase not found",page_scale_width:"Page Width",page_scale_fit:"Page Fit",page_scale_auto:"Automatic Zoom",page_scale_actual:"Actual Size",page_scale_percent:"{{scale}}%",loading_error:"An error occurred while loading the PDF.",invalid_file_error:"Invalid or corrupted PDF file.",missing_file_error:"Missing PDF file.",unexpected_response_error:"Unexpected server response.",rendering_error:"An error occurred while rendering the page.",annotation_date_string:"{{date}}, {{time}}",printing_not_supported:"Warning: Printing is not fully supported by this browser.",printing_not_ready:"Warning: The PDF is not fully loaded for printing.",web_fonts_disabled:"Web fonts are disabled: unable to use embedded PDF fonts.",free_text2_default_content:"Start typing…",editor_free_text2_aria_label:"Text Editor",editor_ink2_aria_label:"Draw Editor",editor_ink_canvas_aria_label:"User-created image",editor_alt_text_button_label:"Alt text",editor_alt_text_edit_button_label:"Edit alt text",editor_alt_text_decorative_tooltip:"Marked as decorative"};function r(e,t){switch(e){case"find_match_count":e="find_match_count[".concat(1===t.total?"one":"other","]");break;case"find_match_count_limit":e="find_match_count_limit[".concat(1===t.limit?"one":"other","]")}return a[e]||""}function i(e,t){return t?e.replaceAll(/\{\{\s*(\w+)\s*\}\}/g,(function(e,a){return a in t?t[a]:"{{"+a+"}}"})):e}a.print_progress_percent="{{progress}}%";var n={getLanguage:function(){return(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return","en-us");case 1:case"end":return e.stop()}}),e)})))()},getDirection:function(){return(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return","ltr");case 1:case"end":return e.stop()}}),e)})))()},get:function(e){var t=arguments;return(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function a(){var n,s;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return n=t.length>1&&void 0!==t[1]?t[1]:null,s=t.length>2&&void 0!==t[2]?t[2]:r(e,n),a.abrupt("return",i(s,n));case 1:case"end":return a.stop()}}),a)})))()},translate:function(e){return(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function e(){return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})))()}};t.NullL10n=n},function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.XfaLayer=void 0;var r=a(25),i=function(){return(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)((function e(){(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,e)}),null,[{key:"setupStorage",value:function(e,t,a,r,i){var n=r.getValue(t,{value:null});switch(a.name){case"textarea":if(null!==n.value&&(e.textContent=n.value),"print"===i)break;e.addEventListener("input",(function(e){r.setValue(t,{value:e.target.value})}));break;case"input":if("radio"===a.attributes.type||"checkbox"===a.attributes.type){if(n.value===a.attributes.xfaOn?e.setAttribute("checked",!0):n.value===a.attributes.xfaOff&&e.removeAttribute("checked"),"print"===i)break;e.addEventListener("change",(function(e){r.setValue(t,{value:e.target.checked?e.target.getAttribute("xfaOn"):e.target.getAttribute("xfaOff")})}))}else{if(null!==n.value&&e.setAttribute("value",n.value),"print"===i)break;e.addEventListener("input",(function(e){r.setValue(t,{value:e.target.value})}))}break;case"select":if(null!==n.value){e.setAttribute("value",n.value);var s,l=_createForOfIteratorHelper(a.children);try{for(l.s();!(s=l.n()).done;){var o=s.value;o.attributes.value===n.value?o.attributes.selected=!0:o.attributes.hasOwnProperty("selected")&&delete o.attributes.selected}}catch(e){l.e(e)}finally{l.f()}}e.addEventListener("input",(function(e){var a=e.target.options,i=-1===a.selectedIndex?"":a[a.selectedIndex].value;r.setValue(t,{value:i})}))}}},{key:"setAttributes",value:function(e){var t=e.html,a=e.element,r=e.storage,i=void 0===r?null:r,n=e.intent,s=e.linkService,l=a.attributes,o=t instanceof HTMLAnchorElement;"radio"===l.type&&(l.name="".concat(l.name,"-").concat(n));for(var _=0,c=Object.entries(l);_<c.length;_++){var u=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(c[_],2),h=u[0],d=u[1];if(null!=d)switch(h){case"class":d.length&&t.setAttribute(h,d.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",d);break;case"style":Object.assign(t.style,d);break;case"textContent":t.textContent=d;break;default:(!o||"href"!==h&&"newWindow"!==h)&&t.setAttribute(h,d)}}o&&s.addLinkAttributes(t,l.href,l.newWindow),i&&l.dataId&&this.setupStorage(t,l.dataId,a,i)}},{key:"render",value:function(e){var t=e.annotationStorage,a=e.linkService,i=e.xfaHtml,n=e.intent||"display",s=document.createElement(i.name);i.attributes&&this.setAttributes({html:s,element:i,intent:n,linkService:a});var l=[[i,-1,s]],o=e.div;if(o.append(s),e.viewport){var _="matrix(".concat(e.viewport.transform.join(","),")");o.style.transform=_}"richText"!==n&&o.setAttribute("class","xfaLayer xfaFont");for(var c=[];l.length>0;){var u,h=l.at(-1),d=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(h,3),p=d[0],v=d[1],f=d[2];if(v+1!==p.children.length){var m=p.children[++l.at(-1)[1]];if(null!==m){var b=m.name;if("#text"!==b){var g=null!=m&&null!==(u=m.attributes)&&void 0!==u&&u.xmlns?document.createElementNS(m.attributes.xmlns,b):document.createElement(b);if(f.append(g),m.attributes&&this.setAttributes({html:g,element:m,storage:t,intent:n,linkService:a}),m.children&&m.children.length>0)l.push([m,-1,g]);else if(m.value){var P=document.createTextNode(m.value);r.XfaText.shouldBuildText(b)&&c.push(P),g.append(P)}}else{var y=document.createTextNode(m.value);c.push(y),f.append(y)}}}else l.pop()}var A,E=_createForOfIteratorHelper(o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"));try{for(E.s();!(A=E.n()).done;)A.value.setAttribute("readOnly",!0)}catch(e){E.e(e)}finally{E.f()}return{textDivs:c}}},{key:"update",value:function(e){var t="matrix(".concat(e.viewport.transform.join(","),")");e.div.style.transform=t,e.div.hidden=!1}}])}();t.XfaLayer=i},function(e,t,a){var r;Object.defineProperty(t,"__esModule",{value:!0}),t.InkEditor=void 0;var i=a(1),n=a(4),s=a(29),l=a(6),o=a(5),_=new WeakMap,c=new WeakMap,u=new WeakMap,h=new WeakMap,d=new WeakMap,p=new WeakMap,v=new WeakMap,f=new WeakMap,m=new WeakMap,b=new WeakMap,g=new WeakMap,P=new WeakMap,y=new WeakMap,A=new WeakMap,E=new WeakSet,C=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateMethodInitSpec(a=_callSuper(this,t,[_objectSpread(_objectSpread({},e),{},{name:"inkEditor"})]),E),_classPrivateFieldInitSpec(a,_,0),_classPrivateFieldInitSpec(a,c,0),_classPrivateFieldInitSpec(a,u,a.canvasPointermove.bind(a)),_classPrivateFieldInitSpec(a,h,a.canvasPointerleave.bind(a)),_classPrivateFieldInitSpec(a,d,a.canvasPointerup.bind(a)),_classPrivateFieldInitSpec(a,p,a.canvasPointerdown.bind(a)),_classPrivateFieldInitSpec(a,v,new Path2D),_classPrivateFieldInitSpec(a,f,!1),_classPrivateFieldInitSpec(a,m,!1),_classPrivateFieldInitSpec(a,b,!1),_classPrivateFieldInitSpec(a,g,null),_classPrivateFieldInitSpec(a,P,0),_classPrivateFieldInitSpec(a,y,0),_classPrivateFieldInitSpec(a,A,null),a.color=e.color||null,a.thickness=e.thickness||null,a.opacity=e.opacity||null,a.paths=[],a.bezierPath2D=[],a.allRawPaths=[],a.currentPath=[],a.scaleFactor=1,a.translationX=a.translationY=0,a.x=0,a.y=0,a._willKeepAspectRatio=!0,a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"updateParams",value:function(e,t){switch(e){case i.AnnotationEditorParamsType.INK_THICKNESS:_assertClassBrand(E,this,M).call(this,t);break;case i.AnnotationEditorParamsType.INK_COLOR:_assertClassBrand(E,this,k).call(this,t);break;case i.AnnotationEditorParamsType.INK_OPACITY:_assertClassBrand(E,this,O).call(this,t)}}},{key:"propertiesToUpdate",get:function(){var e;return[[i.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||t._defaultThickness],[i.AnnotationEditorParamsType.INK_COLOR,this.color||t._defaultColor||n.AnnotationEditor._defaultLineColor],[i.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(null!==(e=this.opacity)&&void 0!==e?e:t._defaultOpacity))]]}},{key:"rebuild",value:function(){this.parent&&(_superPropGet(t,"rebuild",this,3)([]),null!==this.div&&(this.canvas||(_assertClassBrand(E,this,U).call(this),_assertClassBrand(E,this,G).call(this)),this.isAttachedToDOM||(this.parent.add(this),_assertClassBrand(E,this,K).call(this)),_assertClassBrand(E,this,J).call(this)))}},{key:"remove",value:function(){null!==this.canvas&&(this.isEmpty()||this.commit(),this.canvas.width=this.canvas.height=0,this.canvas.remove(),this.canvas=null,_classPrivateFieldGet(g,this).disconnect(),_classPrivateFieldSet(g,this,null),_superPropGet(t,"remove",this,3)([]))}},{key:"setParent",value:function(e){!this.parent&&e?this._uiManager.removeShouldRescale(this):this.parent&&null===e&&this._uiManager.addShouldRescale(this),_superPropGet(t,"setParent",this,3)([e])}},{key:"onScaleChanging",value:function(){var e=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),t=e[0],a=e[1],r=this.width*t,i=this.height*a;this.setDimensions(r,i)}},{key:"enableEditMode",value:function(){_classPrivateFieldGet(f,this)||null===this.canvas||(_superPropGet(t,"enableEditMode",this,3)([]),this._isDraggable=!1,this.canvas.addEventListener("pointerdown",_classPrivateFieldGet(p,this)))}},{key:"disableEditMode",value:function(){this.isInEditMode()&&null!==this.canvas&&(_superPropGet(t,"disableEditMode",this,3)([]),this._isDraggable=!this.isEmpty(),this.div.classList.remove("editing"),this.canvas.removeEventListener("pointerdown",_classPrivateFieldGet(p,this)))}},{key:"onceAdded",value:function(){this._isDraggable=!this.isEmpty()}},{key:"isEmpty",value:function(){return 0===this.paths.length||1===this.paths.length&&0===this.paths[0].length}},{key:"commit",value:function(){_classPrivateFieldGet(f,this)||(_superPropGet(t,"commit",this,3)([]),this.isEditing=!1,this.disableEditMode(),this.setInForeground(),_classPrivateFieldSet(f,this,!0),this.div.classList.add("disabled"),_assertClassBrand(E,this,J).call(this,!0),this.makeResizable(),this.parent.addInkEditorIfNeeded(!0),this.moveInDOM(),this.div.focus({preventScroll:!0}))}},{key:"focusin",value:function(e){this._focusEventsAllowed&&(_superPropGet(t,"focusin",this,3)([e]),this.enableEditMode())}},{key:"canvasPointerdown",value:function(e){0===e.button&&this.isInEditMode()&&!_classPrivateFieldGet(f,this)&&(this.setInForeground(),e.preventDefault(),"mouse"!==e.type&&this.div.focus(),_assertClassBrand(E,this,S).call(this,e.offsetX,e.offsetY))}},{key:"canvasPointermove",value:function(e){e.preventDefault(),_assertClassBrand(E,this,T).call(this,e.offsetX,e.offsetY)}},{key:"canvasPointerup",value:function(e){e.preventDefault(),_assertClassBrand(E,this,W).call(this,e)}},{key:"canvasPointerleave",value:function(e){_assertClassBrand(E,this,W).call(this,e)}},{key:"isResizable",get:function(){return!this.isEmpty()&&_classPrivateFieldGet(f,this)}},{key:"render",value:function(){var e,a,r=this;if(this.div)return this.div;this.width&&(e=this.x,a=this.y),_superPropGet(t,"render",this,3)([]),n.AnnotationEditor._l10nPromise.get("editor_ink2_aria_label").then((function(e){var t;return null===(t=r.div)||void 0===t?void 0:t.setAttribute("aria-label",e)}));var i=_assertClassBrand(E,this,D).call(this),s=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(i,4),l=s[0],o=s[1],_=s[2],c=s[3];if(this.setAt(l,o,0,0),this.setDims(_,c),_assertClassBrand(E,this,U).call(this),this.width){var u=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),h=u[0],d=u[1];this.setAspectRatio(this.width*h,this.height*d),this.setAt(e*h,a*d,this.width*h,this.height*d),_classPrivateFieldSet(b,this,!0),_assertClassBrand(E,this,K).call(this),this.setDims(this.width*h,this.height*d),_assertClassBrand(E,this,B).call(this),this.div.classList.add("disabled")}else this.div.classList.add("editing"),this.enableEditMode();return _assertClassBrand(E,this,G).call(this),this.div}},{key:"setDimensions",value:function(e,t){var a=Math.round(e),r=Math.round(t);if(_classPrivateFieldGet(P,this)!==a||_classPrivateFieldGet(y,this)!==r){_classPrivateFieldSet(P,this,a),_classPrivateFieldSet(y,this,r),this.canvas.style.visibility="hidden";var i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),n=i[0],s=i[1];this.width=e/n,this.height=t/s,this.fixAndSetPosition(),_classPrivateFieldGet(f,this)&&_assertClassBrand(E,this,N).call(this,e,t),_assertClassBrand(E,this,K).call(this),_assertClassBrand(E,this,B).call(this),this.canvas.style.visibility="visible",this.fixDims()}}},{key:"serialize",value:function(){if(this.isEmpty())return null;var e=this.getRect(0,0),t=n.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:i.AnnotationEditorType.INK,color:t,thickness:this.thickness,opacity:this.opacity,paths:_assertClassBrand(E,this,V).call(this,this.scaleFactor/this.parentScale,this.translationX,this.translationY,e),pageIndex:this.pageIndex,rect:e,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}}],[{key:"initialize",value:function(e){n.AnnotationEditor.initialize(e,{strings:["editor_ink_canvas_aria_label","editor_ink2_aria_label"]})}},{key:"updateDefaultParams",value:function(e,a){switch(e){case i.AnnotationEditorParamsType.INK_THICKNESS:t._defaultThickness=a;break;case i.AnnotationEditorParamsType.INK_COLOR:t._defaultColor=a;break;case i.AnnotationEditorParamsType.INK_OPACITY:t._defaultOpacity=a/100}}},{key:"defaultPropertiesToUpdate",get:function(){return[[i.AnnotationEditorParamsType.INK_THICKNESS,t._defaultThickness],[i.AnnotationEditorParamsType.INK_COLOR,t._defaultColor||n.AnnotationEditor._defaultLineColor],[i.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*t._defaultOpacity)]]}},{key:"deserialize",value:function(e,a,r){var l;if(e instanceof s.InkAnnotationElement)return null;var o=_superPropGet(t,"deserialize",this,2)([e,a,r]);o.thickness=e.thickness,o.color=(l=i.Util).makeHexColor.apply(l,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(e.color)),o.opacity=e.opacity;var u=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(o.pageDimensions,2),h=u[0],d=u[1],p=o.width*h,v=o.height*d,m=o.parentScale,b=e.thickness/2;_classPrivateFieldSet(f,o,!0),_classPrivateFieldSet(P,o,Math.round(p)),_classPrivateFieldSet(y,o,Math.round(v));var g,A=e.paths,C=e.rect,M=e.rotation,k=_createForOfIteratorHelper(A);try{for(k.s();!(g=k.n()).done;){var O=g.value.bezier;O=z.call(t,O,C,M);var D=[];o.paths.push(D);for(var F=m*(O[0]-b),S=m*(O[1]-b),T=2,I=O.length;T<I;T+=6){var w=m*(O[T]-b),x=m*(O[T+1]-b),R=m*(O[T+2]-b),L=m*(O[T+3]-b),B=m*(O[T+4]-b),W=m*(O[T+5]-b);D.push([[F,S],[w,x],[R,L],[B,W]]),F=B,S=W}var U=_assertClassBrand(t,this,H).call(this,D);o.bezierPath2D.push(U)}}catch(e){k.e(e)}finally{k.f()}var G=_assertClassBrand(E,o,X).call(o);return _classPrivateFieldSet(c,o,Math.max(n.AnnotationEditor.MIN_SIZE,G[2]-G[0])),_classPrivateFieldSet(_,o,Math.max(n.AnnotationEditor.MIN_SIZE,G[3]-G[1])),_assertClassBrand(E,o,N).call(o,p,v),o}}])}(n.AnnotationEditor);function M(e){var t=this,a=this.thickness;this.addCommands({cmd:function(){t.thickness=e,_assertClassBrand(E,t,J).call(t)},undo:function(){t.thickness=a,_assertClassBrand(E,t,J).call(t)},mustExec:!0,type:i.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})}function k(e){var t=this,a=this.color;this.addCommands({cmd:function(){t.color=e,_assertClassBrand(E,t,B).call(t)},undo:function(){t.color=a,_assertClassBrand(E,t,B).call(t)},mustExec:!0,type:i.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})}function O(e){var t=this;e/=100;var a=this.opacity;this.addCommands({cmd:function(){t.opacity=e,_assertClassBrand(E,t,B).call(t)},undo:function(){t.opacity=a,_assertClassBrand(E,t,B).call(t)},mustExec:!0,type:i.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})}function D(){var e=this.parentRotation,t=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),a=t[0],r=t[1];switch(e){case 90:return[0,r,r,a];case 180:return[a,r,a,r];case 270:return[a,0,r,a];default:return[0,0,a,r]}}function F(){var e=this.ctx,t=this.color,a=this.opacity,r=this.thickness,i=this.parentScale,n=this.scaleFactor;e.lineWidth=r*i/n,e.lineCap="round",e.lineJoin="round",e.miterLimit=10,e.strokeStyle="".concat(t).concat((0,o.opacityToHex)(a))}function S(e,t){var a,i=this;this.canvas.addEventListener("contextmenu",l.noContextMenu),this.canvas.addEventListener("pointerleave",_classPrivateFieldGet(h,this)),this.canvas.addEventListener("pointermove",_classPrivateFieldGet(u,this)),this.canvas.addEventListener("pointerup",_classPrivateFieldGet(d,this)),this.canvas.removeEventListener("pointerdown",_classPrivateFieldGet(p,this)),this.isEditing=!0,_classPrivateFieldGet(b,this)||(_classPrivateFieldSet(b,this,!0),_assertClassBrand(E,this,K).call(this),this.thickness||(this.thickness=r._defaultThickness),this.color||(this.color=r._defaultColor||n.AnnotationEditor._defaultLineColor),null!==(a=this.opacity)&&void 0!==a||(this.opacity=r._defaultOpacity)),this.currentPath.push([e,t]),_classPrivateFieldSet(m,this,!1),_assertClassBrand(E,this,F).call(this),_classPrivateFieldSet(A,this,(function(){_assertClassBrand(E,i,x).call(i),_classPrivateFieldGet(A,i)&&window.requestAnimationFrame(_classPrivateFieldGet(A,i))})),window.requestAnimationFrame(_classPrivateFieldGet(A,this))}function T(e,t){var a,r=this.currentPath.at(-1),i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(r,2),n=i[0],s=i[1];if(!(this.currentPath.length>1&&e===n&&t===s)){var l,o,_=this.currentPath,c=_classPrivateFieldGet(v,this);if(_.push([e,t]),_classPrivateFieldSet(m,this,!0),_.length<=2)return(l=c).moveTo.apply(l,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(_[0])),void c.lineTo(e,t);3===_.length&&(_classPrivateFieldSet(v,this,c=new Path2D),(o=c).moveTo.apply(o,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(_[0]))),(a=_assertClassBrand(E,this,R)).call.apply(a,[this,c].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(_.at(-3)),(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(_.at(-2)),[e,t]))}}function I(){var e;if(0!==this.currentPath.length){var t=this.currentPath.at(-1);(e=_classPrivateFieldGet(v,this)).lineTo.apply(e,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(t))}}function w(e,t){var a,r=this;if(_classPrivateFieldSet(A,this,null),e=Math.min(Math.max(e,0),this.canvas.width),t=Math.min(Math.max(t,0),this.canvas.height),_assertClassBrand(E,this,T).call(this,e,t),_assertClassBrand(E,this,I).call(this),1!==this.currentPath.length)a=_assertClassBrand(E,this,L).call(this);else{var i=[e,t];a=[[i,i.slice(),i.slice(),i]]}var n=_classPrivateFieldGet(v,this),s=this.currentPath;this.currentPath=[],_classPrivateFieldSet(v,this,new Path2D),this.addCommands({cmd:function(){r.allRawPaths.push(s),r.paths.push(a),r.bezierPath2D.push(n),r.rebuild()},undo:function(){r.allRawPaths.pop(),r.paths.pop(),r.bezierPath2D.pop(),0===r.paths.length?r.remove():(r.canvas||(_assertClassBrand(E,r,U).call(r),_assertClassBrand(E,r,G).call(r)),_assertClassBrand(E,r,J).call(r))},mustExec:!0})}function x(){if(_classPrivateFieldGet(m,this)){_classPrivateFieldSet(m,this,!1),Math.ceil(this.thickness*this.parentScale);var e=this.currentPath.slice(-3),t=e.map((function(e){return e[0]})),a=e.map((function(e){return e[1]})),r=(Math.min.apply(Math,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(t)),Math.max.apply(Math,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(t)),Math.min.apply(Math,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(a)),Math.max.apply(Math,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(a)),this.ctx);r.save(),r.clearRect(0,0,this.canvas.width,this.canvas.height);var i,n=_createForOfIteratorHelper(this.bezierPath2D);try{for(n.s();!(i=n.n()).done;){var s=i.value;r.stroke(s)}}catch(e){n.e(e)}finally{n.f()}r.stroke(_classPrivateFieldGet(v,this)),r.restore()}}function R(e,t,a,r,i,n,s){var l=(t+r)/2,o=(a+i)/2,_=(r+n)/2,c=(i+s)/2;e.bezierCurveTo(l+2*(r-l)/3,o+2*(i-o)/3,_+2*(r-_)/3,c+2*(i-c)/3,_,c)}function L(){var e=this.currentPath;if(e.length<=2)return[[e[0],e[0],e.at(-1),e.at(-1)]];var t,a=[],r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e[0],2),i=r[0],n=r[1];for(t=1;t<e.length-2;t++){var s=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e[t],2),l=s[0],o=s[1],_=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e[t+1],2),c=(l+_[0])/2,u=(o+_[1])/2,h=[i+2*(l-i)/3,n+2*(o-n)/3],d=[c+2*(l-c)/3,u+2*(o-u)/3];a.push([[i,n],h,d,[c,u]]),i=c,n=u}var p=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e[t],2),v=p[0],f=p[1],m=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e[t+1],2),b=m[0],g=m[1],P=[i+2*(v-i)/3,n+2*(f-n)/3],y=[b+2*(v-b)/3,g+2*(f-g)/3];return a.push([[i,n],P,y,[b,g]]),a}function B(){if(this.isEmpty())_assertClassBrand(E,this,j).call(this);else{_assertClassBrand(E,this,F).call(this);var e=this.canvas,t=this.ctx;t.setTransform(1,0,0,1,0,0),t.clearRect(0,0,e.width,e.height),_assertClassBrand(E,this,j).call(this);var a,r=_createForOfIteratorHelper(this.bezierPath2D);try{for(r.s();!(a=r.n()).done;){var i=a.value;t.stroke(i)}}catch(e){r.e(e)}finally{r.f()}}}function W(e){var t=this;this.canvas.removeEventListener("pointerleave",_classPrivateFieldGet(h,this)),this.canvas.removeEventListener("pointermove",_classPrivateFieldGet(u,this)),this.canvas.removeEventListener("pointerup",_classPrivateFieldGet(d,this)),this.canvas.addEventListener("pointerdown",_classPrivateFieldGet(p,this)),setTimeout((function(){t.canvas.removeEventListener("contextmenu",l.noContextMenu)}),10),_assertClassBrand(E,this,w).call(this,e.offsetX,e.offsetY),this.addToAnnotationStorage(),this.setInBackground()}function U(){var e=this;this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=0,this.canvas.className="inkEditorCanvas",n.AnnotationEditor._l10nPromise.get("editor_ink_canvas_aria_label").then((function(t){var a;return null===(a=e.canvas)||void 0===a?void 0:a.setAttribute("aria-label",t)})),this.div.append(this.canvas),this.ctx=this.canvas.getContext("2d")}function G(){var e=this;_classPrivateFieldSet(g,this,new ResizeObserver((function(t){var a=t[0].contentRect;a.width&&a.height&&e.setDimensions(a.width,a.height)}))),_classPrivateFieldGet(g,this).observe(this.div)}function K(){if(_classPrivateFieldGet(b,this)){var e=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),t=e[0],a=e[1];this.canvas.width=Math.ceil(this.width*t),this.canvas.height=Math.ceil(this.height*a),_assertClassBrand(E,this,j).call(this)}}function N(e,t){var a=_assertClassBrand(E,this,Y).call(this),r=(e-a)/_classPrivateFieldGet(c,this),i=(t-a)/_classPrivateFieldGet(_,this);this.scaleFactor=Math.min(r,i)}function j(){var e=_assertClassBrand(E,this,Y).call(this)/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+e,this.translationY*this.scaleFactor+e)}function H(e){for(var t=new Path2D,a=0,r=e.length;a<r;a++){var i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(e[a],4),n=i[0],s=i[1],l=i[2],o=i[3];0===a&&t.moveTo.apply(t,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(n)),t.bezierCurveTo(s[0],s[1],l[0],l[1],o[0],o[1])}return t}function q(e,t,a){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(t,4),i=r[0],n=r[1],s=r[2],l=r[3];switch(a){case 0:for(var o=0,_=e.length;o<_;o+=2)e[o]+=i,e[o+1]=l-e[o+1];break;case 90:for(var c=0,u=e.length;c<u;c+=2){var h=e[c];e[c]=e[c+1]+i,e[c+1]=h+n}break;case 180:for(var d=0,p=e.length;d<p;d+=2)e[d]=s-e[d],e[d+1]+=n;break;case 270:for(var v=0,f=e.length;v<f;v+=2){var m=e[v];e[v]=s-e[v+1],e[v+1]=l-m}break;default:throw new Error("Invalid rotation")}return e}function z(e,t,a){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(t,4),i=r[0],n=r[1],s=r[2],l=r[3];switch(a){case 0:for(var o=0,_=e.length;o<_;o+=2)e[o]-=i,e[o+1]=l-e[o+1];break;case 90:for(var c=0,u=e.length;c<u;c+=2){var h=e[c];e[c]=e[c+1]-n,e[c+1]=h-i}break;case 180:for(var d=0,p=e.length;d<p;d+=2)e[d]=s-e[d],e[d+1]-=n;break;case 270:for(var v=0,f=e.length;v<f;v+=2){var m=e[v];e[v]=l-e[v+1],e[v+1]=s-m}break;default:throw new Error("Invalid rotation")}return e}function V(e,t,a,i){var n,s=[],l=this.thickness/2,o=e*t+l,_=e*a+l,c=_createForOfIteratorHelper(this.paths);try{for(c.s();!(n=c.n()).done;){for(var u=n.value,h=[],d=[],p=0,v=u.length;p<v;p++){var f=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(u[p],4),m=f[0],b=f[1],g=f[2],P=f[3],y=e*m[0]+o,A=e*m[1]+_,E=e*b[0]+o,C=e*b[1]+_,M=e*g[0]+o,k=e*g[1]+_,O=e*P[0]+o,D=e*P[1]+_;0===p&&(h.push(y,A),d.push(y,A)),h.push(E,C,M,k,O,D),d.push(E,C),p===v-1&&d.push(O,D)}s.push({bezier:q.call(r,h,i,this.rotation),points:q.call(r,d,i,this.rotation)})}}catch(e){c.e(e)}finally{c.f()}return s}function X(){var e,t=1/0,a=-1/0,r=1/0,n=-1/0,s=_createForOfIteratorHelper(this.paths);try{for(s.s();!(e=s.n()).done;){var l,o=_createForOfIteratorHelper(e.value);try{for(o.s();!(l=o.n()).done;){var _,c=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(l.value,4),u=c[0],h=c[1],d=c[2],p=c[3],v=(_=i.Util).bezierBoundingBox.apply(_,(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(u).concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(h),(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(d),(0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_10__.A)(p)));t=Math.min(t,v[0]),r=Math.min(r,v[1]),a=Math.max(a,v[2]),n=Math.max(n,v[3])}}catch(e){o.e(e)}finally{o.f()}}}catch(e){s.e(e)}finally{s.f()}return[t,r,a,n]}function Y(){return _classPrivateFieldGet(f,this)?Math.ceil(this.thickness*this.parentScale):0}function J(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.isEmpty())if(_classPrivateFieldGet(f,this)){var t=_assertClassBrand(E,this,X).call(this),a=_assertClassBrand(E,this,Y).call(this);_classPrivateFieldSet(c,this,Math.max(n.AnnotationEditor.MIN_SIZE,t[2]-t[0])),_classPrivateFieldSet(_,this,Math.max(n.AnnotationEditor.MIN_SIZE,t[3]-t[1]));var r=Math.ceil(a+_classPrivateFieldGet(c,this)*this.scaleFactor),i=Math.ceil(a+_classPrivateFieldGet(_,this)*this.scaleFactor),s=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),l=s[0],o=s[1];this.width=r/l,this.height=i/o,this.setAspectRatio(r,i);var u=this.translationX,h=this.translationY;this.translationX=-t[0],this.translationY=-t[1],_assertClassBrand(E,this,K).call(this),_assertClassBrand(E,this,B).call(this),_classPrivateFieldSet(P,this,r),_classPrivateFieldSet(y,this,i),this.setDims(r,i);var d=e?a/this.scaleFactor/2:0;this.translate(u-this.translationX-d,h-this.translationY-d)}else _assertClassBrand(E,this,B).call(this)}r=C,(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(C,"_defaultColor",null),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(C,"_defaultOpacity",1),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(C,"_defaultThickness",1),(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(C,"_type","ink"),t.InkEditor=C},function(e,t,a){var r;Object.defineProperty(t,"__esModule",{value:!0}),t.StampEditor=void 0;var i=a(1),n=a(4),s=a(6),l=a(29),o=new WeakMap,_=new WeakMap,c=new WeakMap,u=new WeakMap,h=new WeakMap,d=new WeakMap,p=new WeakMap,v=new WeakMap,f=new WeakMap,m=new WeakMap,b=new WeakSet,g=function(e){function t(e){var a;return(0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_6__.A)(this,t),_classPrivateMethodInitSpec(a=_callSuper(this,t,[_objectSpread(_objectSpread({},e),{},{name:"stampEditor"})]),b),_classPrivateFieldInitSpec(a,o,null),_classPrivateFieldInitSpec(a,_,null),_classPrivateFieldInitSpec(a,c,null),_classPrivateFieldInitSpec(a,u,null),_classPrivateFieldInitSpec(a,h,null),_classPrivateFieldInitSpec(a,d,null),_classPrivateFieldInitSpec(a,p,null),_classPrivateFieldInitSpec(a,v,null),_classPrivateFieldInitSpec(a,f,!1),_classPrivateFieldInitSpec(a,m,!1),_classPrivateFieldSet(u,a,e.bitmapUrl),_classPrivateFieldSet(h,a,e.bitmapFile),a}return(0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_7__.A)(t,e),(0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_8__.A)(t,[{key:"remove",value:function(){var e,a;_classPrivateFieldGet(_,this)&&(_classPrivateFieldSet(o,this,null),this._uiManager.imageManager.deleteId(_classPrivateFieldGet(_,this)),null===(e=_classPrivateFieldGet(d,this))||void 0===e||e.remove(),_classPrivateFieldSet(d,this,null),null===(a=_classPrivateFieldGet(p,this))||void 0===a||a.disconnect(),_classPrivateFieldSet(p,this,null)),_superPropGet(t,"remove",this,3)([])}},{key:"rebuild",value:function(){this.parent?(_superPropGet(t,"rebuild",this,3)([]),null!==this.div&&(_classPrivateFieldGet(_,this)&&_assertClassBrand(b,this,A).call(this),this.isAttachedToDOM||this.parent.add(this))):_classPrivateFieldGet(_,this)&&_assertClassBrand(b,this,A).call(this)}},{key:"onceAdded",value:function(){this._isDraggable=!0,this.div.focus()}},{key:"isEmpty",value:function(){return!(_classPrivateFieldGet(c,this)||_classPrivateFieldGet(o,this)||_classPrivateFieldGet(u,this)||_classPrivateFieldGet(h,this))}},{key:"isResizable",get:function(){return!0}},{key:"render",value:function(){if(this.div)return this.div;var e,a;if(this.width&&(e=this.x,a=this.y),_superPropGet(t,"render",this,3)([]),this.div.hidden=!0,_classPrivateFieldGet(o,this)?_assertClassBrand(b,this,E).call(this):_assertClassBrand(b,this,A).call(this),this.width){var r=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),i=r[0],n=r[1];this.setAt(e*i,a*n,this.width*i,this.height*n)}return this.div}},{key:"serialize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.isEmpty())return null;var a={annotationType:i.AnnotationEditorType.STAMP,bitmapId:_classPrivateFieldGet(_,this),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:_classPrivateFieldGet(f,this),structTreeParentId:this._structTreeParentId};if(e)return a.bitmapUrl=_assertClassBrand(b,this,O).call(this,!0),a.accessibilityData=this.altTextData,a;var r=this.altTextData,n=r.decorative,s=r.altText;if(!n&&s&&(a.accessibilityData={type:"Figure",alt:s}),null===t)return a;t.stamps||(t.stamps=new Map);var l=_classPrivateFieldGet(f,this)?(a.rect[2]-a.rect[0])*(a.rect[3]-a.rect[1]):null;if(t.stamps.has(_classPrivateFieldGet(_,this))){if(_classPrivateFieldGet(f,this)){var o=t.stamps.get(_classPrivateFieldGet(_,this));l>o.area&&(o.area=l,o.serialized.bitmap.close(),o.serialized.bitmap=_assertClassBrand(b,this,O).call(this,!1))}}else t.stamps.set(_classPrivateFieldGet(_,this),{area:l,serialized:a}),a.bitmap=_assertClassBrand(b,this,O).call(this,!1);return a}}],[{key:"initialize",value:function(e){n.AnnotationEditor.initialize(e)}},{key:"supportedTypes",get:function(){return(0,i.shadow)(this,"supportedTypes",["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"].map((function(e){return"image/".concat(e)})))}},{key:"supportedTypesStr",get:function(){return(0,i.shadow)(this,"supportedTypesStr",this.supportedTypes.join(","))}},{key:"isHandlingMimeForPasting",value:function(e){return this.supportedTypes.includes(e)}},{key:"paste",value:function(e,t){t.pasteEditor(i.AnnotationEditorType.STAMP,{bitmapFile:e.getAsFile()})}},{key:"deserialize",value:function(e,a,r){if(e instanceof l.StampAnnotationElement)return null;var i=_superPropGet(t,"deserialize",this,2)([e,a,r]),n=e.rect,s=e.bitmapUrl,o=e.bitmapId,c=e.isSvg,h=e.accessibilityData;o&&r.imageManager.isValidId(o)?_classPrivateFieldSet(_,i,o):_classPrivateFieldSet(u,i,s),_classPrivateFieldSet(f,i,c);var d=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(i.pageDimensions,2),p=d[0],v=d[1];return i.width=(n[2]-n[0])/p,i.height=(n[3]-n[1])/v,h&&(i.altTextData=h),i}}])}(n.AnnotationEditor);function P(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e?(_classPrivateFieldSet(o,this,e.bitmap),t||(_classPrivateFieldSet(_,this,e.id),_classPrivateFieldSet(f,this,e.isSvg)),_assertClassBrand(b,this,E).call(this)):this.remove()}function y(){_classPrivateFieldSet(c,this,null),this._uiManager.enableWaiting(!1),_classPrivateFieldGet(d,this)&&this.div.focus()}function A(){var e=this;if(_classPrivateFieldGet(_,this))return this._uiManager.enableWaiting(!0),void this._uiManager.imageManager.getFromId(_classPrivateFieldGet(_,this)).then((function(t){return _assertClassBrand(b,e,P).call(e,t,!0)})).finally((function(){return _assertClassBrand(b,e,y).call(e)}));if(_classPrivateFieldGet(u,this)){var t=_classPrivateFieldGet(u,this);return _classPrivateFieldSet(u,this,null),this._uiManager.enableWaiting(!0),void _classPrivateFieldSet(c,this,this._uiManager.imageManager.getFromUrl(t).then((function(t){return _assertClassBrand(b,e,P).call(e,t)})).finally((function(){return _assertClassBrand(b,e,y).call(e)})))}if(_classPrivateFieldGet(h,this)){var a=_classPrivateFieldGet(h,this);return _classPrivateFieldSet(h,this,null),this._uiManager.enableWaiting(!0),void _classPrivateFieldSet(c,this,this._uiManager.imageManager.getFromFile(a).then((function(t){return _assertClassBrand(b,e,P).call(e,t)})).finally((function(){return _assertClassBrand(b,e,y).call(e)})))}var i=document.createElement("input");i.type="file",i.accept=r.supportedTypesStr,_classPrivateFieldSet(c,this,new Promise((function(t){i.addEventListener("change",(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_11__.A)(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark((function a(){var r;return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:if(i.files&&0!==i.files.length){a.next=1;break}e.remove(),a.next=3;break;case 1:return e._uiManager.enableWaiting(!0),a.next=2,e._uiManager.imageManager.getFromFile(i.files[0]);case 2:r=a.sent,_assertClassBrand(b,e,P).call(e,r);case 3:t();case 4:case"end":return a.stop()}}),a)})))),i.addEventListener("cancel",(function(){e.remove(),t()}))})).finally((function(){return _assertClassBrand(b,e,y).call(e)}))),i.click()}function E(){var e=this.div,t=_classPrivateFieldGet(o,this),a=t.width,r=t.height,i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageDimensions,2),n=i[0],s=i[1],l=.75;if(this.width)a=this.width*n,r=this.height*s;else if(a>l*n||r>l*s){var _=Math.min(l*n/a,l*s/r);a*=_,r*=_}var c=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),u=c[0],h=c[1];this.setDims(a*u/n,r*h/s),this._uiManager.enableWaiting(!1);var p=_classPrivateFieldSet(d,this,document.createElement("canvas"));e.append(p),e.hidden=!1,_assertClassBrand(b,this,k).call(this,a,r),_assertClassBrand(b,this,D).call(this),_classPrivateFieldGet(m,this)||(this.parent.addUndoableEditor(this),_classPrivateFieldSet(m,this,!0)),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"inserted_image"}}}),this.addAltTextButton()}function C(e,t){var a,r=this,i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.parentDimensions,2),n=i[0],s=i[1];this.width=e/n,this.height=t/s,this.setDims(e,t),null!==(a=this._initialOptions)&&void 0!==a&&a.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,null!==_classPrivateFieldGet(v,this)&&clearTimeout(_classPrivateFieldGet(v,this)),_classPrivateFieldSet(v,this,setTimeout((function(){_classPrivateFieldSet(v,r,null),_assertClassBrand(b,r,k).call(r,e,t)}),200))}function M(e,t){for(var a=_classPrivateFieldGet(o,this),r=a.width,i=a.height,n=_classPrivateFieldGet(o,this);r>2*e||i>2*t;){var s=r,l=i;r>2*e&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2)),i>2*t&&(i=i>=16384?Math.floor(i/2)-1:Math.ceil(i/2));var _=new OffscreenCanvas(r,i);_.getContext("2d").drawImage(n,0,0,s,l,0,0,r,i),n=_.transferToImageBitmap()}return n}function k(e,t){e=Math.ceil(e),t=Math.ceil(t);var a=_classPrivateFieldGet(d,this);if(a&&(a.width!==e||a.height!==t)){a.width=e,a.height=t;var r=_classPrivateFieldGet(f,this)?_classPrivateFieldGet(o,this):_assertClassBrand(b,this,M).call(this,e,t),i=a.getContext("2d");i.filter=this._uiManager.hcmFilter,i.drawImage(r,0,0,r.width,r.height,0,0,e,t)}}function O(e){if(e){if(_classPrivateFieldGet(f,this)){var t=this._uiManager.imageManager.getSvgUrl(_classPrivateFieldGet(_,this));if(t)return t}var a=document.createElement("canvas"),r=_classPrivateFieldGet(o,this);return a.width=r.width,a.height=r.height,a.getContext("2d").drawImage(_classPrivateFieldGet(o,this),0,0),a.toDataURL()}if(_classPrivateFieldGet(f,this)){var i=(0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_9__.A)(this.pageDimensions,2),n=i[0],l=i[1],c=Math.round(this.width*n*s.PixelsPerInch.PDF_TO_CSS_UNITS),u=Math.round(this.height*l*s.PixelsPerInch.PDF_TO_CSS_UNITS),h=new OffscreenCanvas(c,u);return h.getContext("2d").drawImage(_classPrivateFieldGet(o,this),0,0,_classPrivateFieldGet(o,this).width,_classPrivateFieldGet(o,this).height,0,0,c,u),h.transferToImageBitmap()}return structuredClone(_classPrivateFieldGet(o,this))}function D(){var e=this;_classPrivateFieldSet(p,this,new ResizeObserver((function(t){var a=t[0].contentRect;a.width&&a.height&&_assertClassBrand(b,e,C).call(e,a.width,a.height)}))),_classPrivateFieldGet(p,this).observe(this.div)}r=g,(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__.A)(g,"_type","stamp"),t.StampEditor=g}],__webpack_module_cache__={};function __w_pdfjs_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var a=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](a,a.exports,__w_pdfjs_require__),a.exports}var __nested_webpack_exports__={};return function(){var e=__nested_webpack_exports__;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"AbortException",{enumerable:!0,get:function(){return t.AbortException}}),Object.defineProperty(e,"AnnotationEditorLayer",{enumerable:!0,get:function(){return n.AnnotationEditorLayer}}),Object.defineProperty(e,"AnnotationEditorParamsType",{enumerable:!0,get:function(){return t.AnnotationEditorParamsType}}),Object.defineProperty(e,"AnnotationEditorType",{enumerable:!0,get:function(){return t.AnnotationEditorType}}),Object.defineProperty(e,"AnnotationEditorUIManager",{enumerable:!0,get:function(){return s.AnnotationEditorUIManager}}),Object.defineProperty(e,"AnnotationLayer",{enumerable:!0,get:function(){return l.AnnotationLayer}}),Object.defineProperty(e,"AnnotationMode",{enumerable:!0,get:function(){return t.AnnotationMode}}),Object.defineProperty(e,"CMapCompressionType",{enumerable:!0,get:function(){return t.CMapCompressionType}}),Object.defineProperty(e,"DOMSVGFactory",{enumerable:!0,get:function(){return r.DOMSVGFactory}}),Object.defineProperty(e,"FeatureTest",{enumerable:!0,get:function(){return t.FeatureTest}}),Object.defineProperty(e,"GlobalWorkerOptions",{enumerable:!0,get:function(){return o.GlobalWorkerOptions}}),Object.defineProperty(e,"ImageKind",{enumerable:!0,get:function(){return t.ImageKind}}),Object.defineProperty(e,"InvalidPDFException",{enumerable:!0,get:function(){return t.InvalidPDFException}}),Object.defineProperty(e,"MissingPDFException",{enumerable:!0,get:function(){return t.MissingPDFException}}),Object.defineProperty(e,"OPS",{enumerable:!0,get:function(){return t.OPS}}),Object.defineProperty(e,"PDFDataRangeTransport",{enumerable:!0,get:function(){return a.PDFDataRangeTransport}}),Object.defineProperty(e,"PDFDateString",{enumerable:!0,get:function(){return r.PDFDateString}}),Object.defineProperty(e,"PDFWorker",{enumerable:!0,get:function(){return a.PDFWorker}}),Object.defineProperty(e,"PasswordResponses",{enumerable:!0,get:function(){return t.PasswordResponses}}),Object.defineProperty(e,"PermissionFlag",{enumerable:!0,get:function(){return t.PermissionFlag}}),Object.defineProperty(e,"PixelsPerInch",{enumerable:!0,get:function(){return r.PixelsPerInch}}),Object.defineProperty(e,"PromiseCapability",{enumerable:!0,get:function(){return t.PromiseCapability}}),Object.defineProperty(e,"RenderingCancelledException",{enumerable:!0,get:function(){return r.RenderingCancelledException}}),Object.defineProperty(e,"SVGGraphics",{enumerable:!0,get:function(){return a.SVGGraphics}}),Object.defineProperty(e,"UnexpectedResponseException",{enumerable:!0,get:function(){return t.UnexpectedResponseException}}),Object.defineProperty(e,"Util",{enumerable:!0,get:function(){return t.Util}}),Object.defineProperty(e,"VerbosityLevel",{enumerable:!0,get:function(){return t.VerbosityLevel}}),Object.defineProperty(e,"XfaLayer",{enumerable:!0,get:function(){return _.XfaLayer}}),Object.defineProperty(e,"build",{enumerable:!0,get:function(){return a.build}}),Object.defineProperty(e,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return t.createValidAbsoluteUrl}}),Object.defineProperty(e,"getDocument",{enumerable:!0,get:function(){return a.getDocument}}),Object.defineProperty(e,"getFilenameFromUrl",{enumerable:!0,get:function(){return r.getFilenameFromUrl}}),Object.defineProperty(e,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return r.getPdfFilenameFromUrl}}),Object.defineProperty(e,"getXfaPageViewport",{enumerable:!0,get:function(){return r.getXfaPageViewport}}),Object.defineProperty(e,"isDataScheme",{enumerable:!0,get:function(){return r.isDataScheme}}),Object.defineProperty(e,"isPdfFile",{enumerable:!0,get:function(){return r.isPdfFile}}),Object.defineProperty(e,"loadScript",{enumerable:!0,get:function(){return r.loadScript}}),Object.defineProperty(e,"noContextMenu",{enumerable:!0,get:function(){return r.noContextMenu}}),Object.defineProperty(e,"normalizeUnicode",{enumerable:!0,get:function(){return t.normalizeUnicode}}),Object.defineProperty(e,"renderTextLayer",{enumerable:!0,get:function(){return i.renderTextLayer}}),Object.defineProperty(e,"setLayerDimensions",{enumerable:!0,get:function(){return r.setLayerDimensions}}),Object.defineProperty(e,"shadow",{enumerable:!0,get:function(){return t.shadow}}),Object.defineProperty(e,"updateTextLayer",{enumerable:!0,get:function(){return i.updateTextLayer}}),Object.defineProperty(e,"version",{enumerable:!0,get:function(){return a.version}});var t=__w_pdfjs_require__(1),a=__w_pdfjs_require__(2),r=__w_pdfjs_require__(6),i=__w_pdfjs_require__(26),n=__w_pdfjs_require__(27),s=__w_pdfjs_require__(5),l=__w_pdfjs_require__(29),o=__w_pdfjs_require__(14),_=__w_pdfjs_require__(32)}(),__nested_webpack_exports__}()},"object"===("undefined"==typeof exports?"undefined":(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(exports))&&"object"===(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(module)?module.exports=root.pdfjsLib=factory():"function"==typeof define&&__webpack_require__.amdO?define("pdfjs-dist/build/pdf",[],(function(){return root.pdfjsLib=factory()})):"object"===("undefined"==typeof exports?"undefined":(0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_5__.A)(exports))?exports["pdfjs-dist/build/pdf"]=root.pdfjsLib=factory():root["pdfjs-dist/build/pdf"]=root.pdfjsLib=factory()},1791:function(e,t,a){var r=a(5172),i=a(5546);e.exports=function e(t,a){function n(e,i,s,l){try{var o=t[e](i),_=o.value;return _ instanceof r?a.resolve(_.v).then((function(e){n("next",e,s,l)}),(function(e){n("throw",e,s,l)})):a.resolve(_).then((function(e){o.value=e,s(o)}),(function(e){return n("throw",e,s,l)}))}catch(e){l(e)}}var s;this.next||(i(e.prototype),i(e.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",(function(){return this}))),i(this,"_invoke",(function(e,t,r){function i(){return new a((function(t,a){n(e,r,t,a)}))}return s=s?s.then(i,i):i()}),!0)},e.exports.__esModule=!0,e.exports.default=e.exports},2284:function(e,t,a){"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}a.d(t,{A:function(){return r}})},2706:function(){},2901:function(e,t,a){"use strict";a.d(t,{A:function(){return n}});var r=a(816);function i(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(0,r.A)(i.key),i)}}function n(e,t,a){return t&&i(e.prototype,t),a&&i(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}},3029:function(e,t,a){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}a.d(t,{A:function(){return r}})},3145:function(e,t,a){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=Array(t);a<t;a++)r[a]=e[a];return r}a.d(t,{A:function(){return r}})},3662:function(e,t,a){"use strict";function r(e,t){return r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,t)}a.d(t,{A:function(){return r}})},3738:function(e){function t(a){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(a)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},3954:function(e,t,a){"use strict";function r(e){return r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},r(e)}a.d(t,{A:function(){return r}})},4373:function(e){e.exports=function(e){var t=Object(e),a=[];for(var r in t)a.unshift(r);return function e(){for(;a.length;)if((r=a.pop())in t)return e.value=r,e.done=!1,e;return e.done=!0,e}},e.exports.__esModule=!0,e.exports.default=e.exports},4467:function(e,t,a){"use strict";a.d(t,{A:function(){return i}});var r=a(816);function i(e,t,a){return(t=(0,r.A)(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}},4633:function(e,t,a){var r=a(5172),i=a(6993),n=a(5869),s=a(887),l=a(1791),o=a(4373),_=a(579);function c(){"use strict";var t=i(),a=t.m(c),u=(Object.getPrototypeOf?Object.getPrototypeOf(a):a.__proto__).constructor;function h(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===u||"GeneratorFunction"===(t.displayName||t.name))}var d={throw:1,return:2,break:3,continue:3};function p(e){var t,a;return function(r){t||(t={stop:function(){return a(r.a,2)},catch:function(){return r.v},abrupt:function(e,t){return a(r.a,d[e],t)},delegateYield:function(e,i,n){return t.resultName=i,a(r.d,_(e),n)},finish:function(e){return a(r.f,e)}},a=function(e,a,i){r.p=t.prev,r.n=t.next;try{return e(a,i)}finally{t.next=r.n}}),t.resultName&&(t[t.resultName]=r.v,t.resultName=void 0),t.sent=r.v,t.next=r.n;try{return e.call(this,t)}finally{r.p=t.prev,r.n=t.next}}}return(e.exports=c=function(){return{wrap:function(e,a,r,i){return t.w(p(e),a,r,i&&i.reverse())},isGeneratorFunction:h,mark:t.m,awrap:function(e,t){return new r(e,t)},AsyncIterator:l,async:function(e,t,a,r,i){return(h(t)?s:n)(p(e),t,a,r,i)},keys:o,values:_}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=c,e.exports.__esModule=!0,e.exports.default=e.exports},4756:function(e,t,a){var r=a(4633)();e.exports=r;try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}},5172:function(e){e.exports=function(e,t){this.v=e,this.k=t},e.exports.__esModule=!0,e.exports.default=e.exports},5458:function(e,t,a){"use strict";a.d(t,{A:function(){return n}});var r=a(3145),i=a(7800);function n(e){return function(e){if(Array.isArray(e))return(0,r.A)(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||(0,i.A)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},5501:function(e,t,a){"use strict";a.d(t,{A:function(){return i}});var r=a(3662);function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,r.A)(e,t)}},5546:function(e){function t(a,r,i,n){var s=Object.defineProperty;try{s({},"",{})}catch(a){s=0}e.exports=t=function(e,a,r,i){if(a)s?s(e,a,{value:r,enumerable:!i,configurable:!i,writable:!i}):e[a]=r;else{var n=function(a,r){t(e,a,(function(e){return this._invoke(a,r,e)}))};n("next",0),n("throw",1),n("return",2)}},e.exports.__esModule=!0,e.exports.default=e.exports,t(a,r,i,n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},5869:function(e,t,a){var r=a(887);e.exports=function(e,t,a,i,n){var s=r(e,t,a,i,n);return s.next().then((function(e){return e.done?e.value:s.next()}))},e.exports.__esModule=!0,e.exports.default=e.exports},6233:function(){},6359:function(){},6822:function(e,t,a){"use strict";a.d(t,{A:function(){return n}});var r=a(2284),i=a(9417);function n(e,t){if(t&&("object"==(0,r.A)(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return(0,i.A)(e)}},6993:function(e,t,a){var r=a(5546);function i(){var t,a,n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",l=n.toStringTag||"@@toStringTag";function o(e,i,n,s){var l=i&&i.prototype instanceof c?i:c,o=Object.create(l.prototype);return r(o,"_invoke",function(e,r,i){var n,s,l,o=0,c=i||[],u=!1,h={p:0,n:0,v:t,a:d,f:d.bind(t,4),d:function(e,a){return n=e,s=0,l=t,h.n=a,_}};function d(e,r){for(s=e,l=r,a=0;!u&&o&&!i&&a<c.length;a++){var i,n=c[a],d=h.p,p=n[2];e>3?(i=p===r)&&(l=n[(s=n[4])?5:(s=3,3)],n[4]=n[5]=t):n[0]<=d&&((i=e<2&&d<n[1])?(s=0,h.v=r,h.n=n[1]):d<p&&(i=e<3||n[0]>r||r>p)&&(n[4]=e,n[5]=r,h.n=p,s=0))}if(i||e>1)return _;throw u=!0,r}return function(i,c,p){if(o>1)throw TypeError("Generator is already running");for(u&&1===c&&d(c,p),s=c,l=p;(a=s<2?t:l)||!u;){n||(s?s<3?(s>1&&(h.n=-1),d(s,l)):h.n=l:h.v=l);try{if(o=2,n){if(s||(i="next"),a=n[i]){if(!(a=a.call(n,l)))throw TypeError("iterator result is not an object");if(!a.done)return a;l=a.value,s<2&&(s=0)}else 1===s&&(a=n.return)&&a.call(n),s<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),s=1);n=t}else if((a=(u=h.n<0)?l:e.call(r,h))!==_)break}catch(e){n=t,s=1,l=e}finally{o=1}}return{value:a,done:u}}}(e,n,s),!0),o}var _={};function c(){}function u(){}function h(){}a=Object.getPrototypeOf;var d=[][s]?a(a([][s]())):(r(a={},s,(function(){return this})),a),p=h.prototype=c.prototype=Object.create(d);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,r(e,l,"GeneratorFunction")),e.prototype=Object.create(p),e}return u.prototype=h,r(p,"constructor",h),r(h,"constructor",u),u.displayName="GeneratorFunction",r(h,l,"GeneratorFunction"),r(p),r(p,l,"Generator"),r(p,s,(function(){return this})),r(p,"toString",(function(){return"[object Generator]"})),(e.exports=i=function(){return{w:o,m:v}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=i,e.exports.__esModule=!0,e.exports.default=e.exports},7800:function(e,t,a){"use strict";a.d(t,{A:function(){return i}});var r=a(3145);function i(e,t){if(e){if("string"==typeof e)return(0,r.A)(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?(0,r.A)(e,t):void 0}}},8680:function(){},9417:function(e,t,a){"use strict";function r(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}a.d(t,{A:function(){return r}})}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var a=__webpack_module_cache__[e]={id:e,loaded:!1,exports:{}};return __webpack_modules__[e](a,a.exports,__webpack_require__),a.loaded=!0,a.exports}__webpack_require__.amdO={},__webpack_require__.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=function(e,t){for(var a in t)__webpack_require__.o(t,a)&&!__webpack_require__.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},__webpack_require__.hmd=function(e){return(e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:function(){throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e},__webpack_require__.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__(1750);return __webpack_exports__}()}));