/*! For license information please see bundle.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.FoliateJS=e():t.FoliateJS=e()}(window,(function(){return function(){var t,e,n,r,i={34:function(t,e,n){"use strict";var r=n(4901);t.exports=function(t){return"object"==typeof t?null!==t:r(t)}},81:function(t,e,n){"use strict";var r=n(9565),i=n(9306),o=n(8551),a=n(6823),s=n(851),u=TypeError;t.exports=function(t,e){var n=arguments.length<2?s(t):e;if(i(n))return o(r(n,t));throw new u(a(t)+" is not iterable")}},113:function(t,e,n){"use strict";var r=n(6518),i=n(9213).find,o=n(6469),a="find",s=!0;a in[]&&Array(1)[a]((function(){s=!1})),r({target:"Array",proto:!0,forced:s},{find:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),o(a)},122:function(t,e,n){"use strict";var r=n(6518),i=n(4576),o=n(1955),a=n(9306),s=n(2812),u=n(9039),c=n(3724);r({global:!0,enumerable:!0,dontCallGetSet:!0,forced:u((function(){return c&&1!==Object.getOwnPropertyDescriptor(i,"queueMicrotask").value.length}))},{queueMicrotask:function(t){s(arguments.length,1),o(a(t))}})},193:function(t,e,n){"use strict";n(511)("hasInstance")},221:function(t,e,n){"use strict";var r=n(6518),i=n(9039),o=n(34),a=n(2195),s=n(5652),u=Object.isSealed;r({target:"Object",stat:!0,forced:s||i((function(){u(1)}))},{isSealed:function(t){return!o(t)||!(!s||"ArrayBuffer"!==a(t))||!!u&&u(t)}})},235:function(t,e,n){"use strict";var r=n(9213).forEach,i=n(4598)("forEach");t.exports=i?[].forEach:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}},237:function(t,e,n){"use strict";n(6469)("flatMap")},259:function(t,e,n){"use strict";var r=n(4376),i=n(6198),o=n(6837),a=n(6080),s=function(t,e,n,u,c,l,f,h){for(var d,p,v=c,g=0,y=!!f&&a(f,h);g<u;)g in n&&(d=y?y(n[g],g,e):n[g],l>0&&r(d)?(p=i(d),v=s(t,e,d,p,v,l-1)-1):(o(v+1),t[v]=d),v++),g++;return v};t.exports=s},280:function(t,e,n){"use strict";var r=n(6518),i=n(7751),o=n(6395),a=n(550),s=n(916).CONSTRUCTOR,u=n(3438),c=i("Promise"),l=o&&!s;r({target:"Promise",stat:!0,forced:o||s},{resolve:function(t){return u(l&&this===c?a:this,t)}})},283:function(t,e,n){"use strict";var r=n(9504),i=n(9039),o=n(4901),a=n(9297),s=n(3724),u=n(350).CONFIGURABLE,c=n(3706),l=n(1181),f=l.enforce,h=l.get,d=String,p=Object.defineProperty,v=r("".slice),g=r("".replace),y=r([].join),b=s&&!i((function(){return 8!==p((function(){}),"length",{value:8}).length})),m=String(String).split("String"),x=t.exports=function(t,e,n){"Symbol("===v(d(e),0,7)&&(e="["+g(d(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(e="get "+e),n&&n.setter&&(e="set "+e),(!a(t,"name")||u&&t.name!==e)&&(s?p(t,"name",{value:e,configurable:!0}):t.name=e),b&&n&&a(n,"arity")&&t.length!==n.arity&&p(t,"length",{value:n.arity});try{n&&a(n,"constructor")&&n.constructor?s&&p(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=f(t);return a(r,"source")||(r.source=y(m,"string"==typeof e?e:"")),t};Function.prototype.toString=x((function(){return o(this)&&h(this).source||c(this)}),"toString")},287:function(t,e,n){"use strict";n(6518)({target:"Object",stat:!0},{setPrototypeOf:n(2967)})},296:function(t,e,n){"use strict";n.d(e,{A:function(){return i}});var r=n(7800);function i(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],u=!0,c=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(s.push(r.value),s.length!==e);u=!0);}catch(t){c=!0,i=t}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw i}}return s}}(t,e)||(0,r.A)(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},298:function(t,e,n){"use strict";var r=n(2195),i=n(5397),o=n(8480).f,a=n(7680),s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return s&&"Window"===r(t)?function(t){try{return o(t)}catch(t){return a(s)}}(t):o(i(t))}},326:function(t,e,n){"use strict";n(511)("unscopables")},350:function(t,e,n){"use strict";var r=n(3724),i=n(9297),o=Function.prototype,a=r&&Object.getOwnPropertyDescriptor,s=i(o,"name"),u=s&&"something"===function(){}.name,c=s&&(!r||r&&a(o,"name").configurable);t.exports={EXISTS:s,PROPER:u,CONFIGURABLE:c}},373:function(t,e,n){"use strict";var r=n(4576),i=n(7476),o=n(9039),a=n(9306),s=n(4488),u=n(4644),c=n(3709),l=n(3763),f=n(9519),h=n(3607),d=u.aTypedArray,p=u.exportTypedArrayMethod,v=r.Uint16Array,g=v&&i(v.prototype.sort),y=!(!g||o((function(){g(new v(2),null)}))&&o((function(){g(new v(2),{})}))),b=!!g&&!o((function(){if(f)return f<74;if(c)return c<67;if(l)return!0;if(h)return h<602;var t,e,n=new v(516),r=Array(516);for(t=0;t<516;t++)e=t%4,n[t]=515-t,r[t]=t-2*e+3;for(g(n,(function(t,e){return(t/4|0)-(e/4|0)})),t=0;t<516;t++)if(n[t]!==r[t])return!0}));p("sort",(function(t){return void 0!==t&&a(t),b?g(this,t):s(d(this),function(t){return function(e,n){return void 0!==t?+t(e,n)||0:n!=n?-1:e!=e?1:0===e&&0===n?1/e>0&&1/n<0?1:-1:e>n}}(t))}),!b||y)},397:function(t,e,n){"use strict";var r=n(7751);t.exports=r("document","documentElement")},421:function(t){"use strict";t.exports={}},431:function(t,e,n){"use strict";n(6518)({target:"Reflect",stat:!0},{has:function(t,e){return e in t}})},436:function(t,e,n){"use strict";var r,i,o,a,s=n(6518),u=n(6395),c=n(6193),l=n(4576),f=n(9167),h=n(9565),d=n(6840),p=n(2967),v=n(687),g=n(7633),y=n(9306),b=n(4901),m=n(34),x=n(679),w=n(2293),k=n(9225).set,A=n(1955),S=n(3138),E=n(1103),_=n(8265),T=n(1181),O=n(550),M=n(916),L=n(6043),C="Promise",I=M.CONSTRUCTOR,R=M.REJECTION_EVENT,j=M.SUBCLASSING,P=T.getterFor(C),F=T.set,N=O&&O.prototype,D=O,z=N,B=l.TypeError,U=l.document,W=l.process,H=L.f,q=H,V=!!(U&&U.createEvent&&l.dispatchEvent),$="unhandledrejection",G=function(t){var e;return!(!m(t)||!b(e=t.then))&&e},X=function(t,e){var n,r,i,o=e.value,a=1===e.state,s=a?t.ok:t.fail,u=t.resolve,c=t.reject,l=t.domain;try{s?(a||(2===e.rejection&&Q(e),e.rejection=1),!0===s?n=o:(l&&l.enter(),n=s(o),l&&(l.exit(),i=!0)),n===t.promise?c(new B("Promise-chain cycle")):(r=G(n))?h(r,n,u,c):u(n)):c(o)}catch(t){l&&!i&&l.exit(),c(t)}},Y=function(t,e){t.notified||(t.notified=!0,A((function(){for(var n,r=t.reactions;n=r.get();)X(n,t);t.notified=!1,e&&!t.rejection&&J(t)})))},K=function(t,e,n){var r,i;V?((r=U.createEvent("Event")).promise=e,r.reason=n,r.initEvent(t,!1,!0),l.dispatchEvent(r)):r={promise:e,reason:n},!R&&(i=l["on"+t])?i(r):t===$&&S("Unhandled promise rejection",n)},J=function(t){h(k,l,(function(){var e,n=t.facade,r=t.value;if(Z(t)&&(e=E((function(){c?W.emit("unhandledRejection",r,n):K($,n,r)})),t.rejection=c||Z(t)?2:1,e.error))throw e.value}))},Z=function(t){return 1!==t.rejection&&!t.parent},Q=function(t){h(k,l,(function(){var e=t.facade;c?W.emit("rejectionHandled",e):K("rejectionhandled",e,t.value)}))},tt=function(t,e,n){return function(r){t(e,r,n)}},et=function(t,e,n){t.done||(t.done=!0,n&&(t=n),t.value=e,t.state=2,Y(t,!0))},nt=function(t,e,n){if(!t.done){t.done=!0,n&&(t=n);try{if(t.facade===e)throw new B("Promise can't be resolved itself");var r=G(e);r?A((function(){var n={done:!1};try{h(r,e,tt(nt,n,t),tt(et,n,t))}catch(e){et(n,e,t)}})):(t.value=e,t.state=1,Y(t,!1))}catch(e){et({done:!1},e,t)}}};if(I&&(z=(D=function(t){x(this,z),y(t),h(r,this);var e=P(this);try{t(tt(nt,e),tt(et,e))}catch(t){et(e,t)}}).prototype,(r=function(t){F(this,{type:C,done:!1,notified:!1,parent:!1,reactions:new _,rejection:!1,state:0,value:null})}).prototype=d(z,"then",(function(t,e){var n=P(this),r=H(w(this,D));return n.parent=!0,r.ok=!b(t)||t,r.fail=b(e)&&e,r.domain=c?W.domain:void 0,0===n.state?n.reactions.add(r):A((function(){X(r,n)})),r.promise})),i=function(){var t=new r,e=P(t);this.promise=t,this.resolve=tt(nt,e),this.reject=tt(et,e)},L.f=H=function(t){return t===D||t===o?new i(t):q(t)},!u&&b(O)&&N!==Object.prototype)){a=N.then,j||d(N,"then",(function(t,e){var n=this;return new D((function(t,e){h(a,n,t,e)})).then(t,e)}),{unsafe:!0});try{delete N.constructor}catch(t){}p&&p(N,z)}s({global:!0,constructor:!0,wrap:!0,forced:I},{Promise:D}),o=f.Promise,v(D,C,!1,!0),g(C)},467:function(t,e,n){"use strict";function r(t,e,n,r,i,o,a){try{var s=t[o](a),u=s.value}catch(t){return void n(t)}s.done?e(u):Promise.resolve(u).then(r,i)}function i(t){return function(){var e=this,n=arguments;return new Promise((function(i,o){var a=t.apply(e,n);function s(t){r(a,i,o,s,u,"next",t)}function u(t){r(a,i,o,s,u,"throw",t)}s(void 0)}))}}n.d(e,{A:function(){return i}})},479:function(t,e,n){"use strict";n(687)(Math,"Math",!0)},511:function(t,e,n){"use strict";var r=n(9167),i=n(9297),o=n(1951),a=n(4913).f;t.exports=function(t){var e=r.Symbol||(r.Symbol={});i(e,t)||a(e,t,{value:o.f(t)})}},533:function(t,e,n){"use strict";var r=n(9504),i=n(8014),o=n(655),a=n(2333),s=n(7750),u=r(a),c=r("".slice),l=Math.ceil,f=function(t){return function(e,n,r){var a,f,h=o(s(e)),d=i(n),p=h.length,v=void 0===r?" ":o(r);return d<=p||""===v?h:((f=u(v,l((a=d-p)/v.length))).length>a&&(f=c(f,0,a)),t?h+f:f+h)}};t.exports={start:f(!1),end:f(!0)}},537:function(t,e,n){"use strict";var r=n(550),i=n(4428),o=n(916).CONSTRUCTOR;t.exports=o||!i((function(t){r.all(t).then(void 0,(function(){}))}))},545:function(t,e,n){"use strict";function r(t){throw new TypeError('"'+t+'" is read-only')}n.d(e,{A:function(){return r}})},550:function(t,e,n){"use strict";var r=n(4576);t.exports=r.Promise},566:function(t,e,n){"use strict";var r=n(9504),i=n(9306),o=n(34),a=n(9297),s=n(7680),u=n(616),c=Function,l=r([].concat),f=r([].join),h={};t.exports=u?c.bind:function(t){var e=i(this),n=e.prototype,r=s(arguments,1),u=function(){var n=l(r,s(arguments));return this instanceof u?function(t,e,n){if(!a(h,e)){for(var r=[],i=0;i<e;i++)r[i]="a["+i+"]";h[e]=c("C,a","return new C("+f(r,",")+")")}return h[e](t,n)}(e,n.length,n):e.apply(t,n)};return o(n)&&(u.prototype=n),u}},579:function(t,e,n){var r=n(3738).default;t.exports=function(t){if(null!=t){var e=t["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],n=0;if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length))return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}}}throw new TypeError(r(t)+" is not iterable")},t.exports.__esModule=!0,t.exports.default=t.exports},597:function(t,e,n){"use strict";var r=n(9039),i=n(8227),o=n(9519),a=i("species");t.exports=function(t){return o>=51||!r((function(){var e=[];return(e.constructor={})[a]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}},600:function(t,e,n){"use strict";n.d(e,{A:function(){return i}});var r=n(7106);function i(t){return function(){return new o(t.apply(this,arguments))}}function o(t){var e,n;function i(e,n){try{var a=t[e](n),s=a.value,u=s instanceof r.A;Promise.resolve(u?s.v:s).then((function(n){if(u){var r="return"===e?"return":"next";if(!s.k||n.done)return i(r,n);n=t[r](n).value}o(a.done?"return":"normal",n)}),(function(t){i("throw",t)}))}catch(t){o("throw",t)}}function o(t,r){switch(t){case"return":e.resolve({value:r,done:!0});break;case"throw":e.reject(r);break;default:e.resolve({value:r,done:!1})}(e=e.next)?i(e.key,e.arg):n=null}this._invoke=function(t,r){return new Promise((function(o,a){var s={key:t,arg:r,resolve:o,reject:a,next:null};n?n=n.next=s:(e=n=s,i(t,r))}))},"function"!=typeof t.return&&(this.return=void 0)}o.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},o.prototype.next=function(t){return this._invoke("next",t)},o.prototype.throw=function(t){return this._invoke("throw",t)},o.prototype.return=function(t){return this._invoke("return",t)}},605:function(t,e,n){"use strict";n(6518)({target:"Math",stat:!0},{fround:n(5617)})},616:function(t,e,n){"use strict";var r=n(9039);t.exports=!r((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},655:function(t,e,n){"use strict";var r=n(6955),i=String;t.exports=function(t){if("Symbol"===r(t))throw new TypeError("Cannot convert a Symbol value to a string");return i(t)}},679:function(t,e,n){"use strict";var r=n(1625),i=TypeError;t.exports=function(t,e){if(r(e,t))return t;throw new i("Incorrect invocation")}},687:function(t,e,n){"use strict";var r=n(4913).f,i=n(9297),o=n(8227)("toStringTag");t.exports=function(t,e,n){t&&!n&&(t=t.prototype),t&&!i(t,o)&&r(t,o,{configurable:!0,value:e})}},706:function(t,e,n){"use strict";var r=n(350).PROPER,i=n(9039),o=n(7452);t.exports=function(t){return i((function(){return!!o[t]()||"​᠎"!=="​᠎"[t]()||r&&o[t].name!==t}))}},739:function(t,e,n){"use strict";var r=n(6518),i=n(9039),o=n(8981),a=n(2777);r({target:"Date",proto:!0,arity:1,forced:i((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})}))},{toJSON:function(t){var e=o(this),n=a(e,"number");return"number"!=typeof n||isFinite(n)?e.toISOString():null}})},740:function(t,e,n){"use strict";n.r(e),n.d(e,{makeFB2:function(){return _}});var r=n(5458),i=n(467),o=n(3029),a=n(2901),s=n(296),u=n(4756),c=n.n(u);function l(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return f(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var h=function(t){return t?t.replace(/[\t\n\f\r ]+/g," ").replace(/^[\t\n\f\r ]+/,"").replace(/[\t\n\f\r ]+$/,""):""},d=function(t){return h(null==t?void 0:t.textContent)},p={XLINK:"http://www.w3.org/1999/xlink",EPUB:"http://www.idpf.org/2007/ops"},v="application/xml",g="application/xhtml+xml",y={strong:["strong","self"],emphasis:["em","self"],style:["span","self"],a:"anchor",strikethrough:["s","self"],sub:["sub","self"],sup:["sup","self"],code:["code","self"],image:"image"},b={epigraph:["blockquote"],subtitle:["h2",y],"text-author":["p",y],date:["p",y],stanza:"stanza"},m={title:["header",{p:["h1",y],"empty-line":["br"]}],epigraph:["blockquote","self"],image:"image",annotation:["aside"],section:["section","self"],p:["p",y],poem:["blockquote",b],subtitle:["h2",y],cite:["blockquote","self"],"empty-line":["br"],table:["table",{tr:["tr",["align"]],th:["th",["colspan","rowspan","align","valign"]],td:["td",["colspan","rowspan","align","valign"]]}],"text-author":["p",y]};b.epigraph.push(m);var x={image:"image",title:["section",{p:["h1",y],"empty-line":["br"]}],epigraph:["section",m],section:["section",m]},w=function(t){var e=t.getAttributeNS(p.XLINK,"href"),n=e.split("#"),r=(0,s.A)(n,2)[1],i=t.getRootNode().getElementById(r);return i?"data:".concat(i.getAttribute("content-type"),";base64,").concat(i.textContent):e},k=function(){return(0,a.A)((function t(e){(0,o.A)(this,t),this.fb2=e,this.doc=document.implementation.createDocument(p.XHTML,"html")}),[{key:"image",value:function(t){var e=this.doc.createElement("img");return e.alt=t.getAttribute("alt"),e.title=t.getAttribute("title"),e.setAttribute("src",w(t)),e}},{key:"anchor",value:function(t){var e=this.convert(t,{a:["a",y]});return e.setAttribute("href",t.getAttributeNS(p.XLINK,"href")),"note"===t.getAttribute("type")&&e.setAttributeNS(p.EPUB,"epub:type","noteref"),e}},{key:"stanza",value:function(t){var e,n=this.convert(t,{stanza:["p",{title:["header",{p:["strong",y],"empty-line":["br"]}],subtitle:["p",y]}]}),r=l(t.children);try{for(r.s();!(e=r.n()).done;){var i=e.value;"v"===i.nodeName&&(n.append(this.doc.createTextNode(i.textContent)),n.append(this.doc.createElement("br")))}}catch(t){r.e(t)}finally{r.f()}return n}},{key:"convert",value:function(t,e){if(3===t.nodeType)return this.doc.createTextNode(t.textContent);if(4===t.nodeType)return this.doc.createCDATASection(t.textContent);if(8===t.nodeType)return this.doc.createComment(t.textContent);var n=null==e?void 0:e[t.nodeName];if(!n)return null;if("string"==typeof n)return this[n](t);var r=(0,s.A)(n,2),i=r[0],o=r[1],a=this.doc.createElement(i);if(t.id&&(a.id=t.id),a.classList.add(t.nodeName),Array.isArray(o)){var u,c=l(o);try{for(c.s();!(u=c.n()).done;){var f=u.value;a.setAttribute(f,t.getAttribute(f))}}catch(t){c.e(t)}finally{c.f()}}for(var h="self"===o?e:Array.isArray(o)?null:o,d=t.firstChild;d;){var p=this.convert(d,h);p&&a.append(p),d=d.nextSibling}return a}}])}(),A=function(){var t=(0,i.A)(c().mark((function t(e){var n,r,i,o,a,s,u;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,e.arrayBuffer();case 1:if(r=t.sent,i=new TextDecoder("utf-8").decode(r),o=new DOMParser,a=o.parseFromString(i,v),!(s=a.xmlEncoding||(null===(n=i.match(/^<\?xml\s+version\s*=\s*["']1.\d+"\s+encoding\s*=\s*["']([A-Za-z0-9._-]*)["']/))||void 0===n?void 0:n[1]))||"utf-8"===s.toLowerCase()){t.next=2;break}return u=new TextDecoder(s).decode(r),t.abrupt("return",o.parseFromString(u,v));case 2:return t.abrupt("return",a);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),S=URL.createObjectURL(new Blob(['\n@namespace epub "http://www.idpf.org/2007/ops";\nbody > img, section > img {\n    display: block;\n    margin: auto;\n}\n.title h1 {\n    text-align: center;\n}\nbody > section > .title, body.notesBodyType > .title {\n    margin: 3em 0;\n}\nbody.notesBodyType > section .title h1 {\n    text-align: start;\n}\nbody.notesBodyType > section .title {\n    margin: 1em 0;\n}\np {\n    text-indent: 1em;\n    margin: 0;\n}\n:not(p) + p, p:first-child {\n    text-indent: 0;\n}\n.poem p {\n    text-indent: 0;\n    margin: 1em 0;\n}\n.text-author, .date {\n    text-align: end;\n}\n.text-author:before {\n    content: "—";\n}\ntable {\n    border-collapse: collapse;\n}\ntd, th {\n    padding: .25em;\n}\na[epub|type~="noteref"] {\n    font-size: .75em;\n    vertical-align: super;\n}\nbody:not(.notesBodyType) > .title, body:not(.notesBodyType) > .epigraph {\n    margin: 3em 0;\n}\n'],{type:"text/css"})),E="data-foliate-id",_=function(){var t=(0,i.A)(c().mark((function t(e){var n,i,o,a,u,f,p,v,y,b,_,T,O;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n={},t.next=1,A(e);case 1:return i=t.sent,o=new k(i),u=function(t){return(0,r.A)(i.querySelectorAll(t))},f=function(t){var e=d(t.querySelector("nickname"));if(e)return e;var n=d(t.querySelector("first-name")),r=d(t.querySelector("middle-name")),i=d(t.querySelector("last-name"));return{name:[n,r,i].filter((function(t){return t})).join(" "),sortAs:i?[i,[n,r].filter((function(t){return t})).join(" ")].join(", "):null}},p=function(t){var e;return null!==(e=null==t?void 0:t.getAttribute("value"))&&void 0!==e?e:d(t)},v=(a=function(t){return i.querySelector(t)})("title-info annotation"),n.metadata={title:d(a("title-info book-title")),identifier:d(a("document-info id")),language:d(a("title-info lang")),author:u("title-info author").map(f),translator:u("title-info translator").map(f),producer:u("document-info author").map(f).concat(u("document-info program-used").map(d)),publisher:d(a("publish-info publisher")),published:p(a("title-info date")),modified:p(a("document-info date")),description:v?o.convert(v,{annotation:["div",m]}).innerHTML:null,subject:u("title-info genre").map(d)},a("coverpage image")?(y=w(a("coverpage image")),n.getCover=function(){return fetch(y).then((function(t){return t.blob()}))}):n.getCover=function(){return null},b=Array.from(i.querySelectorAll("body"),(function(t){var e=o.convert(t,{body:["body",x]});return[Array.from(e.children,(function(t){var e=[t].concat((0,r.A)(t.querySelectorAll("[id]"))).map((function(t){return t.id}));return{el:t,ids:e}})),e]})),_=[],T=b[0][0].map((function(t){var e=t.el;return{ids:t.ids,titles:Array.from(e.querySelectorAll(":scope > section > .title"),(function(t,e){return t.setAttribute(E,e),{title:d(t),index:e}})),el:e}})).concat(b.slice(1).map((function(t){var e=(0,s.A)(t,2),n=e[0],r=e[1],i=n.map((function(t){return t.ids})).flat();return r.classList.add("notesBodyType"),{ids:i,el:r,linear:"no"}}))).map((function(t){var e,n,r,i=t.ids,o=t.titles,a=t.el,s=t.linear,u=(r=a.outerHTML,'<?xml version="1.0" encoding="utf-8"?>\n<html xmlns="http://www.w3.org/1999/xhtml">\n    <head><link href="'.concat(S,'" rel="stylesheet" type="text/css"/></head>\n    <body>').concat(r,"</body>\n</html>")),c=new Blob([u],{type:g}),l=URL.createObjectURL(c);return _.push(l),{ids:i,title:h(null!==(e=null===(n=a.querySelector(".title, .subtitle, p"))||void 0===n?void 0:n.textContent)&&void 0!==e?e:a.classList.contains("title")?a.textContent:""),titles:o,load:function(){return l},createDocument:function(){return(new DOMParser).parseFromString(u,g)},size:c.size-Array.from(a.querySelectorAll("[src]"),(function(t){var e,n;return null!==(e=null===(n=t.getAttribute("src"))||void 0===n?void 0:n.length)&&void 0!==e?e:0})).reduce((function(t,e){return t+e}),0),linear:s}})),O=new Map,n.sections=T.map((function(t,e){var n,r=t.ids,i=t.load,o=t.createDocument,a=t.size,s=t.linear,u=l(r);try{for(u.s();!(n=u.n()).done;){var c=n.value;c&&O.set(c,e)}}catch(t){u.e(t)}finally{u.f()}return{id:e,load:i,createDocument:o,size:a,linear:s}})),n.toc=T.map((function(t,e){var n=t.title,r=t.titles,i=e.toString();return{label:n,href:i,subitems:null!=r&&r.length?r.map((function(t){var e=t.title,n=t.index;return{label:e,href:"".concat(i,"#").concat(n)}})):null}})).filter((function(t){return t})),n.resolveHref=function(t){var e=t.split("#"),n=(0,s.A)(e,2),r=n[0],i=n[1];return r?{index:Number(r),anchor:function(t){return t.querySelector("[".concat(E,'="').concat(i,'"]'))}}:{index:O.get(i),anchor:function(t){return t.getElementById(i)}}},n.splitTOCHref=function(t){var e,n;return null!==(e=null==t||null===(n=t.split("#"))||void 0===n?void 0:n.map((function(t){return Number(t)})))&&void 0!==e?e:[]},n.getTOCFragment=function(t,e){return t.querySelector("[".concat(E,'="').concat(e,'"]'))},n.destroy=function(){for(var t=0,e=_;t<e.length;t++){var n=e[t];URL.revokeObjectURL(n)}},t.abrupt("return",n);case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()},741:function(t){"use strict";var e=Math.ceil,n=Math.floor;t.exports=Math.trunc||function(t){var r=+t;return(r>0?n:e)(r)}},744:function(t,e,n){"use strict";var r=n(9565),i=n(9504),o=n(9228),a=n(8551),s=n(34),u=n(7750),c=n(2293),l=n(7829),f=n(8014),h=n(655),d=n(5966),p=n(6682),v=n(8429),g=n(9039),y=v.UNSUPPORTED_Y,b=Math.min,m=i([].push),x=i("".slice),w=!g((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),k="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;o("split",(function(t,e,n){var i="0".split(void 0,0).length?function(t,n){return void 0===t&&0===n?[]:r(e,this,t,n)}:e;return[function(e,n){var o=u(this),a=s(e)?d(e,t):void 0;return a?r(a,e,o,n):r(i,h(o),e,n)},function(t,r){var o=a(this),s=h(t);if(!k){var u=n(i,o,s,r,i!==e);if(u.done)return u.value}var d=c(o,RegExp),v=o.unicode,g=(o.ignoreCase?"i":"")+(o.multiline?"m":"")+(o.unicode?"u":"")+(y?"g":"y"),w=new d(y?"^(?:"+o.source+")":o,g),A=void 0===r?4294967295:r>>>0;if(0===A)return[];if(0===s.length)return null===p(w,s)?[s]:[];for(var S=0,E=0,_=[];E<s.length;){w.lastIndex=y?0:E;var T,O=p(w,y?x(s,E):s);if(null===O||(T=b(f(w.lastIndex+(y?E:0)),s.length))===S)E=l(s,E,v);else{if(m(_,x(s,S,E)),_.length===A)return _;for(var M=1;M<=O.length-1;M++)if(m(_,O[M]),_.length===A)return _;E=S=T}}return m(_,x(s,S)),_}]}),k||!w,y)},757:function(t,e,n){"use strict";var r=n(7751),i=n(4901),o=n(1625),a=n(7040),s=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=r("Symbol");return i(e)&&o(e.prototype,s(t))}},761:function(t,e,n){"use strict";n(6518)({target:"Math",stat:!0},{trunc:n(741)})},788:function(t,e,n){"use strict";var r=n(34),i=n(2195),o=n(8227)("match");t.exports=function(t){var e;return r(t)&&(void 0!==(e=t[o])?!!e:"RegExp"===i(t))}},816:function(t,e,n){"use strict";n.d(e,{A:function(){return i}});var r=n(2284);function i(t){var e=function(t){if("object"!=(0,r.A)(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=(0,r.A)(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==(0,r.A)(e)?e:e+""}},823:function(t,e,n){"use strict";n.r(e),n.d(e,{FixedLayout:function(){return I}});var r=n(467),i=n(3029),o=n(2901),a=n(6822),s=n(3954),u=n(5501),c=n(8614),l=n(4467),f=n(296),h=n(4756),d=n.n(h);function p(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(p=function(){return!!t})()}function v(t,e,n){g(t,e),e.set(t,n)}function g(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function y(t,e,n){return t.set(m(t,e),n),n}function b(t,e){return t.get(m(t,e))}function m(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}var x=function(t){var e;return null==t||null===(e=t.split(/[,;\s]/))||void 0===e||null===(e=e.filter((function(t){return t})))||void 0===e?void 0:e.map((function(t){return t.split("=").map((function(t){return t.trim()}))}))},w=function(t,e){var n;if("svg"===t.documentElement.localName){var r,i,o=null!==(r=null===(i=t.documentElement.getAttribute("viewBox"))||void 0===i?void 0:i.split(/\s/))&&void 0!==r?r:[],a=(0,f.A)(o,4);return{width:a[2],height:a[3]}}var s=x(null===(n=t.querySelector('meta[name="viewport"]'))||void 0===n?void 0:n.getAttribute("content"));if(s)return Object.fromEntries(s);if("string"==typeof e)return x(e);if(e)return e;var u=t.querySelector("img");return u?{width:u.naturalWidth,height:u.naturalHeight}:(console.warn(new Error("Missing viewport properties")),{width:1e3,height:2e3})},k=new WeakMap,A=new WeakMap,S=new WeakMap,E=new WeakMap,_=new WeakMap,T=new WeakMap,O=new WeakMap,M=new WeakMap,L=new WeakMap,C=new WeakSet,I=function(t){function e(){var t,n,r,o;(0,i.A)(this,e),n=this,r=e,r=(0,s.A)(r),function(t,e){g(t,e),e.add(t)}(t=(0,a.A)(n,p()?Reflect.construct(r,[],(0,s.A)(n).constructor):r.apply(n,o)),C),v(t,k,t.attachShadow({mode:"closed"})),v(t,A,new ResizeObserver((function(){return m(C,t,P).call(t)}))),v(t,S,void 0),v(t,E,-1),(0,l.A)(t,"defaultViewport",void 0),(0,l.A)(t,"spread",void 0),v(t,_,!1),v(t,T,void 0),v(t,O,void 0),v(t,M,void 0),v(t,L,void 0);var u=new CSSStyleSheet;return b(k,t).adoptedStyleSheets=[u],u.replaceSync(":host {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n        }"),b(A,t).observe(t),t}return(0,u.A)(e,t),(0,o.A)(e,[{key:"open",value:function(t){this.book=t;var e=t.rendition;this.spread=null==e?void 0:e.spread,this.defaultViewport=null==e?void 0:e.viewport;var n="rtl"===t.dir,r=!n;this.rtl=n,"none"===(null==e?void 0:e.spread)?y(S,this,t.sections.map((function(t){return{center:t}}))):y(S,this,t.sections.reduce((function(t,e){var i=t[t.length-1],o=e.linear,a=e.pageSpread;if("no"===o)return t;var s=function(){var e={};return t.push(e),e};return"center"===a?(i.left||i.right?s():i).center=e:"left"===a?(i.center||i.left||r?s():i).left=e:"right"===a?(i.center||i.right||n?s():i).right=e:r?i.center||i.right?s().left=e:i.left?i.right=e:i.left=e:i.center||i.left?s().right=e:i.right?i.left=e:i.right=e,t}),[{}]))}},{key:"index",get:function(){var t,e,n,r=b(S,this)[b(E,this)],i=null!==(t=null==r?void 0:r.center)&&void 0!==t?t:"left"===this.side?null!==(e=r.left)&&void 0!==e?e:r.right:null!==(n=r.right)&&void 0!==n?n:r.left;return this.book.sections.indexOf(i)}},{key:"getSpreadOf",value:function(t){for(var e=b(S,this),n=0;n<e.length;n++){var r=e[n],i=r.left,o=r.right,a=r.center;if(i===t)return{index:n,side:"left"};if(o===t)return{index:n,side:"right"};if(a===t)return{index:n,side:"center"}}}},{key:"goToSpread",value:(x=(0,r.A)(d().mark((function t(e,n,r){var i,o,a,s,u,c,l,f,h,p,v,g,x,w,k;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(e<0||e>b(S,this).length-1)){t.next=1;break}return t.abrupt("return");case 1:if(e!==b(E,this)){t.next=2;break}return m(C,this,P).call(this,n),t.abrupt("return");case 2:if(y(E,this,e),!(i=b(S,this)[e]).center){t.next=5;break}return s=this.book.sections.indexOf(i.center),t.next=3,null===(o=i.center)||void 0===o||null===(a=o.load)||void 0===a?void 0:a.call(o);case 3:return u=t.sent,t.next=4,m(C,this,F).call(this,{center:{index:s,src:u}});case 4:t.next=8;break;case 5:return p=this.book.sections.indexOf(i.left),v=this.book.sections.indexOf(i.right),t.next=6,null===(c=i.left)||void 0===c||null===(l=c.load)||void 0===l?void 0:l.call(c);case 6:return g=t.sent,t.next=7,null===(f=i.right)||void 0===f||null===(h=f.load)||void 0===h?void 0:h.call(f);case 7:return x=t.sent,w={index:p,src:g},k={index:v,src:x},t.next=8,m(C,this,F).call(this,{left:w,right:k,side:n});case 8:m(C,this,B).call(this,r);case 9:case"end":return t.stop()}}),t,this)}))),function(t,e,n){return x.apply(this,arguments)})},{key:"select",value:(h=(0,r.A)(d().mark((function t(e){return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,this.goTo(e);case 1:case"end":return t.stop()}}),t,this)}))),function(t){return h.apply(this,arguments)})},{key:"goTo",value:(f=(0,r.A)(d().mark((function t(e){var n,r,i,o,a,s;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=this.book,t.next=1,e;case 1:if(r=t.sent,i=n.sections[r.index]){t.next=2;break}return t.abrupt("return");case 2:return o=this.getSpreadOf(i),a=o.index,s=o.side,t.next=3,this.goToSpread(a,s);case 3:case"end":return t.stop()}}),t,this)}))),function(t){return f.apply(this,arguments)})},{key:"next",value:(c=(0,r.A)(d().mark((function t(){return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(this.rtl?m(C,this,D).call(this):m(C,this,z).call(this))){t.next=1;break}m(C,this,B).call(this,"page"),t.next=2;break;case 1:return t.abrupt("return",this.goToSpread(b(E,this)+1,this.rtl?"right":"left","page"));case 2:case"end":return t.stop()}}),t,this)}))),function(){return c.apply(this,arguments)})},{key:"prev",value:(n=(0,r.A)(d().mark((function t(){return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(this.rtl?m(C,this,z).call(this):m(C,this,D).call(this))){t.next=1;break}m(C,this,B).call(this,"page"),t.next=2;break;case 1:return t.abrupt("return",this.goToSpread(b(E,this)-1,this.rtl?"left":"right","page"));case 2:case"end":return t.stop()}}),t,this)}))),function(){return n.apply(this,arguments)})},{key:"getContents",value:function(){return Array.from(b(k,this).querySelectorAll("iframe"),(function(t){return{doc:t.contentDocument}}))}},{key:"destroy",value:function(){b(A,this).unobserve(this)}}]);var n,c,f,h,x}((0,c.A)(HTMLElement));function R(t,e){return j.apply(this,arguments)}function j(){return(j=(0,r.A)(d().mark((function t(e,n){var r,i,o,a,s=this;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=n.index,i=n.src,o=document.createElement("div"),a=document.createElement("iframe"),o.append(a),Object.assign(a.style,{border:"0",display:"none",overflow:"hidden"}),a.setAttribute("sandbox","allow-same-origin allow-scripts"),a.setAttribute("scrolling","no"),a.setAttribute("part","filter"),b(k,this).append(o),i){t.next=1;break}return t.abrupt("return",{blank:!0,element:o,iframe:a});case 1:return t.abrupt("return",new Promise((function(t){var n=function(){a.removeEventListener("load",n);var i=a.contentDocument;i.position=e,s.dispatchEvent(new CustomEvent("load",{detail:{doc:i,index:r}}));var u=w(i,s.defaultViewport),c=u.width,l=u.height;t({element:o,iframe:a,width:parseFloat(c),height:parseFloat(l)})};a.addEventListener("load",n),a.src=i})));case 2:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}function P(){var t,e,n,r,i,o,a,s,u,c,l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:b(L,this);if(l){var f=null!==(t=b(T,this))&&void 0!==t?t:{},h=null!==(e=b(M,this))&&void 0!==e?e:b(O,this),d="left"===l?f:h,p=this.getBoundingClientRect(),v=p.width,g=p.height,m="both"!==this.spread&&"portrait"!==this.spread&&g>v;y(_,this,m);var x=null!==(n=f.width)&&void 0!==n?n:h.width,w=null!==(r=f.height)&&void 0!==r?r:h.height,k=m||b(M,this)?Math.min(v/(null!==(i=d.width)&&void 0!==i?i:x),g/(null!==(o=d.height)&&void 0!==o?o:w)):Math.min(v/((null!==(a=f.width)&&void 0!==a?a:x)+(null!==(s=h.width)&&void 0!==s?s:x)),g/Math.max(null!==(u=f.height)&&void 0!==u?u:w,null!==(c=h.height)&&void 0!==c?c:w)),A=function(t){var e=t.element,n=t.iframe,r=t.width,i=t.height,o=t.blank;n.contentDocument.scale=k,Object.assign(n.style,{width:"".concat(r,"px"),height:"".concat(i,"px"),transform:"scale(".concat(k,")"),transformOrigin:"top left",display:o?"none":"block"}),Object.assign(e.style,{width:"".concat((null!=r?r:x)*k,"px"),height:"".concat((null!=i?i:w)*k,"px"),overflow:"hidden",display:"block"}),m&&t!==d&&(e.style.display="none")};b(M,this)?A(b(M,this)):(A(f),A(h))}}function F(t){return N.apply(this,arguments)}function N(){return(N=(0,r.A)(d().mark((function t(e){var n,r,i,o,a,s,u,c,l,f,h,p,v,g,x,w;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.left,r=e.right,i=e.center,o=e.side,b(k,this).replaceChildren(),y(T,this,null),y(O,this,null),y(M,this,null),!i){t.next=2;break}return a=y,s=M,u=this,t.next=1,m(C,this,R).call(this,"center",i);case 1:c=t.sent,a(s,u,c),y(L,this,"center"),m(C,this,P).call(this),t.next=5;break;case 2:return l=y,f=T,h=this,t.next=3,m(C,this,R).call(this,"left",n);case 3:return p=t.sent,l(f,h,p),v=y,g=O,x=this,t.next=4,m(C,this,R).call(this,"right",r);case 4:w=t.sent,v(g,x,w),y(L,this,b(T,this).blank?"right":b(O,this).blank?"left":o),m(C,this,P).call(this);case 5:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}function D(){var t,e;if(!(b(M,this)||null!==(t=b(T,this))&&void 0!==t&&t.blank))return b(_,this)&&"none"===(null===(e=b(T,this))||void 0===e||null===(e=e.element)||void 0===e||null===(e=e.style)||void 0===e?void 0:e.display)?(b(O,this).element.style.display="none",b(T,this).element.style.display="block",y(L,this,"left"),!0):void 0}function z(){var t,e;if(!(b(M,this)||null!==(t=b(O,this))&&void 0!==t&&t.blank))return b(_,this)&&"none"===(null===(e=b(O,this))||void 0===e||null===(e=e.element)||void 0===e||null===(e=e.style)||void 0===e?void 0:e.display)?(b(T,this).element.style.display="none",b(O,this).element.style.display="block",y(L,this,"right"),!0):void 0}function B(t){this.dispatchEvent(new CustomEvent("relocate",{detail:{reason:t,range:null,index:this.index,fraction:0,size:1}}))}customElements.define("foliate-fxl",I)},825:function(t,e,n){"use strict";var r=n(6518),i=n(7751),o=n(8745),a=n(566),s=n(5548),u=n(8551),c=n(34),l=n(2360),f=n(9039),h=i("Reflect","construct"),d=Object.prototype,p=[].push,v=f((function(){function t(){}return!(h((function(){}),[],t)instanceof t)})),g=!f((function(){h((function(){}))})),y=v||g;r({target:"Reflect",stat:!0,forced:y,sham:y},{construct:function(t,e){s(t),u(e);var n=arguments.length<3?t:s(arguments[2]);if(g&&!v)return h(t,e,n);if(t===n){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var r=[null];return o(p,r,e),new(o(a,t,r))}var i=n.prototype,f=l(c(i)?i:d),y=o(t,f,e);return c(y)?y:f}})},851:function(t,e,n){"use strict";var r=n(6955),i=n(5966),o=n(4117),a=n(6269),s=n(8227)("iterator");t.exports=function(t){if(!o(t))return i(t,s)||i(t,"@@iterator")||a[r(t)]}},875:function(t,e,n){"use strict";var r=n(6518),i=n(9039),o=n(8981),a=n(2787),s=n(2211);r({target:"Object",stat:!0,forced:i((function(){a(1)})),sham:!s},{getPrototypeOf:function(t){return a(o(t))}})},887:function(t,e,n){var r=n(6993),i=n(1791);t.exports=function(t,e,n,o,a){return new i(r().w(t,e,n,o),a||Promise)},t.exports.__esModule=!0,t.exports.default=t.exports},888:function(t,e,n){"use strict";var r=n(6518),i=n(9565),o=n(34),a=n(8551),s=n(6575),u=n(7347),c=n(2787);r({target:"Reflect",stat:!0},{get:function t(e,n){var r,l,f=arguments.length<3?e:arguments[2];return a(e)===f?e[n]:(r=u.f(e,n))?s(r)?r.value:void 0===r.get?void 0:i(r.get,f):o(l=c(e))?t(l,n,f):void 0}})},916:function(t,e,n){"use strict";var r=n(4576),i=n(550),o=n(4901),a=n(2796),s=n(3706),u=n(8227),c=n(4215),l=n(6395),f=n(9519),h=i&&i.prototype,d=u("species"),p=!1,v=o(r.PromiseRejectionEvent),g=a("Promise",(function(){var t=s(i),e=t!==String(i);if(!e&&66===f)return!0;if(l&&(!h.catch||!h.finally))return!0;if(!f||f<51||!/native code/.test(t)){var n=new i((function(t){t(1)})),r=function(t){t((function(){}),(function(){}))};if((n.constructor={})[d]=r,!(p=n.then((function(){}))instanceof r))return!0}return!(e||"BROWSER"!==c&&"DENO"!==c||v)}));t.exports={CONSTRUCTOR:g,REJECTION_EVENT:v,SUBCLASSING:p}},926:function(t,e,n){"use strict";var r=n(9306),i=n(8981),o=n(7055),a=n(6198),s=TypeError,u="Reduce of empty array with no initial value",c=function(t){return function(e,n,c,l){var f=i(e),h=o(f),d=a(f);if(r(n),0===d&&c<2)throw new s(u);var p=t?d-1:0,v=t?-1:1;if(c<2)for(;;){if(p in h){l=h[p],p+=v;break}if(p+=v,t?p<0:d<=p)throw new s(u)}for(;t?p>=0:d>p;p+=v)p in h&&(l=n(l,h[p],p,f));return l}};t.exports={left:c(!1),right:c(!0)}},958:function(t,e,n){"use strict";n(5240)},985:function(t,e,n){"use strict";var r=n(6518),i=n(7751),o=n(8551);r({target:"Reflect",stat:!0,sham:!n(2744)},{preventExtensions:function(t){o(t);try{var e=i("Object","preventExtensions");return e&&e(t),!0}catch(t){return!1}}})},991:function(t,e,n){"use strict";n.d(e,{A:function(){return i}});var r=n(3954);function i(){return i="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var i=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=(0,r.A)(t)););return t}(t,e);if(i){var o=Object.getOwnPropertyDescriptor(i,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},i.apply(null,arguments)}},1034:function(t,e,n){"use strict";var r=n(9565),i=n(9297),o=n(1625),a=n(5213),s=n(7979),u=RegExp.prototype;t.exports=a.correct?function(t){return t.flags}:function(t){return a.correct||!o(u,t)||i(t,"flags")?t.flags:r(s,t)}},1051:function(t,e,n){"use strict";var r=n(6518),i=n(9039),o=n(3517),a=n(4659),s=Array;r({target:"Array",stat:!0,forced:i((function(){function t(){}return!(s.of.call(t)instanceof t)}))},{of:function(){for(var t=0,e=arguments.length,n=new(o(this)?this:s)(e);e>t;)a(n,t,arguments[t++]);return n.length=e,n}})},1056:function(t,e,n){"use strict";var r=n(4913).f;t.exports=function(t,e,n){n in t||r(t,n,{configurable:!0,get:function(){return e[n]},set:function(t){e[n]=t}})}},1072:function(t,e,n){"use strict";var r=n(1828),i=n(8727);t.exports=Object.keys||function(t){return r(t,i)}},1073:function(t,e,n){"use strict";n(511)("split")},1088:function(t,e,n){"use strict";var r=n(6518),i=n(9565),o=n(6395),a=n(350),s=n(4901),u=n(3994),c=n(2787),l=n(2967),f=n(687),h=n(6699),d=n(6840),p=n(8227),v=n(6269),g=n(7657),y=a.PROPER,b=a.CONFIGURABLE,m=g.IteratorPrototype,x=g.BUGGY_SAFARI_ITERATORS,w=p("iterator"),k="keys",A="values",S="entries",E=function(){return this};t.exports=function(t,e,n,a,p,g,_){u(n,e,a);var T,O,M,L=function(t){if(t===p&&P)return P;if(!x&&t&&t in R)return R[t];switch(t){case k:case A:case S:return function(){return new n(this,t)}}return function(){return new n(this)}},C=e+" Iterator",I=!1,R=t.prototype,j=R[w]||R["@@iterator"]||p&&R[p],P=!x&&j||L(p),F="Array"===e&&R.entries||j;if(F&&(T=c(F.call(new t)))!==Object.prototype&&T.next&&(o||c(T)===m||(l?l(T,m):s(T[w])||d(T,w,E)),f(T,C,!0,!0),o&&(v[C]=E)),y&&p===A&&j&&j.name!==A&&(!o&&b?h(R,"name",A):(I=!0,P=function(){return i(j,this)})),p)if(O={values:L(A),keys:g?P:L(k),entries:L(S)},_)for(M in O)(x||I||!(M in R))&&d(R,M,O[M]);else r({target:e,proto:!0,forced:x||I},O);return o&&!_||R[w]===P||d(R,w,P,{name:p}),v[e]=P,O}},1103:function(t){"use strict";t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},1108:function(t,e,n){"use strict";var r=n(6955);t.exports=function(t){var e=r(t);return"BigInt64Array"===e||"BigUint64Array"===e}},1137:function(t,e,n){"use strict";n(6518)({target:"Reflect",stat:!0},{ownKeys:n(5031)})},1181:function(t,e,n){"use strict";var r,i,o,a=n(8622),s=n(4576),u=n(34),c=n(6699),l=n(9297),f=n(7629),h=n(6119),d=n(421),p="Object already initialized",v=s.TypeError,g=s.WeakMap;if(a||f.state){var y=f.state||(f.state=new g);y.get=y.get,y.has=y.has,y.set=y.set,r=function(t,e){if(y.has(t))throw new v(p);return e.facade=t,y.set(t,e),e},i=function(t){return y.get(t)||{}},o=function(t){return y.has(t)}}else{var b=h("state");d[b]=!0,r=function(t,e){if(l(t,b))throw new v(p);return e.facade=t,c(t,b,e),e},i=function(t){return l(t,b)?t[b]:{}},o=function(t){return l(t,b)}}t.exports={set:r,get:i,has:o,enforce:function(t){return o(t)?i(t):r(t,{})},getterFor:function(t){return function(e){var n;if(!u(e)||(n=i(e)).type!==t)throw new v("Incompatible receiver, "+t+" required");return n}}}},1211:function(t,e,n){"use strict";var r=n(6518),i=n(8551),o=n(7347).f;r({target:"Reflect",stat:!0},{deleteProperty:function(t,e){var n=o(i(t),e);return!(n&&!n.configurable)&&delete t[e]}})},1240:function(t,e,n){"use strict";var r=n(9504);t.exports=r(1.1.valueOf)},1278:function(t,e,n){"use strict";var r=n(6518),i=n(3724),o=n(5031),a=n(5397),s=n(7347),u=n(4659);r({target:"Object",stat:!0,sham:!i},{getOwnPropertyDescriptors:function(t){for(var e,n,r=a(t),i=s.f,c=o(r),l={},f=0;c.length>f;)void 0!==(n=i(r,e=c[f++]))&&u(l,e,n);return l}})},1291:function(t,e,n){"use strict";var r=n(741);t.exports=function(t){var e=+t;return e!=e||0===e?0:r(e)}},1296:function(t,e,n){"use strict";var r=n(4495);t.exports=r&&!!Symbol.for&&!!Symbol.keyFor},1330:function(t,e,n){"use strict";n.r(e),n.d(e,{EPUB:function(){return jt}});var r=n(3029),i=n(2901),o=n(6822),a=n(3954),s=n(5501),u=n(8614),c=n(296),l=n(4467),f=n(467),h=n(5458),d=n(4756),p=n.n(d),v=n(2248);function g(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(g=function(){return!!t})()}function y(t,e){m(t,e),e.add(t)}function b(t,e,n){m(t,e),e.set(t,n)}function m(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function x(t,e,n){return n(A(t,e))}function w(t,e,n){return t.set(A(t,e),n),n}function k(t,e){return t.get(A(t,e))}function A(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}function S(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return E(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?E(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function E(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function _(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function T(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?_(Object(n),!0).forEach((function(e){(0,l.A)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var O="http://www.idpf.org/2007/opf",M="http://www.idpf.org/2007/ops",L="http://purl.org/dc/elements/1.1/",C="http://www.w3.org/2001/04/xmlenc#",I="http://www.w3.org/1999/xlink",R={XML:"application/xml",NCX:"application/x-dtbncx+xml",XHTML:"application/xhtml+xml",HTML:"text/html",CSS:"text/css",SVG:"image/svg+xml",JS:/\/(x-)?(javascript|ecmascript)/},j=function(t){return t.toLowerCase().replace(/[-:](.)/g,(function(t,e){return e.toUpperCase()}))},P=function(t,e,n){return n?function(n){var r;return null===(r=n.getAttribute(t))||void 0===r||null===(r=r.split(/\s/))||void 0===r?void 0:r.includes(e)}:"function"==typeof e?function(n){return e(n.getAttribute(t))}:function(n){return n.getAttribute(t)===e}},F=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t){return t?Object.fromEntries(e.map((function(e){return[j(e),t.getAttribute(e)]}))):null}},N=function(t){return(e=null==t?void 0:t.textContent)?e.replace(/[\t\n\f\r ]+/g," ").replace(/^[\t\n\f\r ]+/,"").replace(/[\t\n\f\r ]+$/,""):"";var e},D=function(t,e){var n=t.lookupNamespaceURI(null)===e||t.lookupPrefix(e),r=n?function(t,n){return function(t){return t.namespaceURI===e&&t.localName===n}}:function(t,e){return function(t){return t.localName===e}};return{$:function(t,e){return(0,h.A)(t.children).find(r(t,e))},$$:function(t,e){return(0,h.A)(t.children).filter(r(t,e))},$$$:n?function(t,n){return(0,h.A)(t.getElementsByTagNameNS(e,n))}:function(t,e){return(0,h.A)(t.getElementsByTagName(e))}}},z=function(t,e){try{if(e.includes(":"))return new URL(t,e);var n="https://invalid.invalid/",r=new URL(t,n+e);return r.search="",decodeURI(r.href.replace(n,""))}catch(e){return console.warn(e),t}},B=function(t){return/^(?!blob)\w+:/i.test(t)},U=function(){var t=(0,f.A)(p().mark((function t(e,n,r){var i,o,a,s,u,c,l;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=[],e.replace(n,(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return i.push(e),null})),o=[],a=0,s=i;case 1:if(!(a<s.length)){t.next=4;break}return u=s[a],c=o,t.next=2,r.apply(void 0,(0,h.A)(u));case 2:l=t.sent,c.push.call(c,l);case 3:a++,t.next=1;break;case 4:return t.abrupt("return",e.replace(n,(function(){return o.shift()})));case 5:case"end":return t.stop()}}),t)})));return function(e,n,r){return t.apply(this,arguments)}}(),W=function(t){return t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")},H={attrs:["dir","xml:lang"]},q=T(T({name:"alternate-script",many:!0},H),{},{props:["file-as"]}),V=T(T({many:!0},H),{},{props:[{name:"role",many:!0,attrs:["scheme"]},"file-as",q],setLegacyAttrs:function(t,e){var n,r;if(null===(n=t.role)||void 0===n||!n.length){var i=e.getAttributeNS(O,"role");i&&(t.role=[{value:i}])}null!==(r=t.fileAs)&&void 0!==r||(t.fileAs=e.getAttributeNS(O,"file-as"))}}),$=[T(T({name:"title",many:!0},H),{},{props:["title-type","display-seq","file-as",q]}),{name:"identifier",many:!0,props:[{name:"identifier-type",attrs:["scheme"]}],setLegacyAttrs:function(t,e){if(!t.identifierType){var n=e.getAttributeNS(O,"scheme");n&&(t.identifierType={value:n})}}},{name:"language",many:!0},T({name:"creator"},V),T({name:"contributor"},V),T(T({name:"publisher"},H),{},{props:["file-as",q]}),T(T({name:"description"},H),{},{props:[q]}),T(T({name:"rights"},H),{},{props:[q]}),{name:"date"},{name:"dcterms:modified",type:"meta"},T(T({name:"subject",many:!0},H),{},{props:["term","authority",q],setLegacyAttrs:function(t,e){var n,r;null!==(n=t.term)&&void 0!==n||(t.term=e.getAttributeNS(O,"term")),null!==(r=t.authority)&&void 0!==r||(t.authority=e.getAttributeNS(O,"authority"))}}),{name:"source",many:!0},T(T({name:"belongs-to-collection",type:"meta",many:!0},H),{},{props:["collection-type","group-position","dcterms:identifier","file-as",q,{name:"belongs-to-collection",recursive:!0}]})],G=function(t){var e,n=D(t,O),r=n.$,i=n.$$,o=r(t.documentElement,"metadata"),a=Array.from(o.children),s=function(t,e){var n;if(!e)return null;var r=t.props,i=void 0===r?[]:r,o=t.attrs,u=void 0===o?[]:o,c=N(e);if(!i.length&&!u.length)return c;var l=e.getAttribute("id"),f=l?a.filter(P("refines","#"+l)):[],h=Object.fromEntries([["value",c]].concat(i.map((function(e){var n=e.many,r=e.recursive,i="string"==typeof e?e:e.name,o=P("property",i),a=r?t:e;return[j(i),n?f.filter(o).map((function(t){return s(a,t)})):s(a,f.find(o))]}))).concat(u.map((function(t){return[j(t),e.getAttribute(t)]}))));return null===(n=t.setLegacyAttrs)||void 0===n||n.call(t,h,e),h},u=a.filter(P("refines",null)),l=Object.fromEntries($.map((function(t){var e=t.type,n=t.name,r=t.many,i="meta"===e?function(t){return t.namespaceURI===O&&t.getAttribute("property")===n}:function(t){return t.namespaceURI===L&&t.localName===n};return[j(n),r?u.filter(i).map((function(e){return s(t,e)})):s(t,u.find(i))]}))),f=i(o,"meta"),h=function(t){return f.filter(P("property",(function(e){return null==e?void 0:e.startsWith(t)}))).map((function(e){return[e.getAttribute("property").replace(t,""),e]}))},d=Object.fromEntries(h("rendition:").map((function(t){var e=(0,c.A)(t,2),n=e[0],r=e[1];return[n,N(r)]}))),p={narrator:[],duration:{}},v=S(h("media:"));try{for(v.s();!(e=v.n()).done;){var g,y,b=(0,c.A)(e.value,2),m=b[0],x=b[1],w=N(x);"duration"===m?p.duration[null!==(g=null===(y=x.getAttribute("refines"))||void 0===y||null===(y=y.split("#"))||void 0===y?void 0:y[1])&&void 0!==g?g:""]=K(w):"active-class"===m?p.activeClass=w:"narrator"===m?p.narrator.push(w):"playback-active-class"===m&&(p.playbackActiveClass=w)}}catch(t){v.e(t)}finally{v.f()}return{metadata:l,rendition:d,media:p}},X=function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(t){return t},r=D(t,"http://www.w3.org/1999/xhtml"),i=r.$,o=r.$$,a=r.$$$,s=function(t,e){return t?o(t,"li").map(function(t){return function(e){var r,o,a=null!==(r=i(e,"a"))&&void 0!==r?r:i(e,"span"),u=i(e,"ol"),c=function(t){return t?decodeURI(n(t)):null}(null==a?void 0:a.getAttribute("href")),l={label:N(a)||(null==a?void 0:a.getAttribute("title")),href:c,subitems:s(u)};return t&&(l.type=null==a||null===(o=a.getAttributeNS(M,"type"))||void 0===o?void 0:o.split(/\s/)),l}}(e)):null},u=function(t,e){return s(i(t,"ol"),e)},c=null,l=null,f=null,h=[],d=S(a(t,"nav"));try{for(d.s();!(e=d.n()).done;){var p,v,g=e.value,y=null!==(p=null===(v=g.getAttributeNS(M,"type"))||void 0===v?void 0:v.split(/\s/))&&void 0!==p?p:[];y.includes("toc")?null!=c||(c=u(g)):y.includes("page-list")?null!=l||(l=u(g)):y.includes("landmarks")?null!=f||(f=u(g,!0)):h.push({label:N(g.firstElementChild),type:y,list:u(g)})}}catch(t){d.e(t)}finally{d.f()}return{toc:c,pageList:l,landmarks:f,others:h}},Y=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(t){return t},n=D(t,"http://www.daisy.org/z3986/2005/ncx/"),r=n.$,i=n.$$,o=function(t){var n=r(t,"navLabel"),a=r(t,"content"),s=N(n),u=function(t){return t?decodeURI(e(t)):null}(a.getAttribute("src"));if("navPoint"===t.localName){var c=i(t,"navPoint");return{label:s,href:u,subitems:c.length?c.map(o):null}}return{label:s,href:u}},a=function(t,e){return i(t,e).map(o)},s=function(e,n){var i=r(t.documentElement,e);return i?a(i,n):null};return{toc:s("navMap","navPoint"),pageList:s("pageList","pageTarget"),others:i(t.documentElement,"navList").map((function(t){return{label:N(r(t,"navLabel")),list:a(t,"navTarget")}}))}},K=function(t){if(t){var e=t.split(":").map((function(t){return parseFloat(t)}));if(3===e.length){var n=(0,c.A)(e,3);return 60*n[0]*60+60*n[1]+n[2]}if(2===e.length){var r=(0,c.A)(e,2);return 60*r[0]+r[1]}var i=t.split(/(?=[^\d.])/),o=(0,c.A)(i,2),a=o[0],s=o[1];return parseFloat(a)*("h"===s?3600:"min"===s?60:"ms"===s?.001:1)}},J=new WeakMap,Z=new WeakMap,Q=new WeakMap,tt=new WeakMap,et=new WeakMap,nt=new WeakMap,rt=new WeakMap,it=new WeakMap,ot=new WeakSet,at=function(t){function e(t,n){var i,s,u,c;return(0,r.A)(this,e),s=this,u=e,u=(0,a.A)(u),y(i=(0,o.A)(s,g()?Reflect.construct(u,[],(0,a.A)(s).constructor):u.apply(s,c)),ot),b(i,J,void 0),b(i,Z,void 0),b(i,Q,void 0),b(i,tt,void 0),b(i,et,void 0),b(i,nt,void 0),b(i,rt,1),b(i,it,1),i.book=t,i.loadXML=n,i}return(0,s.A)(e,t),(0,i.A)(e,[{key:"start",value:(n=(0,f.A)(p().mark((function t(e){var n,r,i,o,a,s,u,c,l=this,f=arguments;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=f.length>1&&void 0!==f[1]?f[1]:function(){return!0},null===(n=k(nt,this))||void 0===n||n.pause(),i=this.book.sections[e],o=null==i?void 0:i.id){t.next=1;break}return t.abrupt("return");case 1:if(a=i.mediaOverlay){t.next=2;break}return t.abrupt("return",this.start(e+1));case 2:return w(Q,this,e),t.next=3,A(ot,this,st).call(this,a);case 3:s=0;case 4:if(!(s<k(J,this).length)){t.next=8;break}u=k(J,this)[s].items,c=0;case 5:if(!(c<u.length)){t.next=7;break}if(u[c].text.split("#")[0]!==o||!r(u[c],c,u)){t.next=6;break}return t.abrupt("return",A(ot,this,pt).call(this,s,c).catch((function(t){return A(ot,l,ft).call(l,t)})));case 6:c++,t.next=5;break;case 7:s++,t.next=4;break;case 8:case"end":return t.stop()}}),t,this)}))),function(t){return n.apply(this,arguments)})},{key:"pause",value:function(){var t;null===(t=k(nt,this))||void 0===t||t.pause()}},{key:"resume",value:function(){var t,e=this;null===(t=k(nt,this))||void 0===t||t.play().catch((function(t){return A(ot,e,ft).call(e,t)}))}},{key:"prev",value:function(){k(et,this)>0?A(ot,this,pt).call(this,k(tt,this),k(et,this)-1):k(tt,this)>0?A(ot,this,pt).call(this,k(tt,this)-1,k(J,this)[k(tt,this)-1].items.length-1):k(Q,this)>0&&this.start(k(Q,this)-1,(function(t,e,n){return e===n.length-1}))}},{key:"next",value:function(){A(ot,this,pt).call(this,k(tt,this),k(et,this)+1)}},{key:"setVolume",value:function(t){w(rt,this,t),k(nt,this)&&(k(nt,this).volume=t)}},{key:"setRate",value:function(t){w(it,this,t),k(nt,this)&&(k(nt,this).playbackRate=t)}}]);var n}((0,u.A)(EventTarget));function st(t){return ut.apply(this,arguments)}function ut(){return(ut=(0,f.A)(p().mark((function t(e){var n,r,i,o,a;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(k(Z,this)!==e){t.next=1;break}return t.abrupt("return");case 1:return t.next=2,this.loadXML(e.href);case 2:n=t.sent,r=function(t){return t?z(t,e.href):null},i=D(n,"http://www.w3.org/ns/SMIL"),o=i.$,a=i.$$$,w(tt,this,-1),w(et,this,-1),w(J,this,a(n,"par").reduce((function(t,e){var n,i=r(null===(n=o(e,"text"))||void 0===n?void 0:n.getAttribute("src")),a=o(e,"audio");if(!i||!a)return t;var s=r(a.getAttribute("src")),u=K(a.getAttribute("clipBegin")),c=K(a.getAttribute("clipEnd")),l=t.at(-1);return(null==l?void 0:l.src)===s?l.items.push({text:i,begin:u,end:c}):t.push({src:s,items:[{text:i,begin:u,end:c}]}),t}),[])),w(Z,this,e);case 3:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}function ct(t){return k(J,t)[k(tt,t)]}function lt(t){var e;return null===(e=x(ot,t,ct))||void 0===e||null===(e=e.items)||void 0===e?void 0:e[k(et,t)]}function ft(t){console.error(t),this.dispatchEvent(new CustomEvent("error",{detail:t}))}function ht(){this.dispatchEvent(new CustomEvent("highlight",{detail:x(ot,this,lt)}))}function dt(){this.dispatchEvent(new CustomEvent("unhighlight",{detail:x(ot,this,lt)}))}function pt(t,e){return vt.apply(this,arguments)}function vt(){return(vt=(0,f.A)(p().mark((function t(e,n){var r,i,o,a,s,u,c=this;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(k(nt,this)&&(k(nt,this).pause(),URL.revokeObjectURL(k(nt,this).src),w(nt,this,null)),w(tt,this,e),w(et,this,n),(i=null===(r=x(ot,this,ct))||void 0===r?void 0:r.src)&&x(ot,this,lt)){t.next=1;break}return t.abrupt("return",this.start(k(Q,this)+1));case 1:return s=URL,t.next=2,this.book.loadBlob(i);case 2:u=t.sent,o=s.createObjectURL.call(s,u),a=new Audio(o),w(nt,this,a),a.addEventListener("timeupdate",(function(){var t;if(!a.paused){var e=a.currentTime,n=x(ot,c,ct).items;if(e>(null===(t=x(ot,c,lt))||void 0===t?void 0:t.end)&&(A(ot,c,dt).call(c),k(et,c)===n.length-1))A(ot,c,pt).call(c,k(tt,c)+1,0).catch((function(t){return A(ot,c,ft).call(c,t)}));else{for(var r=k(et,c);(null===(o=n[k(et,c)+1])||void 0===o?void 0:o.begin)<=e;){var i,o;w(et,c,(i=k(et,c),++i))}k(et,c)!==r&&A(ot,c,ht).call(c)}}})),a.addEventListener("error",(function(){return A(ot,c,ft).call(c,new Error("Failed to load ".concat(i)))})),a.addEventListener("playing",(function(){return A(ot,c,ht).call(c)})),a.addEventListener("pause",(function(){return A(ot,c,dt).call(c)})),a.addEventListener("ended",(function(){A(ot,c,dt).call(c),URL.revokeObjectURL(o),w(nt,c,null),A(ot,c,pt).call(c,e+1,0).catch((function(t){return A(ot,c,ft).call(c,t)}))})),a.addEventListener("canplaythrough",(function(){var t;a.currentTime=null!==(t=x(ot,c,lt).begin)&&void 0!==t?t:0,a.volume=k(rt,c),a.playbackRate=k(it,c),a.play().catch((function(t){return A(ot,c,ft).call(c,t)}))}));case 3:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}var gt=/([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})/,yt=function(t){var e;return N(null!==(e=t.getElementById(t.documentElement.getAttribute("unique-identifier")))&&void 0!==e?e:t.getElementsByTagNameNS(L,"identifier")[0])},bt=function(){var t=(0,f.A)(p().mark((function t(e,n,r){var i,o,a,s;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=Uint8Array,t.next=1,r.slice(0,n).arrayBuffer();case 1:for(s=t.sent,i=new a(s),n=Math.min(n,i.length),o=0;o<n;o++)i[o]=i[o]^e[o%e.length];return t.abrupt("return",new Blob([i,r.slice(n)],{type:r.type}));case 2:case"end":return t.stop()}}),t)})));return function(e,n,r){return t.apply(this,arguments)}}(),mt=function(){var t=(0,f.A)(p().mark((function t(e){var n,r;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=(new TextEncoder).encode(e),t.next=1,globalThis.crypto.subtle.digest("SHA-1",n);case 1:return r=t.sent,t.abrupt("return",new Uint8Array(r));case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),xt=new WeakMap,wt=new WeakMap,kt=new WeakMap,At=function(){return(0,i.A)((function t(e){(0,r.A)(this,t),b(this,xt,new Map),b(this,wt,new Map),b(this,kt,void 0),w(kt,this,e)}),[{key:"init",value:(t=(0,f.A)(p().mark((function t(e,n){var r,i,o,a,s=this;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e){t.next=1;break}return t.abrupt("return");case 1:r=Array.from(e.getElementsByTagNameNS(C,"EncryptedData"),(function(t){var e,n;return{algorithm:null===(e=t.getElementsByTagNameNS(C,"EncryptionMethod")[0])||void 0===e?void 0:e.getAttribute("Algorithm"),uri:null===(n=t.getElementsByTagNameNS(C,"CipherReference")[0])||void 0===n?void 0:n.getAttribute("URI")}})),i=p().mark((function t(){var e,r,i,u,c;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e=a[o],r=e.algorithm,i=e.uri,k(wt,s).has(r)){t.next=3;break}if(u=k(kt,s)[r]){t.next=1;break}return console.warn("Unknown encryption algorithm"),t.abrupt("return",1);case 1:return t.next=2,u.key(n);case 2:c=t.sent,k(wt,s).set(r,(function(t){return u.decode(c,t)}));case 3:k(xt,s).set(i,r);case 4:case"end":return t.stop()}}),t)})),o=0,a=r;case 2:if(!(o<a.length)){t.next=5;break}return t.delegateYield(i(),"t0",3);case 3:if(!t.t0){t.next=4;break}return t.abrupt("continue",4);case 4:o++,t.next=2;break;case 5:case"end":return t.stop()}}),t)}))),function(e,n){return t.apply(this,arguments)})},{key:"getDecoder",value:function(t){var e;return null!==(e=k(wt,this).get(k(xt,this).get(t)))&&void 0!==e?e:function(t){return t}}}]);var t}(),St=function(){return(0,i.A)((function t(e){var n,i,o,a,s,u,c,l=e.opf,f=e.resolveHref;(0,r.A)(this,t),this.opf=l;var h=D(l,O),d=h.$,p=h.$$,g=h.$$$,y=d(l.documentElement,"manifest"),b=d(l.documentElement,"spine"),m=p(b,"itemref");this.manifest=p(y,"item").map(F("href","id","media-type","properties","media-overlay")).map((function(t){var e;return t.href=f(t.href),t.properties=null===(e=t.properties)||void 0===e?void 0:e.split(/\s/),t})),this.spine=m.map(F("idref","id","linear","properties")).map((function(t){var e;return t.properties=null===(e=t.properties)||void 0===e?void 0:e.split(/\s/),t})),this.pageProgressionDirection=b.getAttribute("page-progression-direction"),this.navPath=null===(n=this.getItemByProperty("nav"))||void 0===n?void 0:n.href,this.ncxPath=null===(i=null!==(o=this.getItemByID(b.getAttribute("toc")))&&void 0!==o?o:this.manifest.find((function(t){return t.mediaType===R.NCX})))||void 0===i?void 0:i.href;var x=d(l.documentElement,"guide");x&&(this.guide=p(x,"reference").map(F("type","title","href")).map((function(t){var e=t.type,n=t.title,r=t.href;return{label:n,type:e.split(/\s/),href:f(r)}}))),this.cover=null!==(a=null!==(s=this.getItemByProperty("cover-image"))&&void 0!==s?s:this.getItemByID(null===(u=g(l,"meta").find(P("name","cover")))||void 0===u?void 0:u.getAttribute("content")))&&void 0!==a?a:this.getItemByHref(null===(c=this.guide)||void 0===c||null===(c=c.find((function(t){return t.type.includes("cover")})))||void 0===c?void 0:c.href),this.cfis=v.ob(m)}),[{key:"getItemByID",value:function(t){return this.manifest.find((function(e){return e.id===t}))}},{key:"getItemByHref",value:function(t){return this.manifest.find((function(e){return e.href===t}))}},{key:"getItemByProperty",value:function(t){return this.manifest.find((function(e){var n;return null===(n=e.properties)||void 0===n?void 0:n.includes(t)}))}},{key:"resolveCFI",value:function(t){var e,n,r=v.qg(t),i=(null!==(e=r.parent)&&void 0!==e?e:r).shift(),o=v.wT(this.opf,i);o&&"idref"!==o.nodeName&&(i[i.length-1].id=null,o=v.wT(this.opf,i));var a=null===(n=o)||void 0===n?void 0:n.getAttribute("idref");return{index:this.spine.findIndex((function(t){return t.idref===a})),anchor:function(t){return v.Xp(t,r)}}}}])}(),Et=new WeakMap,_t=new WeakMap,Tt=new WeakMap,Ot=function(){return(0,i.A)((function t(e){var n=e.loadText,i=e.loadBlob,o=e.resources;(0,r.A)(this,t),b(this,Et,new Map),b(this,_t,new Map),b(this,Tt,new Map),(0,l.A)(this,"allowScript",!1),this.loadText=n,this.loadBlob=i,this.manifest=o.manifest,this.assets=o.manifest;var a=new URLSearchParams(window.location.search);this.allowScript=JSON.parse(a.get("style")).allowScript}),[{key:"createURL",value:function(t,e,n,r){if(!e)return"";var i=URL.createObjectURL(new Blob([e],{type:n}));if(k(Et,this).set(t,i),k(Tt,this).set(t,1),r){var o=k(_t,this).get(r);o?o.push(t):k(_t,this).set(r,[t])}return i}},{key:"ref",value:function(t,e){var n=k(_t,this).get(e);return null!=n&&n.includes(t)||(k(Tt,this).set(t,k(Tt,this).get(t)+1),n?n.push(t):k(_t,this).set(e,[t])),k(Et,this).get(t)}},{key:"unref",value:function(t){if(k(Tt,this).has(t)){var e=k(Tt,this).get(t)-1;if(e<1){URL.revokeObjectURL(k(Et,this).get(t)),k(Et,this).delete(t),k(Tt,this).delete(t);var n=k(_t,this).get(t);if(n)for(;n.length;)this.unref(n.pop());k(_t,this).delete(t)}else k(Tt,this).set(t,e)}}},{key:"loadItem",value:(o=(0,f.A)(p().mark((function t(e){var n,r,i,o,a,s,u,c,l,f,h=arguments;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=h.length>1&&void 0!==h[1]?h[1]:[],e){t.next=1;break}return t.abrupt("return",null);case 1:if(r=e.href,i=e.mediaType,!(o=R.JS.test(e.mediaType))||this.allowScript){t.next=2;break}return t.abrupt("return",null);case 2:if(a=n[n.length-1],!k(Et,this).has(r)){t.next=3;break}return t.abrupt("return",this.ref(r,a));case 3:if(!o&&![R.XHTML,R.HTML,R.CSS,R.SVG].includes(i)||!n.every((function(t){return t!==r}))){t.next=4;break}return t.abrupt("return",this.loadReplaced(e,n));case 4:return s=this,u=r,t.next=5,this.loadBlob(r);case 5:return c=t.sent,l=i,f=a,t.abrupt("return",s.createURL.call(s,u,c,l,f));case 6:case"end":return t.stop()}}),t,this)}))),function(t){return o.apply(this,arguments)})},{key:"loadHref",value:(n=(0,f.A)(p().mark((function t(e,n){var r,i,o,a=arguments;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=a.length>2&&void 0!==a[2]?a[2]:[],!B(e)){t.next=1;break}return t.abrupt("return",e);case 1:if(i=z(e,n),o=this.manifest.find((function(t){return t.href===i}))){t.next=2;break}return t.abrupt("return",e);case 2:return t.abrupt("return",this.loadItem(o,r.concat(n)));case 3:case"end":return t.stop()}}),t,this)}))),function(t,e){return n.apply(this,arguments)})},{key:"loadReplaced",value:(e=(0,f.A)(p().mark((function t(e){var n,r,i,o,a,s,u,c,l,h,d,v,g,y,b,m,x,w,k,A,E,_,T,O,M,L,C,j,P,F,N,D,z,B,W,H,q,V,$,G,X,Y,K,J,Z,Q,tt,et,nt=this,rt=arguments;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=rt.length>1&&void 0!==rt[1]?rt[1]:[],r=e.href,i=e.mediaType,o=n[n.length-1],t.next=1,this.loadText(r);case 1:if(a=t.sent){t.next=2;break}return t.abrupt("return",null);case 2:if(![R.XHTML,R.HTML,R.SVG].includes(i)){t.next=59;break}if(u=(new DOMParser).parseFromString(a,i),i!==R.XHTML||!u.querySelector("parsererror")&&null!==(s=u.documentElement)&&void 0!==s&&s.namespaceURI||(console.warn(null!==(c=null===(l=u.querySelector("parsererror"))||void 0===l?void 0:l.innerText)&&void 0!==c?c:"Invalid XHTML"),e.mediaType=R.HTML,u=(new DOMParser).parseFromString(a,e.mediaType)),![R.XHTML,R.SVG].includes(e.mediaType)){t.next=6;break}h=u.firstChild;case 3:if(!(h instanceof ProcessingInstruction)){t.next=6;break}if(!h.data){t.next=5;break}return t.next=4,U(h.data,/(?:^|\s*)(href\s*=\s*['"])([^'"]*)(['"])/i,(function(t,e,i,o){return nt.loadHref(i,r,n).then((function(t){return"".concat(e).concat(t).concat(o)}))}));case 4:d=t.sent,h.replaceWith(u.createProcessingInstruction(h.target,d));case 5:h=h.nextSibling,t.next=3;break;case 6:v=function(){var t=(0,f.A)(p().mark((function t(e,i){var o,a,s;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o=e,a=i,t.next=1,nt.loadHref(e.getAttribute(i),r,n);case 1:return s=t.sent,t.abrupt("return",o.setAttribute.call(o,a,s));case 2:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),g=S(u.querySelectorAll("link[href]")),t.prev=7,g.s();case 8:if((y=g.n()).done){t.next=10;break}return b=y.value,t.next=9,v(b,"href");case 9:t.next=8;break;case 10:t.next=12;break;case 11:t.prev=11,H=t.catch(7),g.e(H);case 12:return t.prev=12,g.f(),t.finish(12);case 13:m=S(u.querySelectorAll("[src]")),t.prev=14,m.s();case 15:if((x=m.n()).done){t.next=17;break}return w=x.value,t.next=16,v(w,"src");case 16:t.next=15;break;case 17:t.next=19;break;case 18:t.prev=18,q=t.catch(14),m.e(q);case 19:return t.prev=19,m.f(),t.finish(19);case 20:k=S(u.querySelectorAll("[poster]")),t.prev=21,k.s();case 22:if((A=k.n()).done){t.next=24;break}return E=A.value,t.next=23,v(E,"poster");case 23:t.next=22;break;case 24:t.next=26;break;case 25:t.prev=25,V=t.catch(21),k.e(V);case 26:return t.prev=26,k.f(),t.finish(26);case 27:_=S(u.querySelectorAll("object[data]")),t.prev=28,_.s();case 29:if((T=_.n()).done){t.next=31;break}return O=T.value,t.next=30,v(O,"data");case 30:t.next=29;break;case 31:t.next=33;break;case 32:t.prev=32,$=t.catch(28),_.e($);case 33:return t.prev=33,_.f(),t.finish(33);case 34:M=S(u.querySelectorAll("[*|href]:not([href])")),t.prev=35,M.s();case 36:if((L=M.n()).done){t.next=39;break}return C=L.value,G=C,X=I,t.next=37,this.loadHref(C.getAttributeNS(I,"href"),r,n);case 37:Y=t.sent,G.setAttributeNS.call(G,X,"href",Y);case 38:t.next=36;break;case 39:t.next=41;break;case 40:t.prev=40,K=t.catch(35),M.e(K);case 41:return t.prev=41,M.f(),t.finish(41);case 42:j=S(u.querySelectorAll("style")),t.prev=43,j.s();case 44:if((P=j.n()).done){t.next=47;break}if(!(F=P.value).textContent){t.next=46;break}return t.next=45,this.replaceCSS(F.textContent,r,n);case 45:F.textContent=t.sent;case 46:t.next=44;break;case 47:t.next=49;break;case 48:t.prev=48,J=t.catch(43),j.e(J);case 49:return t.prev=49,j.f(),t.finish(49);case 50:N=S(u.querySelectorAll("[style]")),t.prev=51,N.s();case 52:if((D=N.n()).done){t.next=55;break}return z=D.value,Z=z,t.next=53,this.replaceCSS(z.getAttribute("style"),r,n);case 53:Q=t.sent,Z.setAttribute.call(Z,"style",Q);case 54:t.next=52;break;case 55:t.next=57;break;case 56:t.prev=56,tt=t.catch(51),N.e(tt);case 57:return t.prev=57,N.f(),t.finish(57);case 58:return B=(new XMLSerializer).serializeToString(u),t.abrupt("return",this.createURL(r,B,e.mediaType,o));case 59:if(i!==R.CSS){t.next=61;break}return t.next=60,this.replaceCSS(a,r,n);case 60:et=t.sent,t.next=63;break;case 61:return t.next=62,this.replaceString(a,r,n);case 62:et=t.sent;case 63:return W=et,t.abrupt("return",this.createURL(r,W,i,o));case 64:case"end":return t.stop()}}),t,this,[[7,11,12,13],[14,18,19,20],[21,25,26,27],[28,32,33,34],[35,40,41,42],[43,48,49,50],[51,56,57,58]])}))),function(t){return e.apply(this,arguments)})},{key:"replaceCSS",value:(t=(0,f.A)(p().mark((function t(e,n){var r,i,o,a,s,u,c,l,f,h=this,d=arguments;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return s=d.length>2&&void 0!==d[2]?d[2]:[],t.next=1,U(e,/url\(\s*["']?([^'"\n]*?)\s*["']?\s*\)/gi,(function(t,e){return h.loadHref(e,n,s).then((function(t){return'url("'.concat(t,'")')}))}));case 1:return u=t.sent,t.next=2,U(u,/@import\s*["']([^"'\n]*?)["']/gi,(function(t,e){return h.loadHref(e,n,s).then((function(t){return'@import "'.concat(t,'"')}))}));case 2:return c=t.sent,l=null!==(r=null===(i=window)||void 0===i?void 0:i.innerWidth)&&void 0!==r?r:800,f=null!==(o=null===(a=window)||void 0===a?void 0:a.innerHeight)&&void 0!==o?o:600,t.abrupt("return",c.replace(/([{\s;])-epub-/gi,"$1").replace(/(\d*\.?\d+)vw/gi,(function(t,e){return parseFloat(e)*l/100+"px"})).replace(/(\d*\.?\d+)vh/gi,(function(t,e){return parseFloat(e)*f/100+"px"})).replace(/page-break-(after|before|inside)\s*:/gi,(function(t,e){return"-webkit-column-break-".concat(e,":")})).replace(/break-(after|before|inside)\s*:\s*(avoid-)?page/gi,(function(t,e,n){return"break-".concat(e,": ").concat(null!=n?n:"","column")})).replace(/(\d*\.?\d+)px/gi,(function(t,e){return"".concat(parseFloat(e)/16,"em")})));case 3:case"end":return t.stop()}}),t)}))),function(e,n){return t.apply(this,arguments)})},{key:"replaceString",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=new Map,o=this.assets.map((function(t){if(t.href!==e){var n,r=function(t,e){if(!t)return e;var n=t.replace(/\/$/,"").split("/"),r=e.replace(/\/$/,"").split("/"),i=(n.length>r.length?n:r).findIndex((function(t,e){return n[e]!==r[e]}));return i<0?"":Array(n.length-i).fill("..").concat(r.slice(i)).join("/")}(function(t){return t.slice(0,t.lastIndexOf("/")+1)}(e),t.href),o=encodeURI(r),a="/"+t.href,s=encodeURI(a),u=new Set([r,o,a,s]),c=S(u);try{for(c.s();!(n=c.n()).done;){var l=n.value;i.set(l,t)}}catch(t){c.e(t)}finally{c.f()}return Array.from(u)}})).flat().filter((function(t){return t}));if(!o.length)return t;var a=new RegExp(o.map(W).join("|"),"g");return U(t,a,function(){var t=(0,f.A)(p().mark((function t(o){return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",n.loadItem(i.get(o.replace(/^\//,"")),r.concat(e)));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}},{key:"unloadItem",value:function(t){this.unref(null==t?void 0:t.href)}},{key:"destroy",value:function(){var t,e=S(k(Et,this).values());try{for(e.s();!(t=e.n()).done;){var n=t.value;URL.revokeObjectURL(n)}}catch(t){e.e(t)}finally{e.f()}}}]);var t,e,n,o}(),Mt=function(t){var e,n=S(t);try{for(n.s();!(e=n.n()).done;){var r=e.value;if("page-spread-left"===r||"rendition:page-spread-left"===r)return"left";if("page-spread-right"===r||"rendition:page-spread-right"===r)return"right";if("rendition:page-spread-center"===r)return"center"}}catch(t){n.e(t)}finally{n.f()}},Lt=function(t){return t?{fixedLayout:N(t.querySelector('option[name="fixed-layout"]')),openToSpread:N(t.querySelector('option[name="open-to-spread"]'))}:null},Ct=new WeakMap,It=new WeakMap,Rt=new WeakSet,jt=function(){return(0,i.A)((function t(e){var n=e.loadText,i=e.loadBlob,o=e.getSize,a=e.sha1;(0,r.A)(this,t),y(this,Rt),(0,l.A)(this,"parser",new DOMParser),b(this,Ct,void 0),b(this,It,void 0),this.loadText=n,this.loadBlob=i,this.getSize=o,w(It,this,new At(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:mt;return{"http://www.idpf.org/2008/embedding":{key:function(e){return t(yt(e).replaceAll(/[\u0020\u0009\u000d\u000a]/g,""))},decode:function(t,e){return bt(t,1040,e)}},"http://ns.adobe.com/pdf/enc#RC":{key:function(t){var e=function(t){var e,n=S(t.getElementsByTagNameNS(L,"identifier"));try{for(n.s();!(e=n.n()).done;){var r=e.value,i=N(r).split(":").slice(-1),o=(0,c.A)(i,1)[0];if(gt.test(o))return o}}catch(t){n.e(t)}finally{n.f()}return""}(t).replaceAll("-","");return Uint8Array.from({length:16},(function(t,n){return parseInt(e.slice(2*n,2*n+2),16)}))},decode:function(t,e){return bt(t,1024,e)}}}}(a)))}),[{key:"init",value:(o=(0,f.A)(p().mark((function t(){var e,n,r,i,o,a,s,u,l,f,d,v,g,y,b,m,x,S,E,_,T,O,M,L,C,I,R,j,P,N,D,B,U,W,H,q,V,$,K,J,Z,Q,tt,et,nt,rt=this;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,A(Rt,this,Pt).call(this,"META-INF/container.xml");case 1:if(v=t.sent){t.next=2;break}throw new Error("Failed to load container file");case 2:if((g=Array.from(v.getElementsByTagNameNS("urn:oasis:names:tc:opendocument:xmlns:container","rootfile"),F("full-path","media-type")).filter((function(t){return"application/oebps-package+xml"===t.mediaType}))).length){t.next=3;break}throw new Error("No package document defined in container");case 3:return y=g[0].fullPath,t.next=4,A(Rt,this,Pt).call(this,y);case 4:if(b=t.sent){t.next=5;break}throw new Error("Failed to load package document");case 5:return t.next=6,A(Rt,this,Pt).call(this,"META-INF/encryption.xml");case 6:return m=t.sent,t.next=7,k(It,this).init(m,b);case 7:if(this.resources=new St({opf:b,resolveHref:function(t){return z(t,y)}}),w(Ct,this,new Ot({loadText:this.loadText,loadBlob:function(t){return Promise.resolve(rt.loadBlob(t)).then(k(It,rt).getDecoder(t))},resources:this.resources})),this.sections=this.resources.spine.map((function(t,e){var n=t.idref,r=t.linear,i=t.properties,o=void 0===i?[]:i,a=rt.resources.getItemByID(n);return a?{id:a.href,load:function(){return k(Ct,rt).loadItem(a)},unload:function(){return k(Ct,rt).unloadItem(a)},createDocument:function(){return rt.loadDocument(a)},size:rt.getSize(a.href),cfi:rt.resources.cfis[e],linear:r,pageSpread:Mt(o),resolveHref:function(t){return z(t,a.href)},mediaOverlay:a.mediaOverlay?rt.resources.getItemByID(a.mediaOverlay):null}:(console.warn('Could not find item with ID "'.concat(n,'" in manifest')),null)})).filter((function(t){return t})),x=this.resources,S=x.navPath,E=x.ncxPath,!S){t.next=11;break}return t.prev=8,_=function(t){return z(t,S)},q=X,t.next=9,A(Rt,this,Pt).call(this,S);case 9:V=t.sent,T=q(V,_),this.toc=T.toc,this.pageList=T.pageList,this.landmarks=T.landmarks,t.next=11;break;case 10:t.prev=10,$=t.catch(8),console.warn($);case 11:if(this.toc||!E){t.next=15;break}return t.prev=12,O=function(t){return z(t,E)},K=Y,t.next=13,A(Rt,this,Pt).call(this,E);case 13:J=t.sent,M=K(J,O),this.toc=M.toc,this.pageList=M.pageList,t.next=15;break;case 14:t.prev=14,Z=t.catch(12),console.warn(Z);case 15:return null!==(e=this.landmarks)&&void 0!==e||(this.landmarks=this.resources.guide),L=G(b),C=L.metadata,I=L.rendition,R=L.media,this.rendition=I,this.media=R,this.dir=this.resources.pageProgressionDirection,Q=Lt,t.next=16,A(Rt,this,Pt).call(this,"META-INF/com.apple.ibooks.display-options.xml");case 16:if(et=n=t.sent,!(tt=null!==et)){t.next=17;break}tt=void 0!==n;case 17:if(!tt){t.next=18;break}nt=n,t.next=20;break;case 18:return t.next=19,A(Rt,this,Pt).call(this,"META-INF/com.kobobooks.display-options.xml");case 19:nt=t.sent;case 20:return(j=Q(nt))&&("true"===j.fixedLayout&&(null!==(N=(P=this.rendition).layout)&&void 0!==N||(P.layout="pre-paginated")),"false"===j.openToSpread&&(null!==(B=(D=this.sections.find((function(t){return"no"!==t.linear}))).pageSpread)&&void 0!==B||(D.pageSpread="rtl"===this.dir?"left":"right"))),this.parsedMetadata=C,U=null==C||null===(r=C.title)||void 0===r?void 0:r[0],this.metadata={title:null==U?void 0:U.value,subtitle:null==C||null===(i=C.title)||void 0===i||null===(i=i.find((function(t){return"subtitle"===t.titleType})))||void 0===i?void 0:i.value,sortAs:null==U?void 0:U.fileAs,language:null==C?void 0:C.language,identifier:yt(b),description:null==C||null===(o=C.description)||void 0===o?void 0:o.value,publisher:null==C||null===(a=C.publisher)||void 0===a?void 0:a.value,published:null==C?void 0:C.date,modified:null==C?void 0:C.dctermsModified,subject:null==C||null===(s=C.subject)||void 0===s||null===(s=s.filter((function(t){var e=t.value,n=t.term;return e||n})))||void 0===s?void 0:s.map((function(t){return{name:t.value,code:t.term,scheme:t.authority}})),rights:null==C||null===(u=C.rights)||void 0===u?void 0:u.value},W={art:"artist",aut:"author",bkp:"producer",clr:"colorist",edt:"editor",ill:"illustrator",nrt:"narrator",trl:"translator",pbl:"publisher"},H=function(t){return function(e){var n,r=(0,h.A)(new Set(null===(n=e.role)||void 0===n?void 0:n.map((function(e){var n,r=e.value,i=e.scheme;return null!==(n=i&&"marc:relators"!==i?null:W[r])&&void 0!==n?n:t})))),i={name:e.value,sortAs:e.fileAs};return[null!=r&&r.length?r:[t],i]}},null==C||null===(l=C.creator)||void 0===l||null===(l=l.map(H("author")))||void 0===l||null===(l=l.concat(null==C||null===(f=C.contributor)||void 0===f||null===(d=f.map)||void 0===d?void 0:d.call(f,H("contributor"))))||void 0===l||l.forEach((function(t){var e=(0,c.A)(t,2),n=e[0],r=e[1];return n.forEach((function(t){rt.metadata[t]?rt.metadata[t].push(r):rt.metadata[t]=[r]}))})),t.abrupt("return",this);case 21:case"end":return t.stop()}}),t,this,[[8,10],[12,14]])}))),function(){return o.apply(this,arguments)})},{key:"loadDocument",value:(n=(0,f.A)(p().mark((function t(e){var n;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,this.loadText(e.href);case 1:return n=t.sent,t.abrupt("return",this.parser.parseFromString(n,e.mediaType));case 2:case"end":return t.stop()}}),t,this)}))),function(t){return n.apply(this,arguments)})},{key:"getMediaOverlay",value:function(){return new at(this,A(Rt,this,Pt).bind(this))}},{key:"resolveCFI",value:function(t){return this.resources.resolveCFI(t)}},{key:"resolveHref",value:function(t){var e=t.split("#"),n=(0,c.A)(e,2),r=n[0],i=n[1],o=this.resources.getItemByHref(decodeURI(r));return o?{index:this.resources.spine.findIndex((function(t){return t.idref===o.id})),anchor:i?function(t){return function(t,e){var n;return null!==(n=t.getElementById(e))&&void 0!==n?n:t.querySelector('[name="'.concat(CSS.escape(e),'"]'))}(t,i)}:function(){return 0}}:null}},{key:"splitTOCHref",value:function(t){var e;return null!==(e=null==t?void 0:t.split("#"))&&void 0!==e?e:[]}},{key:"getTOCFragment",value:function(t,e){var n;return null!==(n=t.getElementById(e))&&void 0!==n?n:t.querySelector('[name="'.concat(CSS.escape(e),'"]'))}},{key:"isExternal",value:function(t){return B(t)}},{key:"getCover",value:(e=(0,f.A)(p().mark((function t(){var e,n,r,i,o,a,s;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(null==(n=null===(e=this.resources)||void 0===e?void 0:e.cover)||!n.href){t.next=2;break}return i=Blob,t.next=1,this.loadBlob(n.href);case 1:o=t.sent,a=[o],s={type:n.mediaType},r=new i(a,s),t.next=3;break;case 2:r=null;case 3:return t.abrupt("return",r);case 4:case"end":return t.stop()}}),t,this)}))),function(){return e.apply(this,arguments)})},{key:"getCalibreBookmarks",value:(t=(0,f.A)(p().mark((function t(){var e,n;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,this.loadText("META-INF/calibre_bookmarks.txt");case 1:if(e=t.sent,null==e||!e.startsWith("encoding=json+base64:")){t.next=2;break}return n=atob(e.slice(21)),t.abrupt("return",JSON.parse(n));case 2:case"end":return t.stop()}}),t,this)}))),function(){return t.apply(this,arguments)})},{key:"destroy",value:function(){var t;null===(t=k(Ct,this))||void 0===t||t.destroy()}}]);var t,e,n,o}();function Pt(t){return Ft.apply(this,arguments)}function Ft(){return(Ft=(0,f.A)(p().mark((function t(e){var n,r;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,this.loadText(e);case 1:if(n=t.sent){t.next=2;break}return t.abrupt("return",null);case 2:if(!(r=this.parser.parseFromString(n,R.XML)).querySelector("parsererror")){t.next=3;break}throw new Error("XML parsing error: ".concat(e,"\n").concat(r.querySelector("parsererror").innerText));case 3:return t.abrupt("return",r);case 4:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}},1367:function(t,e,n){"use strict";n(6518)({target:"Math",stat:!0},{log2:n(7787)})},1392:function(t,e,n){"use strict";var r,i=n(6518),o=n(7476),a=n(7347).f,s=n(8014),u=n(655),c=n(5749),l=n(7750),f=n(1436),h=n(6395),d=o("".slice),p=Math.min,v=f("startsWith");i({target:"String",proto:!0,forced:!(!h&&!v&&(r=a(String.prototype,"startsWith"),r&&!r.writable)||v)},{startsWith:function(t){var e=u(l(this));c(t);var n=s(p(arguments.length>1?arguments[1]:void 0,e.length)),r=u(t);return d(e,n,n+r.length)===r}})},1405:function(t,e,n){"use strict";var r=n(4576),i=n(8745),o=n(4644),a=n(9039),s=n(7680),u=r.Int8Array,c=o.aTypedArray,l=o.exportTypedArrayMethod,f=[].toLocaleString,h=!!u&&a((function(){f.call(new u(1))}));l("toLocaleString",(function(){return i(f,h?s(c(this)):c(this),s(arguments))}),a((function(){return[1,2].toLocaleString()!==new u([1,2]).toLocaleString()}))||!a((function(){u.prototype.toLocaleString.call([1,2])})))},1415:function(t,e,n){"use strict";n(2405)},1436:function(t,e,n){"use strict";var r=n(8227)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[r]=!1,"/./"[t](e)}catch(t){}}return!1}},1469:function(t,e,n){"use strict";var r=n(7433);t.exports=function(t,e){return new(r(t))(0===e?0:e)}},1480:function(t,e,n){"use strict";var r=n(6518),i=n(9039),o=n(298).f;r({target:"Object",stat:!0,forced:i((function(){return!Object.getOwnPropertyNames(1)}))},{getOwnPropertyNames:o})},1481:function(t,e,n){"use strict";var r=n(6518),i=n(6043);r({target:"Promise",stat:!0,forced:n(916).CONSTRUCTOR},{reject:function(t){var e=i.f(this);return(0,e.reject)(t),e.promise}})},1489:function(t,e,n){"use strict";n(5823)("Uint8",(function(t){return function(e,n,r){return t(this,e,n,r)}}))},1510:function(t,e,n){"use strict";var r=n(6518),i=n(7751),o=n(9297),a=n(655),s=n(5745),u=n(1296),c=s("string-to-symbol-registry"),l=s("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!u},{for:function(t){var e=a(t);if(o(c,e))return c[e];var n=i("Symbol")(e);return c[e]=n,l[n]=e,n}})},1575:function(t,e,n){"use strict";var r=n(4644),i=n(926).left,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("reduce",(function(t){var e=arguments.length;return i(o(this),t,e,e>1?arguments[1]:void 0)}))},1625:function(t,e,n){"use strict";var r=n(9504);t.exports=r({}.isPrototypeOf)},1630:function(t,e,n){"use strict";var r=n(9504),i=n(4644),o=r(n(7029)),a=i.aTypedArray;(0,i.exportTypedArrayMethod)("copyWithin",(function(t,e){return o(a(this),t,e,arguments.length>2?arguments[2]:void 0)}))},1694:function(t,e,n){"use strict";var r=n(4644),i=n(9213).find,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("find",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},1699:function(t,e,n){"use strict";var r=n(6518),i=n(9504),o=n(5749),a=n(7750),s=n(655),u=n(1436),c=i("".indexOf);r({target:"String",proto:!0,forced:!u("includes")},{includes:function(t){return!!~c(s(a(this)),s(o(t)),arguments.length>1?arguments[1]:void 0)}})},1740:function(t,e,n){"use strict";n(5823)("Uint32",(function(t){return function(e,n,r){return t(this,e,n,r)}}))},1745:function(t,e,n){"use strict";var r=n(6518),i=n(7476),o=n(9039),a=n(6346),s=n(8551),u=n(5610),c=n(8014),l=a.ArrayBuffer,f=a.DataView,h=f.prototype,d=i(l.prototype.slice),p=i(h.getUint8),v=i(h.setUint8);r({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:o((function(){return!new l(2).slice(1,void 0).byteLength}))},{slice:function(t,e){if(d&&void 0===e)return d(s(this),t);for(var n=s(this).byteLength,r=u(t,n),i=u(void 0===e?n:e,n),o=new l(c(i-r)),a=new f(this),h=new f(o),g=0;r<i;)v(h,g++,p(a,r++));return o}})},1761:function(t,e,n){"use strict";var r=n(9565),i=n(9504),o=n(9228),a=n(8551),s=n(34),u=n(8014),c=n(655),l=n(7750),f=n(5966),h=n(7829),d=n(1034),p=n(6682),v=i("".indexOf);o("match",(function(t,e,n){return[function(e){var n=l(this),i=s(e)?f(e,t):void 0;return i?r(i,e,n):new RegExp(e)[t](c(n))},function(t){var r=a(this),i=c(t),o=n(e,r,i);if(o.done)return o.value;var s=c(d(r));if(-1===v(s,"g"))return p(r,i);var l=-1!==v(s,"u");r.lastIndex=0;for(var f,g=[],y=0;null!==(f=p(r,i));){var b=c(f[0]);g[y]=b,""===b&&(r.lastIndex=h(i,u(r.lastIndex),l)),y++}return 0===y?null:g}]}))},1791:function(t,e,n){var r=n(5172),i=n(5546);t.exports=function t(e,n){function o(t,i,a,s){try{var u=e[t](i),c=u.value;return c instanceof r?n.resolve(c.v).then((function(t){o("next",t,a,s)}),(function(t){o("throw",t,a,s)})):n.resolve(c).then((function(t){u.value=t,a(u)}),(function(t){return o("throw",t,a,s)}))}catch(t){s(t)}}var a;this.next||(i(t.prototype),i(t.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",(function(){return this}))),i(this,"_invoke",(function(t,e,r){function i(){return new n((function(e,n){o(t,r,e,n)}))}return a=a?a.then(i,i):i()}),!0)},t.exports.__esModule=!0,t.exports.default=t.exports},1828:function(t,e,n){"use strict";var r=n(9504),i=n(9297),o=n(5397),a=n(9617).indexOf,s=n(421),u=r([].push);t.exports=function(t,e){var n,r=o(t),c=0,l=[];for(n in r)!i(s,n)&&i(r,n)&&u(l,n);for(;e.length>c;)i(r,n=e[c++])&&(~a(l,n)||u(l,n));return l}},1833:function(t,e,n){"use strict";n(511)("search")},1920:function(t,e,n){"use strict";var r=n(4644),i=n(9213).filter,o=n(9948),a=r.aTypedArray;(0,r.exportTypedArrayMethod)("filter",(function(t){var e=i(a(this),t,arguments.length>1?arguments[1]:void 0);return o(this,e)}))},1951:function(t,e,n){"use strict";var r=n(8227);e.f=r},1955:function(t,e,n){"use strict";var r,i,o,a,s,u=n(4576),c=n(3389),l=n(6080),f=n(9225).set,h=n(8265),d=n(9544),p=n(4265),v=n(7860),g=n(6193),y=u.MutationObserver||u.WebKitMutationObserver,b=u.document,m=u.process,x=u.Promise,w=c("queueMicrotask");if(!w){var k=new h,A=function(){var t,e;for(g&&(t=m.domain)&&t.exit();e=k.get();)try{e()}catch(t){throw k.head&&r(),t}t&&t.enter()};d||g||v||!y||!b?!p&&x&&x.resolve?((a=x.resolve(void 0)).constructor=x,s=l(a.then,a),r=function(){s(A)}):g?r=function(){m.nextTick(A)}:(f=l(f,u),r=function(){f(A)}):(i=!0,o=b.createTextNode(""),new y(A).observe(o,{characterData:!0}),r=function(){o.data=i=!i}),w=function(t){k.head||r(),k.add(t)}}t.exports=w},2003:function(t,e,n){"use strict";var r=n(6518),i=n(6395),o=n(916).CONSTRUCTOR,a=n(550),s=n(7751),u=n(4901),c=n(6840),l=a&&a.prototype;if(r({target:"Promise",proto:!0,forced:o,real:!0},{catch:function(t){return this.then(void 0,t)}}),!i&&u(a)){var f=s("Promise").prototype.catch;l.catch!==f&&c(l,"catch",f,{unsafe:!0})}},2008:function(t,e,n){"use strict";var r=n(6518),i=n(9213).filter;r({target:"Array",proto:!0,forced:!n(597)("filter")},{filter:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},2062:function(t,e,n){"use strict";var r=n(6518),i=n(9213).map;r({target:"Array",proto:!0,forced:!n(597)("map")},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},2087:function(t,e,n){"use strict";var r=n(34),i=Math.floor;t.exports=Number.isInteger||function(t){return!r(t)&&isFinite(t)&&i(t)===t}},2106:function(t,e,n){"use strict";var r=n(283),i=n(4913);t.exports=function(t,e,n){return n.get&&r(n.get,e,{getter:!0}),n.set&&r(n.set,e,{setter:!0}),i.f(t,e,n)}},2107:function(t,e,n){"use strict";n(5823)("Int16",(function(t){return function(e,n,r){return t(this,e,n,r)}}))},2134:function(t,e,n){"use strict";n(5823)("Uint8",(function(t){return function(e,n,r){return t(this,e,n,r)}}),!0)},2140:function(t,e,n){"use strict";var r={};r[n(8227)("toStringTag")]="z",t.exports="[object z]"===String(r)},2168:function(t,e,n){"use strict";n(511)("isConcatSpreadable")},2170:function(t,e,n){"use strict";var r=n(4644),i=n(9213).every,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("every",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},2195:function(t,e,n){"use strict";var r=n(9504),i=r({}.toString),o=r("".slice);t.exports=function(t){return o(i(t),8,-1)}},2211:function(t,e,n){"use strict";var r=n(9039);t.exports=!r((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},2248:function(t,e,n){"use strict";n.d(e,{EL:function(){return R},Lu:function(){return l},RK:function(){return d},UD:function(){return k},Xp:function(){return L},ap:function(){return w},ob:function(){return C},qg:function(){return y},wT:function(){return I},yT:function(){return M}});var r=n(296),i=n(5458);function o(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return a(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return s=t.done,t},e:function(t){u=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(u)throw o}}}}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var s,u=function(t,e){return[-1].concat((0,i.A)(e),[t.length]).reduce((function(e,n){var r,i=e.xs,o=e.a;return{xs:null!==(r=null==i?void 0:i.concat([t.slice(o+1,n)]))&&void 0!==r?r:[],a:n}}),{}).xs},c=/\d/,l=/^epubcfi\((.*)\)$/,f=function(t){return t.replace(/[\^[\](),;=]/g,"^$&")},h=function(t){return l.test(t)?t:"epubcfi(".concat(t,")")},d=(s=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.join("!")},function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return"epubcfi(".concat(s.apply(void 0,(0,i.A)(e.map((function(t){var e,n;return null!==(e=null===(n=t.match(l))||void 0===n?void 0:n[1])&&void 0!==e?e:t})))),")")}),p=function(t,e){return function(t,e){return t.map((function(t,n,r){return e(t)?n:null})).filter((function(t){return null!=t}))}(t,(function(t){return(0,r.A)(t,1)[0]===e}))},v=function(t){var e,n,i=[],a=o(t);try{for(a.s();!(n=a.n()).done;){var s=(0,r.A)(n.value,2),u=s[0],c=s[1];if("/"===u)i.push({index:c});else{var l,f=i[i.length-1];if(":"===u)f.offset=c;else if("~"===u)f.temporal=c;else if("@"===u)f.spatial=(null!==(l=f.spatial)&&void 0!==l?l:[]).concat(c);else if(";s"===u)f.side=c;else if("["===u){if("/"!==e||!c){var h;f.text=(null!==(h=f.text)&&void 0!==h?h:[]).concat(c);continue}f.id=c}}e=u}}catch(t){a.e(t)}finally{a.f()}return i},g=function(t){return u(t,p(t,"!")).map(v)},y=function(t){var e,n,i,a=function(t){var e,n,r,i=[],a="",s=function(t){return i.push(t),e=null,a=""},u=function(t){return a+=t,n=!1},l=o(Array.from(t.trim()).concat(""));try{for(l.s();!(r=l.n()).done;){var f,h=r.value;if("^"!==h||n){if("!"===e)s(["!"]);else if(","===e)s([","]);else if("/"===e||":"===e){if(c.test(h)){u(h);continue}s([e,parseInt(a)])}else if("~"===e){if(c.test(h)||"."===h){u(h);continue}s(["~",parseFloat(a)])}else if("@"===e){if(":"===h){s(["@",parseFloat(a)]),e="@";continue}if(c.test(h)||"."===h){u(h);continue}s(["@",parseFloat(a)])}else{if("["===e){";"!==h||n?","!==h||n?"]"!==h||n?u(h):s(["[",a]):(s(["[",a]),e="["):(s(["[",a]),e=";");continue}if(null!==(f=e)&&void 0!==f&&f.startsWith(";")){"="!==h||n?";"!==h||n?"]"!==h||n?u(h):s([e,a]):(s([e,a]),e=";"):(e=";".concat(a),a="");continue}}"/"!==h&&":"!==h&&"~"!==h&&"@"!==h&&"["!==h&&"!"!==h&&","!==h||(e=h)}else n=!0}}catch(t){l.e(t)}finally{l.f()}return i}(null!==(n=null===(i=(e=t).match(l))||void 0===i?void 0:i[1])&&void 0!==n?n:e),s=p(a,",");if(!s.length)return g(a);var f=u(a,s).map(g),h=(0,r.A)(f,3);return{parent:h[0],start:h[1],end:h[2]}},b=function(t){var e,n,r=t.index,i=t.id,o=t.offset,a=t.temporal,s=t.spatial,u=t.text,c=t.side,l=c?";s=".concat(c):"";return"/".concat(r)+(i?"[".concat(f(i)).concat(l,"]"):"")+(null!=o&&r%2?":".concat(o):"")+(a?"~".concat(a):"")+(s?"@".concat(s.join(":")):"")+(u||!i&&c?"["+(null!==(e=null==u||null===(n=u.map(f))||void 0===n?void 0:n.join(","))&&void 0!==e?e:"")+l+"]":"")},m=function(t){return t.parent?[t.parent,t.start,t.end].map(m).join(","):t.map((function(t){return t.map(b).join("")})).join("!")},x=function(t){return h(m(t))},w=function(t,e){return"string"==typeof t?x(w(y(t),e)):t.parent?(n=t.parent,r=t[e?"end":"start"],n.slice(0,-1).concat([n[n.length-1].concat(r[0])]).concat(r.slice(1))):t;var n,r},k=function(t,e){if("string"==typeof t&&(t=y(t)),"string"==typeof e&&(e=y(e)),t.start||e.start)return k(w(t),w(e))||k(w(t,!0),w(e,!0));for(var n=0;n<Math.max(t.length,e.length);n++)for(var r=t[n],i=e[n],o=Math.max(r.length,i.length)-1,a=0;a<=o;a++){var s=r[a],u=i[a];if(!s)return-1;if(!u)return 1;if(s.index>u.index)return 1;if(s.index<u.index)return-1;if(a===o){if(s.offset>u.offset)return 1;if(s.offset<u.offset)return-1}}return 0},A=function(t){var e=t.nodeType;return 3===e||4===e},S=function(t){return 1===t.nodeType},E=function(t,e){var n=Array.from(t.childNodes).filter((function(t){return A(t)||S(t)}));return e?n.map((function(t){var n=e(t);return n===NodeFilter.FILTER_REJECT?null:n===NodeFilter.FILTER_SKIP?E(t,e):t})).flat().filter((function(t){return t})):n},_=function(t,e){var n=E(t,e).reduce((function(t,e){var n=t[t.length-1];return n?A(e)?Array.isArray(n)?n.push(e):A(n)?t[t.length-1]=[n,e]:t.push(e):S(n)?t.push(null,e):t.push(e):t.push(e),t}),[]);return S(n[0])&&n.unshift("first"),S(n[n.length-1])&&n.push("last"),n.unshift("before"),n.push("after"),n},T=function(t,e,n){var r=e[e.length-1].id;if(r){var i=t.ownerDocument.getElementById(r);if(i)return{node:i,offset:0}}var a,s=o(e);try{for(s.s();!(a=s.n()).done;){var u,c,l=a.value.index,f=t?_(t,n)[l]:null;if("first"===f)return{node:null!==(u=t.firstChild)&&void 0!==u?u:t};if("last"===f)return{node:null!==(c=t.lastChild)&&void 0!==c?c:t};if("before"===f)return{node:t,before:!0};if("after"===f)return{node:t,after:!0};t=f}}catch(t){s.e(t)}finally{s.f()}var h=e[e.length-1].offset;if(!Array.isArray(t))return{node:t,offset:h};var d,p=0,v=o(t);try{for(v.s();!(d=v.n()).done;){var g=d.value,y=g.nodeValue.length;if(p+y>=h)return{node:g,offset:h-p};p+=y}}catch(t){v.e(t)}finally{v.f()}},O=function(t,e,n){var r=t.parentNode,i=t.id,a=_(r,n),s=a.findIndex((function(e){return Array.isArray(e)?e.some((function(e){return e===t})):e===t})),u=a[s];if(Array.isArray(u)){var c,l=0,f=o(u);try{for(f.s();!(c=f.n()).done;){var h=c.value;if(h===t){l+=e;break}l+=h.nodeValue.length}}catch(t){f.e(t)}finally{f.f()}e=l}var d={id:i,index:s,offset:e};return(r!==t.ownerDocument.documentElement?O(r,null,n).concat(d):[d]).filter((function(t){return-1!==t.index}))},M=function(t,e){var n=t.startContainer,r=t.startOffset,i=t.endContainer,o=t.endOffset,a=O(n,r,e);return t.collapsed?x([a]):function(t,e){"string"==typeof t&&(t=y(t)),"string"==typeof e&&(e=y(e)),t=w(t),e=w(e,!0);for(var n=t[t.length-1],r=e[e.length-1],i=[],o=[],a=[],s=!0,u=Math.max(n.length,r.length),c=0;c<u;c++){var l=n[c],f=r[c];s&&(s=!((null==l?void 0:l.index)!==(null==f?void 0:f.index)||null!=l&&l.offset||null!=f&&f.offset)),s?i.push(l):(l&&o.push(l),f&&a.push(f))}var h=t.slice(0,-1).concat([i]);return x({parent:h,start:[o],end:[a]})}([a],[O(i,o,e)])},L=function(t,e,n){var r=w(e),i=w(e,!0),o=t.documentElement,a=T(o,r[0],n),s=T(o,i[0],n),u=t.createRange();return a.before?u.setStartBefore(a.node):a.after?u.setStartAfter(a.node):u.setStart(a.node,a.offset),s.before?u.setEndBefore(s.node):s.after?u.setEndAfter(s.node):u.setEnd(s.node,s.offset),u},C=function(t){var e,n=[],i=t[0].parentNode,a=O(i),s=o(_(i).entries());try{for(s.s();!(e=s.n()).done;){var u=(0,r.A)(e.value,2),c=u[0],l=u[1],f=t[n.length];l===f&&n.push(x([a.concat({id:f.id,index:c})]))}}catch(t){s.e(t)}finally{s.f()}return n},I=function(t,e){return T(t.documentElement,w(e)).node},R={fromIndex:function(t){return h("/6/".concat(2*(t+1)))},toIndex:function(t){return(null==t?void 0:t.at(-1).index)/2-1}}},2259:function(t,e,n){"use strict";n(511)("iterator")},2284:function(t,e,n){"use strict";function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}n.d(e,{A:function(){return r}})},2293:function(t,e,n){"use strict";var r=n(8551),i=n(5548),o=n(4117),a=n(8227)("species");t.exports=function(t,e){var n,s=r(t).constructor;return void 0===s||o(n=r(s)[a])?e:i(n)}},2326:function(t,e,n){"use strict";var r=n(6518),i=Math.asinh,o=Math.log,a=Math.sqrt;r({target:"Math",stat:!0,forced:!(i&&1/i(0)>0)},{asinh:function t(e){var n=+e;return isFinite(n)&&0!==n?n<0?-t(-n):o(n+a(n*n+1)):n}})},2333:function(t,e,n){"use strict";var r=n(1291),i=n(655),o=n(7750),a=RangeError;t.exports=function(t){var e=i(o(this)),n="",s=r(t);if(s<0||s===1/0)throw new a("Wrong number of repetitions");for(;s>0;(s>>>=1)&&(e+=e))1&s&&(n+=e);return n}},2357:function(t,e,n){"use strict";var r=n(3724),i=n(9039),o=n(9504),a=n(2787),s=n(1072),u=n(5397),c=o(n(8773).f),l=o([].push),f=r&&i((function(){var t=Object.create(null);return t[2]=2,!c(t,2)})),h=function(t){return function(e){for(var n,i=u(e),o=s(i),h=f&&null===a(i),d=o.length,p=0,v=[];d>p;)n=o[p++],r&&!(h?n in i:c(i,n))||l(v,t?[n,i[n]]:i[n]);return v}};t.exports={entries:h(!0),values:h(!1)}},2360:function(t,e,n){"use strict";var r,i=n(8551),o=n(6801),a=n(8727),s=n(421),u=n(397),c=n(4055),l=n(6119),f="prototype",h="script",d=l("IE_PROTO"),p=function(){},v=function(t){return"<"+h+">"+t+"</"+h+">"},g=function(t){t.write(v("")),t.close();var e=t.parentWindow.Object;return t=null,e},y=function(){try{r=new ActiveXObject("htmlfile")}catch(t){}var t,e,n;y="undefined"!=typeof document?document.domain&&r?g(r):(e=c("iframe"),n="java"+h+":",e.style.display="none",u.appendChild(e),e.src=String(n),(t=e.contentWindow.document).open(),t.write(v("document.F=Object")),t.close(),t.F):g(r);for(var i=a.length;i--;)delete y[f][a[i]];return y()};s[d]=!0,t.exports=Object.create||function(t,e){var n;return null!==t?(p[f]=i(t),n=new p,p[f]=null,n[d]=t):n=y(),void 0===e?n:o.f(n,e)}},2405:function(t,e,n){"use strict";n(6468)("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),n(6938))},2407:function(t,e,n){"use strict";n.d(e,{F:function(){return m}});var r=n(3029),i=n(2901),o=n(6822),a=n(3954),s=n(5501),u=n(8614),c=n(4467);function l(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(l=function(){return!!t})()}function f(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}var h=function(t){var e;return new Set(null==t||null===(e=t.getAttributeNS)||void 0===e||null===(e=e.call(t,"http://www.idpf.org/2007/ops","type"))||void 0===e?void 0:e.split(" "))},d=function(t){var e;return new Set(null==t||null===(e=t.getAttribute)||void 0===e||null===(e=e.call(t,"role"))||void 0===e?void 0:e.split(" "))},p=function(t){var e=getComputedStyle(t).verticalAlign;return"super"===e||/^\d/.test(e)},v=["biblioref","glossref","noteref"],g=["doc-biblioref","doc-glossref","doc-noteref"],y="a, span, sup, sub, em, strong, i, b, small, big",b=new WeakSet,m=function(t){function e(){var t,n,i,s;(0,r.A)(this,e);for(var u=arguments.length,f=new Array(u),h=0;h<u;h++)f[h]=arguments[h];return n=this,i=e,s=[].concat(f),i=(0,a.A)(i),function(t,e){(function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,e),e.add(t)}(t=(0,o.A)(n,l()?Reflect.construct(i,s||[],(0,a.A)(n).constructor):i.apply(n,s)),b),(0,c.A)(t,"detectFootnotes",!0),t}return(0,s.A)(e,t),(0,i.A)(e,[{key:"handle",value:function(t,e){var n=this,r=e.detail,i=r.a,o=r.href,a=function(t){var e=h(t),n=d(t);return{yes:g.some((function(t){return n.has(t)}))||v.some((function(t){return e.has(t)})),maybe:function(){return!e.has("backlink")&&!n.has("doc-backlink")&&(p(t)||1===t.children.length&&p(t.children[0])||p(t.parentElement))}}}(i),s=a.yes,u=a.maybe;return s?(e.preventDefault(),Promise.resolve(t.resolveHref(o)).then((function(e){return f(b,n,x).call(n,t,e,o)}))):this.detectFootnotes&&u()?(e.preventDefault(),Promise.resolve(t.resolveHref(o)).then((function(e){var r=e.index,i=e.anchor,a={index:r,anchor:function(t){return function(t,e){for(var n=e(t),r=n;n.matches(y);){var i=n.parentElement;if(!i)break;n=i}if(n===t.body){var o=r.nextElementSibling;if(o&&!o.matches(y))return o;throw new Error("Failed to extract footnote")}return n}(t,i)}};return f(b,n,x).call(n,t,a,o)}))):void 0}}])}((0,u.A)(EventTarget));function x(t,e,n){var r=this,i=e.index,o=e.anchor,a=document.createElement("foliate-view");return new Promise((function(e,s){a.addEventListener("load",(function(t){try{var i,u=t.detail.doc,c=o(u),l=function(t){var e=h(t),n=d(t);return n.has("doc-biblioentry")||e.has("biblioentry")?"biblioentry":n.has("definition")||e.has("glossdef")?"definition":n.has("doc-endnote")||e.has("endnote")||e.has("rearnote")?"endnote":n.has("doc-footnote")||e.has("footnote")?"footnote":n.has("note")||e.has("note")?"note":null}(c),f=(null==c||null===(i=c.matches)||void 0===i?void 0:i.call(c,"aside"))&&"footnote"===l;if(c){var p=c.startContainer?c:u.createRange();c.startContainer||(c.matches("li, aside")?p.selectNodeContents(c):p.selectNode(c));var v=p.extractContents();u.body.replaceChildren(),u.body.appendChild(v)}var g={view:a,href:n,type:l,hidden:f,target:c};r.dispatchEvent(new CustomEvent("render",{detail:g})),e()}catch(t){s(t)}})),a.open(t).then((function(){return r.dispatchEvent(new CustomEvent("before-render",{detail:{view:a}}))})).then((function(){return a.goTo(i)})).catch(s)}))}},2419:function(t,e,n){"use strict";n.d(e,{k:function(){return d},r:function(){return v}});var r=n(296),i=n(467),o=n(3029),a=n(2901),s=n(4756),u=n.n(s);function c(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return l(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var f=function(t){var e,n=0,r=function(t){if(t.id=n++,t.subitems){var e,i=c(t.subitems);try{for(i.s();!(e=i.n()).done;){var o=e.value;r(o)}}catch(t){i.e(t)}finally{i.f()}}},i=c(t);try{for(i.s();!(e=i.n()).done;){var o=e.value;r(o)}}catch(t){i.e(t)}finally{i.f()}return t},h=function(t){return t.map((function(t){var e;return null!==(e=t.subitems)&&void 0!==e&&e.length?[t,h(t.subitems)].flat():t})).flat()},d=function(){return(0,a.A)((function t(){(0,o.A)(this,t)}),[{key:"init",value:(t=(0,i.A)(u().mark((function t(e){var n,i,o,a,s,l,d,p,v,g,y,b,m,x,w,k,A,S,E,_,T,O,M,L,C,I,R;return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=e.toc,i=e.ids,o=e.splitHref,a=e.getFragment,f(n),s=h(n),l=new Map,d=c(s.entries()),t.prev=1,d.s();case 2:if((p=d.n()).done){t.next=8;break}return g=(0,r.A)(p.value,2),y=g[0],b=g[1],t.next=3,o(null==b?void 0:b.href);case 3:if(C=v=t.sent,!(L=null!==C)){t.next=4;break}L=void 0!==v;case 4:if(!L){t.next=5;break}I=v,t.next=6;break;case 5:I=[];case 6:m=I,x=(0,r.A)(m,2),w=x[0],k=x[1],A={fragment:k,item:b},l.has(w)?l.get(w).items.push(A):l.set(w,{prev:s[y-1],items:[A]});case 7:t.next=2;break;case 8:t.next=10;break;case 9:t.prev=9,R=t.catch(1),d.e(R);case 10:return t.prev=10,d.f(),t.finish(10);case 11:S=new Map,E=c(i.entries());try{for(E.s();!(_=E.n()).done;)T=(0,r.A)(_.value,2),O=T[0],M=T[1],l.has(M)?S.set(M,l.get(M)):S.set(M,S.get(i[O-1]))}catch(t){E.e(t)}finally{E.f()}this.ids=i,this.map=S,this.getFragment=a;case 12:case"end":return t.stop()}}),t,this,[[1,9,10,11]])}))),function(e){return t.apply(this,arguments)})},{key:"getProgress",value:function(t,e){if(this.ids){var n=this.ids[t],i=this.map.get(n);if(!i)return null;var o=i.prev,a=i.items;if(!a)return o;if(!e||1===a.length&&!a[0].fragment)return a[0].item;var s,u=e.startContainer.getRootNode(),l=c(a.entries());try{for(l.s();!(s=l.n()).done;){var f,h,d=(0,r.A)(s.value,2),p=d[0],v=d[1].fragment,g=this.getFragment(u,v);if(g&&e.comparePoint(g,0)>0)return null!==(f=null===(h=a[p-1])||void 0===h?void 0:h.item)&&void 0!==f?f:o}}catch(t){l.e(t)}finally{l.f()}return a[a.length-1].item}}}]);var t}(),p=new WeakSet,v=function(){return(0,a.A)((function t(e,n,r){var i,a;(0,o.A)(this,t),function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}(i=this,a=p),a.add(i),this.sizes=e.map((function(t){return"no"!=t.linear&&t.size>0?t.size:0})),this.sizePerLoc=n,this.sizePerTimeUnit=r,this.sizeTotal=this.sizes.reduce((function(t,e){return t+e}),0),this.sectionFractions=function(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}(p,this,g).call(this)}),[{key:"getProgress",value:function(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=this.sizes,o=this.sizePerLoc,a=this.sizePerTimeUnit,s=this.sizeTotal,u=null!==(n=i[t])&&void 0!==n?n:0,c=i.slice(0,t).reduce((function(t,e){return t+e}),0)+e*u,l=c+r*u,f=s-c,h=(1-e)*u;return{fraction:l/s,section:{current:t,total:i.length},location:{current:Math.floor(c/o),next:Math.floor(l/o),total:Math.ceil(s/o)},time:{section:h/a,total:f/a}}}},{key:"getSection",value:function(t){if(t<=0)return[0,0];if(t>=1)return[this.sizes.length-1,1];t+=Number.EPSILON;var e=this.sizeTotal,n=this.sectionFractions.findIndex((function(e){return e>t}))-1;if(n<0)return[0,0];for(;!this.sizes[n];)n++;return[n,(t-this.sectionFractions[n])/(this.sizes[n]/e)]}}])}();function g(){var t,e=this.sizeTotal,n=[0],r=0,i=c(this.sizes);try{for(i.s();!(t=i.n()).done;){var o=t.value;n.push((r+=o)/e)}}catch(t){i.e(t)}finally{i.f()}return n}},2478:function(t,e,n){"use strict";var r=n(9504),i=n(8981),o=Math.floor,a=r("".charAt),s=r("".replace),u=r("".slice),c=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,l=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,n,r,f,h){var d=n+t.length,p=r.length,v=l;return void 0!==f&&(f=i(f),v=c),s(h,v,(function(i,s){var c;switch(a(s,0)){case"$":return"$";case"&":return t;case"`":return u(e,0,n);case"'":return u(e,d);case"<":c=f[u(s,1,-1)];break;default:var l=+s;if(0===l)return i;if(l>p){var h=o(l/10);return 0===h?i:h<=p?void 0===r[h-1]?a(s,1):r[h-1]+a(s,1):i}c=r[l-1]}return void 0===c?"":c}))}},2529:function(t){"use strict";t.exports=function(t,e){return{value:t,done:e}}},2551:function(t,e,n){"use strict";var r=n(6395),i=n(4576),o=n(9039),a=n(3607);t.exports=r||!o((function(){if(!(a&&a<535)){var t=Math.random();__defineSetter__.call(null,t,(function(){})),delete i[t]}}))},2568:function(t,e,n){"use strict";var r=n(4644),i=n(2805),o=r.aTypedArrayConstructor;(0,r.exportTypedArrayStaticMethod)("of",(function(){for(var t=0,e=arguments.length,n=new(o(this))(e);e>t;)n[t]=arguments[t++];return n}),i)},2637:function(t,e,n){"use strict";n(6518)({target:"Number",stat:!0},{isInteger:n(2087)})},2652:function(t,e,n){"use strict";var r=n(6080),i=n(9565),o=n(8551),a=n(6823),s=n(4209),u=n(6198),c=n(1625),l=n(81),f=n(851),h=n(9539),d=TypeError,p=function(t,e){this.stopped=t,this.result=e},v=p.prototype;t.exports=function(t,e,n){var g,y,b,m,x,w,k,A=n&&n.that,S=!(!n||!n.AS_ENTRIES),E=!(!n||!n.IS_RECORD),_=!(!n||!n.IS_ITERATOR),T=!(!n||!n.INTERRUPTED),O=r(e,A),M=function(t){return g&&h(g,"normal"),new p(!0,t)},L=function(t){return S?(o(t),T?O(t[0],t[1],M):O(t[0],t[1])):T?O(t,M):O(t)};if(E)g=t.iterator;else if(_)g=t;else{if(!(y=f(t)))throw new d(a(t)+" is not iterable");if(s(y)){for(b=0,m=u(t);m>b;b++)if((x=L(t[b]))&&c(v,x))return x;return new p(!1)}g=l(t,y)}for(w=E?t.next:g.next;!(k=i(w,g)).done;){try{x=L(k.value)}catch(t){h(g,"throw",t)}if("object"==typeof x&&x&&c(v,x))return x}return new p(!1)}},2675:function(t,e,n){"use strict";n(6761),n(1510),n(7812),n(3110),n(9773)},2703:function(t,e,n){"use strict";var r=n(4576),i=n(9039),o=n(9504),a=n(655),s=n(3802).trim,u=n(7452),c=r.parseInt,l=r.Symbol,f=l&&l.iterator,h=/^[+-]?0x/i,d=o(h.exec),p=8!==c(u+"08")||22!==c(u+"0x16")||f&&!i((function(){c(Object(f))}));t.exports=p?function(t,e){var n=s(a(t));return c(n,e>>>0||(d(h,n)?16:10))}:c},2712:function(t,e,n){"use strict";var r=n(6518),i=n(926).left,o=n(4598),a=n(9519);r({target:"Array",proto:!0,forced:!n(6193)&&a>79&&a<83||!o("reduce")},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},2725:function(t,e,n){"use strict";n.r(e),n.d(e,{unzlibSync:function(){return M}});var r=Uint8Array,i=Uint16Array,o=Uint32Array,a=new r([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),s=new r([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),u=new r([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),c=function(t,e){for(var n=new i(31),r=0;r<31;++r)n[r]=e+=1<<t[r-1];var a=new o(n[30]);for(r=1;r<30;++r)for(var s=n[r];s<n[r+1];++s)a[s]=s-n[r]<<5|r;return[n,a]},l=c(a,2),f=l[0],h=l[1];f[28]=258,h[258]=28;for(var d=c(s,0)[0],p=new i(32768),v=0;v<32768;++v){var g=(43690&v)>>>1|(21845&v)<<1;g=(61680&(g=(52428&g)>>>2|(13107&g)<<2))>>>4|(3855&g)<<4,p[v]=((65280&g)>>>8|(255&g)<<8)>>>1}var y=function(t,e,n){for(var r=t.length,o=0,a=new i(e);o<r;++o)t[o]&&++a[t[o]-1];var s,u=new i(e);for(o=0;o<e;++o)u[o]=u[o-1]+a[o-1]<<1;if(n){s=new i(1<<e);var c=15-e;for(o=0;o<r;++o)if(t[o])for(var l=o<<4|t[o],f=e-t[o],h=u[t[o]-1]++<<f,d=h|(1<<f)-1;h<=d;++h)s[p[h]>>>c]=l}else for(s=new i(r),o=0;o<r;++o)t[o]&&(s[o]=p[u[t[o]-1]++]>>>15-t[o]);return s},b=new r(288);for(v=0;v<144;++v)b[v]=8;for(v=144;v<256;++v)b[v]=9;for(v=256;v<280;++v)b[v]=7;for(v=280;v<288;++v)b[v]=8;var m=new r(32);for(v=0;v<32;++v)m[v]=5;var x=y(b,9,1),w=y(m,5,1),k=function(t){for(var e=t[0],n=1;n<t.length;++n)t[n]>e&&(e=t[n]);return e},A=function(t,e,n){var r=e/8|0;return(t[r]|t[r+1]<<8)>>(7&e)&n},S=function(t,e){var n=e/8|0;return(t[n]|t[n+1]<<8|t[n+2]<<16)>>(7&e)},E=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],_=function(t,e,n){var r=new Error(e||E[t]);if(r.code=t,Error.captureStackTrace&&Error.captureStackTrace(r,_),!n)throw r;return r},T=function(t,e,n){var c=t.length;if(!c||n&&n.f&&!n.l)return e||new r(0);var l=!e||n,h=!n||n.i;n||(n={}),e||(e=new r(3*c));var p=function(t){var n=e.length;if(t>n){var i=new r(Math.max(2*n,t));i.set(e),e=i}},v=n.f||0,g=n.p||0,b=n.b||0,m=n.l,E=n.d,T=n.m,O=n.n,M=8*c;do{if(!m){v=A(t,g,1);var L=A(t,g+1,3);if(g+=3,!L){var C=t[(W=4+((g+7)/8|0))-4]|t[W-3]<<8,I=W+C;if(I>c){h&&_(0);break}l&&p(b+C),e.set(t.subarray(W,I),b),n.b=b+=C,n.p=g=8*I,n.f=v;continue}if(1==L)m=x,E=w,T=9,O=5;else if(2==L){var R=A(t,g,31)+257,j=A(t,g+10,15)+4,P=R+A(t,g+5,31)+1;g+=14;for(var F=new r(P),N=new r(19),D=0;D<j;++D)N[u[D]]=A(t,g+3*D,7);g+=3*j;var z=k(N),B=(1<<z)-1,U=y(N,z,1);for(D=0;D<P;){var W,H=U[A(t,g,B)];if(g+=15&H,(W=H>>>4)<16)F[D++]=W;else{var q=0,V=0;for(16==W?(V=3+A(t,g,3),g+=2,q=F[D-1]):17==W?(V=3+A(t,g,7),g+=3):18==W&&(V=11+A(t,g,127),g+=7);V--;)F[D++]=q}}var $=F.subarray(0,R),G=F.subarray(R);T=k($),O=k(G),m=y($,T,1),E=y(G,O,1)}else _(1);if(g>M){h&&_(0);break}}l&&p(b+131072);for(var X=(1<<T)-1,Y=(1<<O)-1,K=g;;K=g){var J=(q=m[S(t,g)&X])>>>4;if((g+=15&q)>M){h&&_(0);break}if(q||_(2),J<256)e[b++]=J;else{if(256==J){K=g,m=null;break}var Z=J-254;if(J>264){var Q=a[D=J-257];Z=A(t,g,(1<<Q)-1)+f[D],g+=Q}var tt=E[S(t,g)&Y],et=tt>>>4;if(tt||_(3),g+=15&tt,G=d[et],et>3&&(Q=s[et],G+=S(t,g)&(1<<Q)-1,g+=Q),g>M){h&&_(0);break}l&&p(b+131072);for(var nt=b+Z;b<nt;b+=4)e[b]=e[b-G],e[b+1]=e[b+1-G],e[b+2]=e[b+2-G],e[b+3]=e[b+3-G];b=nt}}n.l=m,n.p=K,n.b=b,n.f=v,m&&(v=1,n.m=T,n.d=E,n.n=O)}while(!v);return b==e.length?e:function(t,e,n){(null==e||e<0)&&(e=0),(null==n||n>t.length)&&(n=t.length);var a=new(2==t.BYTES_PER_ELEMENT?i:4==t.BYTES_PER_ELEMENT?o:r)(n-e);return a.set(t.subarray(e,n)),a}(e,0,b)},O=new r(0);function M(t,e){return T(((8!=(15&(n=t)[0])||n[0]>>>4>7||(n[0]<<8|n[1])%31)&&_(6,"invalid zlib data"),32&n[1]&&_(6,"invalid zlib data: preset dictionaries not supported"),t.subarray(2,-4)),e);var n}var L="undefined"!=typeof TextDecoder&&new TextDecoder;try{L.decode(O,{stream:!0})}catch(r){}},2744:function(t,e,n){"use strict";var r=n(9039);t.exports=!r((function(){return Object.isExtensible(Object.preventExtensions({}))}))},2762:function(t,e,n){"use strict";var r=n(6518),i=n(3802).trim;r({target:"String",proto:!0,forced:n(706)("trim")},{trim:function(){return i(this)}})},2777:function(t,e,n){"use strict";var r=n(9565),i=n(34),o=n(757),a=n(5966),s=n(4270),u=n(8227),c=TypeError,l=u("toPrimitive");t.exports=function(t,e){if(!i(t)||o(t))return t;var n,u=a(t,l);if(u){if(void 0===e&&(e="default"),n=r(u,t,e),!i(n)||o(n))return n;throw new c("Can't convert object to primitive value")}return void 0===e&&(e="number"),s(t,e)}},2781:function(t,e,n){"use strict";n(6518)({target:"String",proto:!0},{repeat:n(2333)})},2787:function(t,e,n){"use strict";var r=n(9297),i=n(4901),o=n(8981),a=n(6119),s=n(2211),u=a("IE_PROTO"),c=Object,l=c.prototype;t.exports=s?c.getPrototypeOf:function(t){var e=o(t);if(r(e,u))return e[u];var n=e.constructor;return i(n)&&e instanceof n?n.prototype:e instanceof c?l:null}},2796:function(t,e,n){"use strict";var r=n(9039),i=n(4901),o=/#|\.prototype\./,a=function(t,e){var n=u[s(t)];return n===l||n!==c&&(i(e)?r(e):!!e)},s=a.normalize=function(t){return String(t).replace(o,".").toLowerCase()},u=a.data={},c=a.NATIVE="N",l=a.POLYFILL="P";t.exports=a},2805:function(t,e,n){"use strict";var r=n(4576),i=n(9039),o=n(4428),a=n(4644).NATIVE_ARRAY_BUFFER_VIEWS,s=r.ArrayBuffer,u=r.Int8Array;t.exports=!a||!i((function(){u(1)}))||!i((function(){new u(-1)}))||!o((function(t){new u,new u(null),new u(1.5),new u(t)}),!0)||i((function(){return 1!==new u(new s(2),1,void 0).length}))},2811:function(t,e,n){"use strict";var r=n(6518),i=n(2744),o=n(9039),a=n(34),s=n(3451).onFreeze,u=Object.freeze;r({target:"Object",stat:!0,forced:o((function(){u(1)})),sham:!i},{freeze:function(t){return u&&a(t)?u(s(t)):t}})},2812:function(t){"use strict";var e=TypeError;t.exports=function(t,n){if(t<n)throw new e("Not enough arguments");return t}},2839:function(t,e,n){"use strict";var r=n(4576).navigator,i=r&&r.userAgent;t.exports=i?String(i):""},2887:function(t,e,n){"use strict";var r=n(4576),i=n(9039),o=n(9504),a=n(4644),s=n(3792),u=n(8227)("iterator"),c=r.Uint8Array,l=o(s.values),f=o(s.keys),h=o(s.entries),d=a.aTypedArray,p=a.exportTypedArrayMethod,v=c&&c.prototype,g=!i((function(){v[u].call([1])})),y=!!v&&v.values&&v[u]===v.values&&"values"===v.values.name,b=function(){return l(d(this))};p("entries",(function(){return h(d(this))}),g),p("keys",(function(){return f(d(this))}),g),p("values",b,g||!y,{name:"values"}),p(u,b,g||!y,{name:"values"})},2892:function(t,e,n){"use strict";var r=n(6518),i=n(6395),o=n(3724),a=n(4576),s=n(9167),u=n(9504),c=n(2796),l=n(9297),f=n(3167),h=n(1625),d=n(757),p=n(2777),v=n(9039),g=n(8480).f,y=n(7347).f,b=n(4913).f,m=n(1240),x=n(3802).trim,w="Number",k=a[w],A=s[w],S=k.prototype,E=a.TypeError,_=u("".slice),T=u("".charCodeAt),O=c(w,!k(" 0o1")||!k("0b1")||k("+0x1")),M=function(t){var e,n=arguments.length<1?0:k(function(t){var e=p(t,"number");return"bigint"==typeof e?e:function(t){var e,n,r,i,o,a,s,u,c=p(t,"number");if(d(c))throw new E("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=x(c),43===(e=T(c,0))||45===e){if(88===(n=T(c,2))||120===n)return NaN}else if(48===e){switch(T(c,1)){case 66:case 98:r=2,i=49;break;case 79:case 111:r=8,i=55;break;default:return+c}for(a=(o=_(c,2)).length,s=0;s<a;s++)if((u=T(o,s))<48||u>i)return NaN;return parseInt(o,r)}return+c}(e)}(t));return h(S,e=this)&&v((function(){m(e)}))?f(Object(n),this,M):n};M.prototype=S,O&&!i&&(S.constructor=M),r({global:!0,constructor:!0,wrap:!0,forced:O},{Number:M});var L=function(t,e){for(var n,r=o?g(e):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),i=0;r.length>i;i++)l(e,n=r[i])&&!l(t,n)&&b(t,n,y(e,n))};i&&A&&L(s[w],A),(O||i)&&L(s[w],k)},2901:function(t,e,n){"use strict";n.d(e,{A:function(){return o}});var r=n(816);function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(0,r.A)(i.key),i)}}function o(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}},2953:function(t,e,n){"use strict";var r=n(4576),i=n(7400),o=n(9296),a=n(3792),s=n(6699),u=n(687),c=n(8227)("iterator"),l=a.values,f=function(t,e){if(t){if(t[c]!==l)try{s(t,c,l)}catch(e){t[c]=l}if(u(t,e,!0),i[e])for(var n in a)if(t[n]!==a[n])try{s(t,n,a[n])}catch(e){t[n]=a[n]}}};for(var h in i)f(r[h]&&r[h].prototype,h);f(o,"DOMTokenList")},2967:function(t,e,n){"use strict";var r=n(6706),i=n(34),o=n(7750),a=n(3506);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,n={};try{(t=r(Object.prototype,"__proto__","set"))(n,[]),e=n instanceof Array}catch(t){}return function(n,r){return o(n),a(r),i(n)?(e?t(n,r):n.__proto__=r,n):n}}():void 0)},3029:function(t,e,n){"use strict";function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}n.d(e,{A:function(){return r}})},3063:function(t,e,n){"use strict";var r=n(2839);t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(r)},3110:function(t,e,n){"use strict";var r=n(6518),i=n(7751),o=n(8745),a=n(9565),s=n(9504),u=n(9039),c=n(4901),l=n(757),f=n(7680),h=n(6933),d=n(4495),p=String,v=i("JSON","stringify"),g=s(/./.exec),y=s("".charAt),b=s("".charCodeAt),m=s("".replace),x=s(1.1.toString),w=/[\uD800-\uDFFF]/g,k=/^[\uD800-\uDBFF]$/,A=/^[\uDC00-\uDFFF]$/,S=!d||u((function(){var t=i("Symbol")("stringify detection");return"[null]"!==v([t])||"{}"!==v({a:t})||"{}"!==v(Object(t))})),E=u((function(){return'"\\udf06\\ud834"'!==v("\udf06\ud834")||'"\\udead"'!==v("\udead")})),_=function(t,e){var n=f(arguments),r=h(e);if(c(r)||void 0!==t&&!l(t))return n[1]=function(t,e){if(c(r)&&(e=a(r,this,p(t),e)),!l(e))return e},o(v,null,n)},T=function(t,e,n){var r=y(n,e-1),i=y(n,e+1);return g(k,t)&&!g(A,i)||g(A,t)&&!g(k,r)?"\\u"+x(b(t,0),16):t};v&&r({target:"JSON",stat:!0,arity:3,forced:S||E},{stringify:function(t,e,n){var r=f(arguments),i=o(S?_:v,null,r);return E&&"string"==typeof i?m(i,w,T):i}})},3138:function(t){"use strict";t.exports=function(t,e){try{1===arguments.length?console.error(t):console.error(t,e)}catch(t){}}},3145:function(t,e,n){"use strict";function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}n.d(e,{A:function(){return r}})},3153:function(t,e,n){"use strict";var r=n(6518),i=n(5359),o=Math.acosh,a=Math.log,s=Math.sqrt,u=Math.LN2;r({target:"Math",stat:!0,forced:!o||710!==Math.floor(o(Number.MAX_VALUE))||o(1/0)!==1/0},{acosh:function(t){var e=+t;return e<1?NaN:e>94906265.62425156?a(e)+u:i(e-1+s(e-1)*s(e+1))}})},3164:function(t,e,n){"use strict";var r=n(7782),i=n(3602),o=Math.abs;t.exports=function(t,e,n,a){var s=+t,u=o(s),c=r(s);if(u<a)return c*i(u/a/e)*a*e;var l=(1+e/2220446049250313e-31)*u,f=l-(l-u);return f>n||f!=f?c*(1/0):c*f}},3167:function(t,e,n){"use strict";var r=n(4901),i=n(34),o=n(2967);t.exports=function(t,e,n){var a,s;return o&&r(a=e.constructor)&&a!==n&&i(s=a.prototype)&&s!==n.prototype&&o(t,s),t}},3179:function(t,e,n){"use strict";var r=n(2140),i=n(6955);t.exports=r?{}.toString:function(){return"[object "+i(this)+"]"}},3206:function(t,e,n){"use strict";var r=n(4644),i=n(9213).forEach,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("forEach",(function(t){i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},3234:function(t,e,n){"use strict";n.r(e),n.d(e,{BlobReader:function(){return Me},BlobWriter:function(){return Le},TextWriter:function(){return Ce},ZipReader:function(){return cn},configure:function(){return Z}});var r=n(4467),i=n(991),o=n(296),a=n(467),s=n(9417),u=n(2284),c=n(6822),l=n(3954),f=n(5501),h=n(2901),d=n(3029),p=n(6218),v=n(600),g=n(4756),y=n.n(g);function b(t,e,n,r){var o=(0,i.A)((0,l.A)(1&r?t.prototype:t),e,n);return 2&r&&"function"==typeof o?function(t){return o.apply(n,t)}:o}function m(t,e,n){return e=(0,l.A)(e),(0,c.A)(t,x()?Reflect.construct(e,n||[],(0,l.A)(t).constructor):e.apply(t,n))}function x(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(x=function(){return!!t})()}function w(t){var e,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(e=t[n]))return e.call(t);if(r&&null!=(e=t[r]))return new k(e.call(t));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function k(t){function e(t){if(Object(t)!==t)return Promise.reject(new TypeError(t+" is not an object."));var e=t.done;return Promise.resolve(t.value).then((function(t){return{value:t,done:e}}))}return k=function(t){this.s=t,this.n=t.next},k.prototype={s:null,n:null,next:function(){return e(this.n.apply(this.s,arguments))},return:function(t){var n=this.s.return;return void 0===n?Promise.resolve({value:t,done:!0}):e(n.apply(this.s,arguments))},throw:function(t){var n=this.s.return;return void 0===n?Promise.reject(t):e(n.apply(this.s,arguments))}},new k(t)}var A=-2,S=-3,E=-5,_=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],T=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],O=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],M=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],L=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],C=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],I=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function R(){var t,e,n,r,i,o;function a(t,e,a,s,u,c,l,f,h,d,p){var v,g,y,b,m,x,w,k,A,_,T,O,M,L,C;_=0,m=a;do{n[t[e+_]]++,_++,m--}while(0!==m);if(n[0]==a)return l[0]=-1,f[0]=0,0;for(k=f[0],x=1;x<=15&&0===n[x];x++);for(w=x,k<x&&(k=x),m=15;0!==m&&0===n[m];m--);for(y=m,k>m&&(k=m),f[0]=k,L=1<<x;x<m;x++,L<<=1)if((L-=n[x])<0)return S;if((L-=n[m])<0)return S;for(n[m]+=L,o[1]=x=0,_=1,M=2;0!=--m;)o[M]=x+=n[_],M++,_++;m=0,_=0;do{0!==(x=t[e+_])&&(p[o[x]++]=m),_++}while(++m<a);for(a=o[y],o[0]=m=0,_=0,b=-1,O=-k,i[0]=0,T=0,C=0;w<=y;w++)for(v=n[w];0!=v--;){for(;w>O+k;){if(b++,C=(C=y-(O+=k))>k?k:C,(g=1<<(x=w-O))>v+1&&(g-=v+1,M=w,x<C))for(;++x<C&&!((g<<=1)<=n[++M]);)g-=n[M];if(C=1<<x,d[0]+C>1440)return S;i[b]=T=d[0],d[0]+=C,0!==b?(o[b]=m,r[0]=x,r[1]=k,x=m>>>O-k,r[2]=T-i[b-1]-x,h.set(r,3*(i[b-1]+x))):l[0]=T}for(r[1]=w-O,_>=a?r[0]=192:p[_]<s?(r[0]=p[_]<256?0:96,r[2]=p[_++]):(r[0]=c[p[_]-s]+16+64,r[2]=u[p[_++]-s]),g=1<<w-O,x=m>>>O;x<C;x+=g)h.set(r,3*(T+x));for(x=1<<w-1;m&x;x>>>=1)m^=x;for(m^=x,A=(1<<O)-1;(m&A)!=o[b];)b--,A=(1<<(O-=k))-1}return 0!==L&&1!=y?E:0}function s(a){var s;for(t||(t=[],e=[],n=new Int32Array(16),r=[],i=new Int32Array(15),o=new Int32Array(16)),e.length<a&&(e=[]),s=0;s<a;s++)e[s]=0;for(s=0;s<16;s++)n[s]=0;for(s=0;s<3;s++)r[s]=0;i.set(n.subarray(0,15),0),o.set(n.subarray(0,16),0)}this.inflate_trees_bits=function(n,r,i,o,u){var c;return s(19),t[0]=0,(c=a(n,0,19,19,null,null,i,r,o,t,e))==S?u.msg="oversubscribed dynamic bit lengths tree":c!=E&&0!==r[0]||(u.msg="incomplete dynamic bit lengths tree",c=S),c},this.inflate_trees_dynamic=function(n,r,i,o,u,c,l,f,h){var d;return s(288),t[0]=0,0!=(d=a(i,0,n,257,M,L,c,o,f,t,e))||0===o[0]?(d==S?h.msg="oversubscribed literal/length tree":-4!=d&&(h.msg="incomplete literal/length tree",d=S),d):(s(288),0!=(d=a(i,n,r,0,C,I,l,u,f,t,e))||0===u[0]&&n>257?(d==S?h.msg="oversubscribed distance tree":d==E?(h.msg="incomplete distance tree",d=S):-4!=d&&(h.msg="empty distance tree with lengths",d=S),d):0)}}function j(){var t,e,n,r,i=this,o=0,a=0,s=0,u=0,c=0,l=0,f=0,h=0,d=0,p=0;function v(t,e,n,r,i,o,a,s){var u,c,l,f,h,d,p,v,g,y,b,m,x,w,k,A;p=s.next_in_index,v=s.avail_in,h=a.bitb,d=a.bitk,y=(g=a.write)<a.read?a.read-g-1:a.end-g,b=_[t],m=_[e];do{for(;d<20;)v--,h|=(255&s.read_byte(p++))<<d,d+=8;if(0!==(f=(c=n)[A=3*((l=r)+(u=h&b))]))for(;;){if(h>>=c[A+1],d-=c[A+1],16&f){for(f&=15,x=c[A+2]+(h&_[f]),h>>=f,d-=f;d<15;)v--,h|=(255&s.read_byte(p++))<<d,d+=8;for(f=(c=i)[A=3*((l=o)+(u=h&m))];;){if(h>>=c[A+1],d-=c[A+1],16&f){for(f&=15;d<f;)v--,h|=(255&s.read_byte(p++))<<d,d+=8;if(w=c[A+2]+(h&_[f]),h>>=f,d-=f,y-=x,g>=w)g-(k=g-w)>0&&2>g-k?(a.win[g++]=a.win[k++],a.win[g++]=a.win[k++],x-=2):(a.win.set(a.win.subarray(k,k+2),g),g+=2,k+=2,x-=2);else{k=g-w;do{k+=a.end}while(k<0);if(x>(f=a.end-k)){if(x-=f,g-k>0&&f>g-k)do{a.win[g++]=a.win[k++]}while(0!=--f);else a.win.set(a.win.subarray(k,k+f),g),g+=f,k+=f,f=0;k=0}}if(g-k>0&&x>g-k)do{a.win[g++]=a.win[k++]}while(0!=--x);else a.win.set(a.win.subarray(k,k+x),g),g+=x,k+=x,x=0;break}if(64&f)return s.msg="invalid distance code",v+=x=d>>3<(x=s.avail_in-v)?d>>3:x,p-=x,d-=x<<3,a.bitb=h,a.bitk=d,s.avail_in=v,s.total_in+=p-s.next_in_index,s.next_in_index=p,a.write=g,S;u+=c[A+2],f=c[A=3*(l+(u+=h&_[f]))]}break}if(64&f)return 32&f?(v+=x=d>>3<(x=s.avail_in-v)?d>>3:x,p-=x,d-=x<<3,a.bitb=h,a.bitk=d,s.avail_in=v,s.total_in+=p-s.next_in_index,s.next_in_index=p,a.write=g,1):(s.msg="invalid literal/length code",v+=x=d>>3<(x=s.avail_in-v)?d>>3:x,p-=x,d-=x<<3,a.bitb=h,a.bitk=d,s.avail_in=v,s.total_in+=p-s.next_in_index,s.next_in_index=p,a.write=g,S);if(u+=c[A+2],0===(f=c[A=3*(l+(u+=h&_[f]))])){h>>=c[A+1],d-=c[A+1],a.win[g++]=c[A+2],y--;break}}else h>>=c[A+1],d-=c[A+1],a.win[g++]=c[A+2],y--}while(y>=258&&v>=10);return v+=x=d>>3<(x=s.avail_in-v)?d>>3:x,p-=x,d-=x<<3,a.bitb=h,a.bitk=d,s.avail_in=v,s.total_in+=p-s.next_in_index,s.next_in_index=p,a.write=g,0}i.init=function(i,o,a,s,u,c){t=0,f=i,h=o,n=a,d=s,r=u,p=c,e=null},i.proc=function(i,g,y){var b,m,x,w,k,E,T,O=0,M=0,L=0;for(L=g.next_in_index,w=g.avail_in,O=i.bitb,M=i.bitk,E=(k=i.write)<i.read?i.read-k-1:i.end-k;;)switch(t){case 0:if(E>=258&&w>=10&&(i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,y=v(f,h,n,d,r,p,i,g),L=g.next_in_index,w=g.avail_in,O=i.bitb,M=i.bitk,E=(k=i.write)<i.read?i.read-k-1:i.end-k,0!=y)){t=1==y?7:9;break}s=f,e=n,a=d,t=1;case 1:for(b=s;M<b;){if(0===w)return i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);y=0,w--,O|=(255&g.read_byte(L++))<<M,M+=8}if(O>>>=e[1+(m=3*(a+(O&_[b])))],M-=e[m+1],0===(x=e[m])){u=e[m+2],t=6;break}if(16&x){c=15&x,o=e[m+2],t=2;break}if(!(64&x)){s=x,a=m/3+e[m+2];break}if(32&x){t=7;break}return t=9,g.msg="invalid literal/length code",y=S,i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);case 2:for(b=c;M<b;){if(0===w)return i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);y=0,w--,O|=(255&g.read_byte(L++))<<M,M+=8}o+=O&_[b],O>>=b,M-=b,s=h,e=r,a=p,t=3;case 3:for(b=s;M<b;){if(0===w)return i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);y=0,w--,O|=(255&g.read_byte(L++))<<M,M+=8}if(O>>=e[1+(m=3*(a+(O&_[b])))],M-=e[m+1],16&(x=e[m])){c=15&x,l=e[m+2],t=4;break}if(!(64&x)){s=x,a=m/3+e[m+2];break}return t=9,g.msg="invalid distance code",y=S,i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);case 4:for(b=c;M<b;){if(0===w)return i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);y=0,w--,O|=(255&g.read_byte(L++))<<M,M+=8}l+=O&_[b],O>>=b,M-=b,t=5;case 5:for(T=k-l;T<0;)T+=i.end;for(;0!==o;){if(0===E&&(k==i.end&&0!==i.read&&(E=(k=0)<i.read?i.read-k-1:i.end-k),0===E&&(i.write=k,y=i.inflate_flush(g,y),E=(k=i.write)<i.read?i.read-k-1:i.end-k,k==i.end&&0!==i.read&&(E=(k=0)<i.read?i.read-k-1:i.end-k),0===E)))return i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);i.win[k++]=i.win[T++],E--,T==i.end&&(T=0),o--}t=0;break;case 6:if(0===E&&(k==i.end&&0!==i.read&&(E=(k=0)<i.read?i.read-k-1:i.end-k),0===E&&(i.write=k,y=i.inflate_flush(g,y),E=(k=i.write)<i.read?i.read-k-1:i.end-k,k==i.end&&0!==i.read&&(E=(k=0)<i.read?i.read-k-1:i.end-k),0===E)))return i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);y=0,i.win[k++]=u,E--,t=0;break;case 7:if(M>7&&(M-=8,w++,L--),i.write=k,y=i.inflate_flush(g,y),E=(k=i.write)<i.read?i.read-k-1:i.end-k,i.read!=i.write)return i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);t=8;case 8:return y=1,i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);case 9:return y=S,i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y);default:return y=A,i.bitb=O,i.bitk=M,g.avail_in=w,g.total_in+=L-g.next_in_index,g.next_in_index=L,i.write=k,i.inflate_flush(g,y)}},i.free=function(){}}R.inflate_trees_fixed=function(t,e,n,r){return t[0]=9,e[0]=5,n[0]=T,r[0]=O,0};var P=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function F(t,e){var n,r=this,i=0,o=0,a=0,s=0,u=[0],c=[0],l=new j,f=0,h=new Int32Array(4320),d=new R;r.bitk=0,r.bitb=0,r.win=new Uint8Array(e),r.end=e,r.read=0,r.write=0,r.reset=function(t,e){e&&(e[0]=0),6==i&&l.free(t),i=0,r.bitk=0,r.bitb=0,r.read=r.write=0},r.reset(t,null),r.inflate_flush=function(t,e){var n,i,o;return i=t.next_out_index,(n=((o=r.read)<=r.write?r.write:r.end)-o)>t.avail_out&&(n=t.avail_out),0!==n&&e==E&&(e=0),t.avail_out-=n,t.total_out+=n,t.next_out.set(r.win.subarray(o,o+n),i),i+=n,(o+=n)==r.end&&(o=0,r.write==r.end&&(r.write=0),(n=r.write-o)>t.avail_out&&(n=t.avail_out),0!==n&&e==E&&(e=0),t.avail_out-=n,t.total_out+=n,t.next_out.set(r.win.subarray(o,o+n),i),i+=n,o+=n),t.next_out_index=i,r.read=o,e},r.proc=function(t,e){var p,v,g,y,b,m,x,w;for(y=t.next_in_index,b=t.avail_in,v=r.bitb,g=r.bitk,x=(m=r.write)<r.read?r.read-m-1:r.end-m;;){var k=void 0,E=void 0,T=void 0,O=void 0,M=void 0,L=void 0,C=void 0,I=void 0;switch(i){case 0:for(;g<3;){if(0===b)return r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);e=0,b--,v|=(255&t.read_byte(y++))<<g,g+=8}switch(f=1&(p=7&v),p>>>1){case 0:v>>>=3,v>>>=p=7&(g-=3),g-=p,i=1;break;case 1:k=[],E=[],T=[[]],O=[[]],R.inflate_trees_fixed(k,E,T,O),l.init(k[0],E[0],T[0],0,O[0],0),v>>>=3,g-=3,i=6;break;case 2:v>>>=3,g-=3,i=3;break;case 3:return v>>>=3,g-=3,i=9,t.msg="invalid block type",e=S,r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e)}break;case 1:for(;g<32;){if(0===b)return r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);e=0,b--,v|=(255&t.read_byte(y++))<<g,g+=8}if((~v>>>16&65535)!=(65535&v))return i=9,t.msg="invalid stored block lengths",e=S,r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);o=65535&v,v=g=0,i=0!==o?2:0!==f?7:0;break;case 2:if(0===b)return r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);if(0===x&&(m==r.end&&0!==r.read&&(x=(m=0)<r.read?r.read-m-1:r.end-m),0===x&&(r.write=m,e=r.inflate_flush(t,e),x=(m=r.write)<r.read?r.read-m-1:r.end-m,m==r.end&&0!==r.read&&(x=(m=0)<r.read?r.read-m-1:r.end-m),0===x)))return r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);if(e=0,(p=o)>b&&(p=b),p>x&&(p=x),r.win.set(t.read_buf(y,p),m),y+=p,b-=p,m+=p,x-=p,0!=(o-=p))break;i=0!==f?7:0;break;case 3:for(;g<14;){if(0===b)return r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);e=0,b--,v|=(255&t.read_byte(y++))<<g,g+=8}if(a=p=16383&v,(31&p)>29||(p>>5&31)>29)return i=9,t.msg="too many length or distance symbols",e=S,r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);if(p=258+(31&p)+(p>>5&31),!n||n.length<p)n=[];else for(w=0;w<p;w++)n[w]=0;v>>>=14,g-=14,s=0,i=4;case 4:for(;s<4+(a>>>10);){for(;g<3;){if(0===b)return r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);e=0,b--,v|=(255&t.read_byte(y++))<<g,g+=8}n[P[s++]]=7&v,v>>>=3,g-=3}for(;s<19;)n[P[s++]]=0;if(u[0]=7,0!=(p=d.inflate_trees_bits(n,u,c,h,t)))return(e=p)==S&&(n=null,i=9),r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);s=0,i=5;case 5:for(;!(s>=258+(31&(p=a))+(p>>5&31));){var j=void 0,F=void 0;for(p=u[0];g<p;){if(0===b)return r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);e=0,b--,v|=(255&t.read_byte(y++))<<g,g+=8}if(p=h[3*(c[0]+(v&_[p]))+1],(F=h[3*(c[0]+(v&_[p]))+2])<16)v>>>=p,g-=p,n[s++]=F;else{for(w=18==F?7:F-14,j=18==F?11:3;g<p+w;){if(0===b)return r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);e=0,b--,v|=(255&t.read_byte(y++))<<g,g+=8}if(g-=p,j+=(v>>>=p)&_[w],v>>>=w,g-=w,(w=s)+j>258+(31&(p=a))+(p>>5&31)||16==F&&w<1)return n=null,i=9,t.msg="invalid bit length repeat",e=S,r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);F=16==F?n[w-1]:0;do{n[w++]=F}while(0!=--j);s=w}}if(c[0]=-1,L=[],C=[],I=[],(M=[])[0]=9,L[0]=6,p=a,0!=(p=d.inflate_trees_dynamic(257+(31&p),1+(p>>5&31),n,M,L,C,I,h,t)))return p==S&&(n=null,i=9),e=p,r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);l.init(M[0],L[0],h,C[0],h,I[0]),i=6;case 6:if(r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,1!=(e=l.proc(r,t,e)))return r.inflate_flush(t,e);if(e=0,l.free(t),y=t.next_in_index,b=t.avail_in,v=r.bitb,g=r.bitk,x=(m=r.write)<r.read?r.read-m-1:r.end-m,0===f){i=0;break}i=7;case 7:if(r.write=m,e=r.inflate_flush(t,e),x=(m=r.write)<r.read?r.read-m-1:r.end-m,r.read!=r.write)return r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);i=8;case 8:return e=1,r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);case 9:return e=S,r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e);default:return e=A,r.bitb=v,r.bitk=g,t.avail_in=b,t.total_in+=y-t.next_in_index,t.next_in_index=y,r.write=m,r.inflate_flush(t,e)}}},r.free=function(t){r.reset(t,null),r.win=null,h=null},r.set_dictionary=function(t,e,n){r.win.set(t.subarray(e,e+n),0),r.read=r.write=n},r.sync_point=function(){return 1==i?1:0}}var N=13,D=[0,0,255,255];function z(){var t=this;function e(t){return t&&t.istate?(t.total_in=t.total_out=0,t.msg=null,t.istate.mode=7,t.istate.blocks.reset(t,null),0):A}t.mode=0,t.method=0,t.was=[0],t.need=0,t.marker=0,t.wbits=0,t.inflateEnd=function(e){return t.blocks&&t.blocks.free(e),t.blocks=null,0},t.inflateInit=function(n,r){return n.msg=null,t.blocks=null,r<8||r>15?(t.inflateEnd(n),A):(t.wbits=r,n.istate.blocks=new F(n,1<<r),e(n),0)},t.inflate=function(t,e){var n,r;if(!t||!t.istate||!t.next_in)return A;var i=t.istate;for(e=4==e?E:0,n=E;;)switch(i.mode){case 0:if(0===t.avail_in)return n;if(n=e,t.avail_in--,t.total_in++,8!=(15&(i.method=t.read_byte(t.next_in_index++)))){i.mode=N,t.msg="unknown compression method",i.marker=5;break}if(8+(i.method>>4)>i.wbits){i.mode=N,t.msg="invalid win size",i.marker=5;break}i.mode=1;case 1:if(0===t.avail_in)return n;if(n=e,t.avail_in--,t.total_in++,r=255&t.read_byte(t.next_in_index++),((i.method<<8)+r)%31!=0){i.mode=N,t.msg="incorrect header check",i.marker=5;break}if(!(32&r)){i.mode=7;break}i.mode=2;case 2:if(0===t.avail_in)return n;n=e,t.avail_in--,t.total_in++,i.need=(255&t.read_byte(t.next_in_index++))<<24&4278190080,i.mode=3;case 3:if(0===t.avail_in)return n;n=e,t.avail_in--,t.total_in++,i.need+=(255&t.read_byte(t.next_in_index++))<<16&16711680,i.mode=4;case 4:if(0===t.avail_in)return n;n=e,t.avail_in--,t.total_in++,i.need+=(255&t.read_byte(t.next_in_index++))<<8&65280,i.mode=5;case 5:return 0===t.avail_in?n:(n=e,t.avail_in--,t.total_in++,i.need+=255&t.read_byte(t.next_in_index++),i.mode=6,2);case 6:return i.mode=N,t.msg="need dictionary",i.marker=0,A;case 7:if((n=i.blocks.proc(t,n))==S){i.mode=N,i.marker=0;break}if(0==n&&(n=e),1!=n)return n;n=e,i.blocks.reset(t,i.was),i.mode=12;case 12:return t.avail_in=0,1;case N:return S;default:return A}},t.inflateSetDictionary=function(t,e,n){var r=0,i=n;if(!t||!t.istate||6!=t.istate.mode)return A;var o=t.istate;return i>=1<<o.wbits&&(r=n-(i=(1<<o.wbits)-1)),o.blocks.set_dictionary(e,r,i),o.mode=7,0},t.inflateSync=function(t){var n,r,i,o,a;if(!t||!t.istate)return A;var s=t.istate;if(s.mode!=N&&(s.mode=N,s.marker=0),0===(n=t.avail_in))return E;for(r=t.next_in_index,i=s.marker;0!==n&&i<4;)t.read_byte(r)==D[i]?i++:i=0!==t.read_byte(r)?0:4-i,r++,n--;return t.total_in+=r-t.next_in_index,t.next_in_index=r,t.avail_in=n,s.marker=i,4!=i?S:(o=t.total_in,a=t.total_out,e(t),t.total_in=o,t.total_out=a,s.mode=7,0)},t.inflateSyncPoint=function(t){return t&&t.istate&&t.istate.blocks?t.istate.blocks.sync_point():A}}function B(){}B.prototype={inflateInit:function(t){var e=this;return e.istate=new z,t||(t=15),e.istate.inflateInit(e,t)},inflate:function(t){var e=this;return e.istate?e.istate.inflate(e,t):A},inflateEnd:function(){var t=this;if(!t.istate)return A;var e=t.istate.inflateEnd(t);return t.istate=null,e},inflateSync:function(){var t=this;return t.istate?t.istate.inflateSync(t):A},inflateSetDictionary:function(t,e){var n=this;return n.istate?n.istate.inflateSetDictionary(n,t,e):A},read_byte:function(t){return this.next_in[t]},read_buf:function(t,e){return this.next_in.subarray(t,t+e)}};var U=4294967295,W=65535,H=33639248,q=101075792,V=void 0,$="undefined",G="function",X=(0,h.A)((function t(e){return(0,d.A)(this,t),function(t){function n(t,r){(0,d.A)(this,n);var i=new e(r);return m(this,n,[{transform:function(t,e){e.enqueue(i.append(t))},flush:function(t){var e=i.flush();e&&t.enqueue(e)}}])}return(0,f.A)(n,t),(0,h.A)(n)}(TransformStream)})),Y=2;try{("undefined"==typeof navigator?"undefined":(0,u.A)(navigator))!=$&&navigator.hardwareConcurrency&&(Y=navigator.hardwareConcurrency)}catch(t){}var K={chunkSize:524288,maxWorkers:Y,terminateWorkerTimeout:5e3,useWebWorkers:!0,useCompressionStream:!0,workerScripts:V,CompressionStreamNative:("undefined"==typeof CompressionStream?"undefined":(0,u.A)(CompressionStream))!=$&&CompressionStream,DecompressionStreamNative:("undefined"==typeof DecompressionStream?"undefined":(0,u.A)(DecompressionStream))!=$&&DecompressionStream},J=Object.assign({},K);function Z(t){var e=t.baseURL,n=t.chunkSize,r=t.maxWorkers,i=t.terminateWorkerTimeout,o=t.useCompressionStream,a=t.useWebWorkers,s=t.Deflate,u=t.Inflate,c=t.CompressionStream,l=t.DecompressionStream,f=t.workerScripts;if(Q("baseURL",e),Q("chunkSize",n),Q("maxWorkers",r),Q("terminateWorkerTimeout",i),Q("useCompressionStream",o),Q("useWebWorkers",a),s&&(J.CompressionStream=new X(s)),u&&(J.DecompressionStream=new X(u)),Q("CompressionStream",c),Q("DecompressionStream",l),f!==V){var h=f.deflate,d=f.inflate;if((h||d)&&(J.workerScripts||(J.workerScripts={})),h){if(!Array.isArray(h))throw new Error("workerScripts.deflate must be an array");J.workerScripts.deflate=h}if(d){if(!Array.isArray(d))throw new Error("workerScripts.inflate must be an array");J.workerScripts.inflate=d}}}function Q(t,e){e!==V&&(J[t]=e)}for(var tt=[],et=0;et<256;et++){for(var nt=et,rt=0;rt<8;rt++)1&nt?nt=nt>>>1^3988292384:nt>>>=1;tt[et]=nt}var it=function(){return(0,h.A)((function t(e){(0,d.A)(this,t),this.crc=e||-1}),[{key:"append",value:function(t){for(var e=0|this.crc,n=0,r=0|t.length;n<r;n++)e=e>>>8^tt[255&(e^t[n])];this.crc=e}},{key:"get",value:function(){return~this.crc}}])}(),ot=function(t){function e(){var t,n;(0,d.A)(this,e);var r=new it;return t=m(this,e,[{transform:function(t,e){r.append(t),e.enqueue(t)},flush:function(){var t=new Uint8Array(4);new DataView(t.buffer).setUint32(0,r.get()),n.value=t}}]),n=(0,s.A)(t),t}return(0,f.A)(e,t),(0,h.A)(e)}(TransformStream),at={concat:function(t,e){if(0===t.length||0===e.length)return t.concat(e);var n=t[t.length-1],r=at.getPartial(n);return 32===r?t.concat(e):at._shiftRight(e,r,0|n,t.slice(0,t.length-1))},bitLength:function(t){var e=t.length;if(0===e)return 0;var n=t[e-1];return 32*(e-1)+at.getPartial(n)},clamp:function(t,e){if(32*t.length<e)return t;var n=(t=t.slice(0,Math.ceil(e/32))).length;return e&=31,n>0&&e&&(t[n-1]=at.partial(e,t[n-1]&2147483648>>e-1,1)),t},partial:function(t,e,n){return 32===t?e:(n?0|e:e<<32-t)+1099511627776*t},getPartial:function(t){return Math.round(t/1099511627776)||32},_shiftRight:function(t,e,n,r){for(void 0===r&&(r=[]);e>=32;e-=32)r.push(n),n=0;if(0===e)return r.concat(t);for(var i=0;i<t.length;i++)r.push(n|t[i]>>>e),n=t[i]<<32-e;var o=t.length?t[t.length-1]:0,a=at.getPartial(o);return r.push(at.partial(e+a&31,e+a>32?n:r.pop(),1)),r}},st={bytes:{fromBits:function(t){for(var e,n=at.bitLength(t)/8,r=new Uint8Array(n),i=0;i<n;i++)!(3&i)&&(e=t[i/4]),r[i]=e>>>24,e<<=8;return r},toBits:function(t){var e,n=[],r=0;for(e=0;e<t.length;e++)r=r<<8|t[e],!(3&~e)&&(n.push(r),r=0);return 3&e&&n.push(at.partial(8*(3&e),r)),n}}},ut={sha1:function(){return(0,h.A)((function t(e){(0,d.A)(this,t);var n=this;n.blockSize=512,n._init=[1732584193,4023233417,2562383102,271733878,3285377520],n._key=[1518500249,1859775393,2400959708,3395469782],e?(n._h=e._h.slice(0),n._buffer=e._buffer.slice(0),n._length=e._length):n.reset()}),[{key:"reset",value:function(){var t=this;return t._h=t._init.slice(0),t._buffer=[],t._length=0,t}},{key:"update",value:function(t){var e=this;"string"==typeof t&&(t=st.utf8String.toBits(t));var n=e._buffer=at.concat(e._buffer,t),r=e._length,i=e._length=r+at.bitLength(t);if(i>9007199254740991)throw new Error("Cannot hash more than 2^53 - 1 bits");for(var o=new Uint32Array(n),a=0,s=e.blockSize+r-(e.blockSize+r&e.blockSize-1);s<=i;s+=e.blockSize)e._block(o.subarray(16*a,16*(a+1))),a+=1;return n.splice(0,16*a),e}},{key:"finalize",value:function(){for(var t=this,e=t._buffer,n=t._h,r=(e=at.concat(e,[at.partial(1,1)])).length+2;15&r;r++)e.push(0);for(e.push(Math.floor(t._length/4294967296)),e.push(0|t._length);e.length;)t._block(e.splice(0,16));return t.reset(),n}},{key:"_f",value:function(t,e,n,r){return t<=19?e&n|~e&r:t<=39?e^n^r:t<=59?e&n|e&r|n&r:t<=79?e^n^r:void 0}},{key:"_S",value:function(t,e){return e<<t|e>>>32-t}},{key:"_block",value:function(t){for(var e=this,n=e._h,r=Array(80),i=0;i<16;i++)r[i]=t[i];for(var o=n[0],a=n[1],s=n[2],u=n[3],c=n[4],l=0;l<=79;l++){l>=16&&(r[l]=e._S(1,r[l-3]^r[l-8]^r[l-14]^r[l-16]));var f=e._S(5,o)+e._f(l,a,s,u)+c+r[l]+e._key[Math.floor(l/20)]|0;c=u,u=s,s=e._S(30,a),a=o,o=f}n[0]=n[0]+o|0,n[1]=n[1]+a|0,n[2]=n[2]+s|0,n[3]=n[3]+u|0,n[4]=n[4]+c|0}}])}()},ct={aes:function(){return(0,h.A)((function t(e){(0,d.A)(this,t);var n=this;n._tables=[[[],[],[],[],[]],[[],[],[],[],[]]],n._tables[0][0][0]||n._precompute();var r,i,o,a=n._tables[0][4],s=n._tables[1],u=e.length,c=1;if(4!==u&&6!==u&&8!==u)throw new Error("invalid aes key size");for(n._key=[i=e.slice(0),o=[]],r=u;r<4*u+28;r++){var l=i[r-1];(r%u==0||8===u&&r%u==4)&&(l=a[l>>>24]<<24^a[l>>16&255]<<16^a[l>>8&255]<<8^a[255&l],r%u==0&&(l=l<<8^l>>>24^c<<24,c=c<<1^283*(c>>7))),i[r]=i[r-u]^l}for(var f=0;r;f++,r--){var h=i[3&f?r:r-4];o[f]=r<=4||f<4?h:s[0][a[h>>>24]]^s[1][a[h>>16&255]]^s[2][a[h>>8&255]]^s[3][a[255&h]]}}),[{key:"encrypt",value:function(t){return this._crypt(t,0)}},{key:"decrypt",value:function(t){return this._crypt(t,1)}},{key:"_precompute",value:function(){for(var t,e,n,r=this._tables[0],i=this._tables[1],o=r[4],a=i[4],s=[],u=[],c=0;c<256;c++)u[(s[c]=c<<1^283*(c>>7))^c]=c;for(var l=t=0;!o[l];l^=e||1,t=u[t]||1){var f=t^t<<1^t<<2^t<<3^t<<4;f=f>>8^255&f^99,o[l]=f,a[f]=l;for(var h=16843009*s[n=s[e=s[l]]]^65537*n^257*e^16843008*l,d=257*s[f]^16843008*f,p=0;p<4;p++)r[p][l]=d=d<<24^d>>>8,i[p][f]=h=h<<24^h>>>8}for(var v=0;v<5;v++)r[v]=r[v].slice(0),i[v]=i[v].slice(0)}},{key:"_crypt",value:function(t,e){if(4!==t.length)throw new Error("invalid aes block size");for(var n,r,i,o=this._key[e],a=o.length/4-2,s=[0,0,0,0],u=this._tables[e],c=u[0],l=u[1],f=u[2],h=u[3],d=u[4],p=t[0]^o[0],v=t[e?3:1]^o[1],g=t[2]^o[2],y=t[e?1:3]^o[3],b=4,m=0;m<a;m++)n=c[p>>>24]^l[v>>16&255]^f[g>>8&255]^h[255&y]^o[b],r=c[v>>>24]^l[g>>16&255]^f[y>>8&255]^h[255&p]^o[b+1],i=c[g>>>24]^l[y>>16&255]^f[p>>8&255]^h[255&v]^o[b+2],y=c[y>>>24]^l[p>>16&255]^f[v>>8&255]^h[255&g]^o[b+3],b+=4,p=n,v=r,g=i;for(var x=0;x<4;x++)s[e?3&-x:x]=d[p>>>24]<<24^d[v>>16&255]<<16^d[g>>8&255]<<8^d[255&y]^o[b++],n=p,p=v,v=g,g=y,y=n;return s}}])}()},lt={ctrGladman:function(){return(0,h.A)((function t(e,n){(0,d.A)(this,t),this._prf=e,this._initIv=n,this._iv=n}),[{key:"reset",value:function(){this._iv=this._initIv}},{key:"update",value:function(t){return this.calculate(this._prf,t,this._iv)}},{key:"incWord",value:function(t){if(255&~(t>>24))t+=1<<24;else{var e=t>>16&255,n=t>>8&255,r=255&t;255===e?(e=0,255===n?(n=0,255===r?r=0:++r):++n):++e,t=0,t+=e<<16,t+=n<<8,t+=r}return t}},{key:"incCounter",value:function(t){0===(t[0]=this.incWord(t[0]))&&(t[1]=this.incWord(t[1]))}},{key:"calculate",value:function(t,e,n){var r;if(!(r=e.length))return[];for(var i=at.bitLength(e),o=0;o<r;o+=4){this.incCounter(n);var a=t.encrypt(n);e[o]^=a[0],e[o+1]^=a[1],e[o+2]^=a[2],e[o+3]^=a[3]}return at.clamp(e,i)}}])}()},ft={importKey:function(t){return new ft.hmacSha1(st.bytes.toBits(t))},pbkdf2:function(t,e,n,r){if(n=n||1e4,r<0||n<0)throw new Error("invalid params to pbkdf2");var i,o,a,s,u,c=1+(r>>5)<<2,l=new ArrayBuffer(c),f=new DataView(l),h=0,d=at;for(e=st.bytes.toBits(e),u=1;h<(c||1);u++){for(i=o=t.encrypt(d.concat(e,[u])),a=1;a<n;a++)for(o=t.encrypt(o),s=0;s<o.length;s++)i[s]^=o[s];for(a=0;h<(c||1)&&a<i.length;a++)f.setInt32(h,i[a]),h+=4}return l.slice(0,r/8)},hmacSha1:function(){return(0,h.A)((function t(e){(0,d.A)(this,t);var n=this,r=n._hash=ut.sha1,i=[[],[]];n._baseHash=[new r,new r];var o=n._baseHash[0].blockSize/32;e.length>o&&(e=(new r).update(e).finalize());for(var a=0;a<o;a++)i[0][a]=909522486^e[a],i[1][a]=1549556828^e[a];n._baseHash[0].update(i[0]),n._baseHash[1].update(i[1]),n._resultHash=new r(n._baseHash[0])}),[{key:"reset",value:function(){var t=this;t._resultHash=new t._hash(t._baseHash[0]),t._updated=!1}},{key:"update",value:function(t){this._updated=!0,this._resultHash.update(t)}},{key:"digest",value:function(){var t=this,e=t._resultHash.finalize(),n=new t._hash(t._baseHash[1]).update(e).finalize();return t.reset(),n}},{key:"encrypt",value:function(t){if(this._updated)throw new Error("encrypt on already updated hmac called!");return this.update(t),this.digest(t)}}])}()},ht="undefined"!=typeof crypto&&"function"==typeof crypto.getRandomValues,dt="Invalid password",pt="Invalid signature",vt="zipjs-abort-check-password";function gt(t){return ht?crypto.getRandomValues(t):function(t){for(var e,n=new Uint32Array(t.buffer),r=function(t){var e=987654321,n=4294967295;return function(){return((((e=36969*(65535&e)+(e>>16)&n)<<16)+(t=18e3*(65535&t)+(t>>16)&n)&n)/4294967296+.5)*(Math.random()>.5?1:-1)}},i=0;i<t.length;i+=4){var o=r(4294967296*(e||Math.random()));e=987654071*o(),n[i/4]=4294967296*o()|0}return t}(t)}var yt=16,bt={name:"PBKDF2"},mt=Object.assign({hash:{name:"HMAC"}},bt),xt=Object.assign({iterations:1e3,hash:{name:"SHA-1"}},bt),wt=["deriveBits"],kt=[8,12,16],At=[16,24,32],St=10,Et=[0,0,0,0],_t="undefined",Tt="function",Ot=("undefined"==typeof crypto?"undefined":(0,u.A)(crypto))!=_t,Mt=Ot&&crypto.subtle,Lt=Ot&&(0,u.A)(Mt)!=_t,Ct=st.bytes,It=ct.aes,Rt=lt.ctrGladman,jt=ft.hmacSha1,Pt=Ot&&Lt&&(0,u.A)(Mt.importKey)==Tt,Ft=Ot&&Lt&&(0,u.A)(Mt.deriveBits)==Tt,Nt=function(t){function e(t){var n=t.password,r=t.signed,i=t.encryptionStrength,o=t.checkPasswordOnly;return(0,d.A)(this,e),m(this,e,[{start:function(){var t=this;Object.assign(this,{ready:new Promise((function(e){return t.resolveReady=e})),password:n,signed:r,strength:i-1,pending:new Uint8Array})},transform:function(t,e){var n=this;return(0,a.A)(y().mark((function r(){var i,s,u,c,l,f;return y().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(s=(i=n).password,u=i.strength,c=i.resolveReady,l=i.ready,!s){r.next=2;break}return r.next=1,function(){var t=(0,a.A)(y().mark((function t(e,n,r,i){var o,a;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,Bt(e,n,r,Ht(i,0,kt[n]));case 1:if(o=t.sent,a=Ht(i,kt[n]),o[0]==a[0]&&o[1]==a[1]){t.next=2;break}throw new Error(dt);case 2:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()(i,u,s,Ht(t,0,kt[u]+2));case 1:t=Ht(t,kt[u]+2),o?e.error(new Error(vt)):c(),r.next=3;break;case 2:return r.next=3,l;case 3:f=new Uint8Array(t.length-St-(t.length-St)%yt),e.enqueue(zt(i,t,f,0,St,!0));case 4:case"end":return r.stop()}}),r)})))()},flush:function(t){var e=this;return(0,a.A)(y().mark((function n(){var r,i,o,a,s,u,c,l,f,h,d,p;return y().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(r=e.signed,i=e.ctr,o=e.hmac,a=e.pending,s=e.ready,!o||!i){n.next=5;break}return n.next=1,s;case 1:if(u=Ht(a,0,a.length-St),c=Ht(a,a.length-St),l=new Uint8Array,u.length&&(f=Vt(Ct,u),o.update(f),h=i.update(f),l=qt(Ct,h)),!r){n.next=4;break}d=Ht(qt(Ct,o.digest()),0,St),p=0;case 2:if(!(p<St)){n.next=4;break}if(d[p]==c[p]){n.next=3;break}throw new Error(pt);case 3:p++,n.next=2;break;case 4:t.enqueue(l);case 5:case"end":return n.stop()}}),n)})))()}}])}return(0,f.A)(e,t),(0,h.A)(e)}(TransformStream),Dt=function(t){function e(t){var n,r,i=t.password,o=t.encryptionStrength;return(0,d.A)(this,e),n=m(this,e,[{start:function(){var t=this;Object.assign(this,{ready:new Promise((function(e){return t.resolveReady=e})),password:i,strength:o-1,pending:new Uint8Array})},transform:function(t,e){var n=this;return(0,a.A)(y().mark((function r(){var i,o,s,u,c,l,f;return y().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(o=(i=n).password,s=i.strength,u=i.resolveReady,c=i.ready,l=new Uint8Array,!o){r.next=2;break}return r.next=1,function(){var t=(0,a.A)(y().mark((function t(e,n,r){var i,o;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=gt(new Uint8Array(kt[n])),t.next=1,Bt(e,n,r,i);case 1:return o=t.sent,t.abrupt("return",Wt(i,o));case 2:case"end":return t.stop()}}),t)})));return function(e,n,r){return t.apply(this,arguments)}}()(i,s,o);case 1:l=r.sent,u(),r.next=3;break;case 2:return r.next=3,c;case 3:(f=new Uint8Array(l.length+t.length-t.length%yt)).set(l,0),e.enqueue(zt(i,t,f,l.length,0));case 4:case"end":return r.stop()}}),r)})))()},flush:function(t){var e=this;return(0,a.A)(y().mark((function n(){var i,o,a,s,u,c;return y().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(i=e.ctr,o=e.hmac,a=e.pending,s=e.ready,!o||!i){n.next=2;break}return n.next=1,s;case 1:u=new Uint8Array,a.length&&(c=i.update(Vt(Ct,a)),o.update(c),u=qt(Ct,c)),r.signature=qt(Ct,o.digest()).slice(0,St),t.enqueue(Wt(u,r.signature));case 2:case"end":return n.stop()}}),n)})))()}}]),r=(0,s.A)(n),n}return(0,f.A)(e,t),(0,h.A)(e)}(TransformStream);function zt(t,e,n,r,i,o){var a,s=t.ctr,u=t.hmac,c=t.pending,l=e.length-i;for(c.length&&(e=Wt(c,e),n=function(t,e){if(e&&e>t.length){var n=t;(t=new Uint8Array(e)).set(n,0)}return t}(n,l-l%yt)),a=0;a<=l-yt;a+=yt){var f=Vt(Ct,Ht(e,a,a+yt));o&&u.update(f);var h=s.update(f);o||u.update(h),n.set(qt(Ct,h),a+r)}return t.pending=Ht(e,a),n}function Bt(t,e,n,r){return Ut.apply(this,arguments)}function Ut(){return Ut=(0,a.A)(y().mark((function t(e,n,r,i){var o,s,u,c,l,f,h;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.password=null,o=function(t){if("undefined"==typeof TextEncoder){t=unescape(encodeURIComponent(t));for(var e=new Uint8Array(t.length),n=0;n<e.length;n++)e[n]=t.charCodeAt(n);return e}return(new TextEncoder).encode(t)}(r),t.next=1,function(){var t=(0,a.A)(y().mark((function t(e,n,r,i,o){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(Pt){t.next=1;break}return t.abrupt("return",ft.importKey(n));case 1:return t.prev=1,t.next=2,Mt.importKey(e,n,r,i,o);case 2:return t.abrupt("return",t.sent);case 3:return t.prev=3,t.catch(1),t.abrupt("return",(Pt=!1,ft.importKey(n)));case 4:case"end":return t.stop()}}),t,null,[[1,3]])})));return function(e,n,r,i,o){return t.apply(this,arguments)}}()("raw",o,mt,!1,wt);case 1:return s=t.sent,t.next=2,function(){var t=(0,a.A)(y().mark((function t(e,n,r){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(Ft){t.next=1;break}return t.abrupt("return",ft.pbkdf2(n,e.salt,xt.iterations,r));case 1:return t.prev=1,t.next=2,Mt.deriveBits(e,n,r);case 2:return t.abrupt("return",t.sent);case 3:return t.prev=3,t.catch(1),t.abrupt("return",(Ft=!1,ft.pbkdf2(n,e.salt,xt.iterations,r)));case 4:case"end":return t.stop()}}),t,null,[[1,3]])})));return function(e,n,r){return t.apply(this,arguments)}}()(Object.assign({salt:i},xt),s,8*(2*At[n]+2));case 2:return u=t.sent,c=new Uint8Array(u),l=Vt(Ct,Ht(c,0,At[n])),f=Vt(Ct,Ht(c,At[n],2*At[n])),h=Ht(c,2*At[n]),t.abrupt("return",(Object.assign(e,{keys:{key:l,authentication:f,passwordVerification:h},ctr:new Rt(new It(l),Array.from(Et)),hmac:new jt(f)}),h));case 3:case"end":return t.stop()}}),t)}))),Ut.apply(this,arguments)}function Wt(t,e){var n=t;return t.length+e.length&&((n=new Uint8Array(t.length+e.length)).set(t,0),n.set(e,t.length)),n}function Ht(t,e,n){return t.subarray(e,n)}function qt(t,e){return t.fromBits(e)}function Vt(t,e){return t.toBits(e)}var $t=function(t){function e(t){var n=t.password,r=t.passwordVerification,i=t.checkPasswordOnly;return(0,d.A)(this,e),m(this,e,[{start:function(){Object.assign(this,{password:n,passwordVerification:r}),Kt(this,n)},transform:function(t,e){var n=this;if(n.password){var r=Xt(n,t.subarray(0,12));if(n.password=null,r[11]!=n.passwordVerification)throw new Error(dt);t=t.subarray(12)}i?e.error(new Error(vt)):e.enqueue(Xt(n,t))}}])}return(0,f.A)(e,t),(0,h.A)(e)}(TransformStream),Gt=function(t){function e(t){var n=t.password,r=t.passwordVerification;return(0,d.A)(this,e),m(this,e,[{start:function(){Object.assign(this,{password:n,passwordVerification:r}),Kt(this,n)},transform:function(t,e){var n,r,i=this;if(i.password){i.password=null;var o=gt(new Uint8Array(12));o[11]=i.passwordVerification,(n=new Uint8Array(t.length+o.length)).set(Yt(i,o),0),r=12}else n=new Uint8Array(t.length),r=0;n.set(Yt(i,t),r),e.enqueue(n)}}])}return(0,f.A)(e,t),(0,h.A)(e)}(TransformStream);function Xt(t,e){for(var n=new Uint8Array(e.length),r=0;r<e.length;r++)n[r]=Zt(t)^e[r],Jt(t,n[r]);return n}function Yt(t,e){for(var n=new Uint8Array(e.length),r=0;r<e.length;r++)n[r]=Zt(t)^e[r],Jt(t,e[r]);return n}function Kt(t,e){var n=[305419896,591751049,878082192];Object.assign(t,{keys:n,crcKey0:new it(n[0]),crcKey2:new it(n[2])});for(var r=0;r<e.length;r++)Jt(t,e.charCodeAt(r))}function Jt(t,e){var n=(0,o.A)(t.keys,3),r=n[0],i=n[1],a=n[2];t.crcKey0.append([e]),r=~t.crcKey0.get(),i=te(Math.imul(te(i+Qt(r)),134775813)+1),t.crcKey2.append([i>>>24]),a=~t.crcKey2.get(),t.keys=[r,i,a]}function Zt(t){var e=2|t.keys[2];return Qt(Math.imul(e,1^e)>>>8)}function Qt(t){return 255&t}function te(t){return 4294967295&t}var ee="deflate-raw",ne=function(t){function e(t,n){var r,i=n.chunkSize,o=n.CompressionStream,a=n.CompressionStreamNative;(0,d.A)(this,e),r=m(this,e,[{}]);var s,u,c=t.compressed,l=t.encrypted,f=t.useCompressionStream,h=t.zipCrypto,p=t.signed,v=t.level,g=r,y=ie(b(e,"readable",r,1));return l&&!h||!p||(y=se(y,s=new ot)),c&&(y=ae(y,f,{level:v,chunkSize:i},a,o)),l&&(y=se(y,h?new Gt(t):u=new Dt(t))),oe(g,y,(function(){var t;l&&!h&&(t=u.signature),l&&!h||!p||(t=new DataView(s.value.buffer).getUint32(0)),g.signature=t})),r}return(0,f.A)(e,t),(0,h.A)(e)}(TransformStream),re=function(t){function e(t,n){var r,i=n.chunkSize,o=n.DecompressionStream,a=n.DecompressionStreamNative;(0,d.A)(this,e),r=m(this,e,[{}]);var s,u=t.zipCrypto,c=t.encrypted,l=t.signed,f=t.signature,h=t.compressed,p=t.useCompressionStream,v=ie(b(e,"readable",r,1));return c&&(v=se(v,u?new $t(t):new Nt(t))),h&&(v=ae(v,p,{chunkSize:i},a,o)),c&&!u||!l||(v=se(v,s=new ot)),oe(r,v,(function(){if((!c||u)&&l){var t=new DataView(s.value.buffer);if(f!=t.getUint32(0,!1))throw new Error(pt)}})),r}return(0,f.A)(e,t),(0,h.A)(e)}(TransformStream);function ie(t){return se(t,new TransformStream({transform:function(t,e){t&&t.length&&e.enqueue(t)}}))}function oe(t,e,n){e=se(e,new TransformStream({flush:n})),Object.defineProperty(t,"readable",{get:function(){return e}})}function ae(t,e,n,r,i){try{t=se(t,new(e&&r?r:i)(ee,n))}catch(r){if(!e)throw r;t=se(t,new i(ee,n))}return t}function se(t,e){return t.pipeThrough(e)}var ue="data",ce="inflate",le=function(t){function e(t,n){var r;(0,d.A)(this,e);var i,o=r=m(this,e,[{}]),a=t.codecType;a.startsWith("deflate")?i=ne:a.startsWith(ce)&&(i=re);var s=0,u=new i(t,n),c=b(e,"readable",r,1),l=new TransformStream({transform:function(t,e){t&&t.length&&(s+=t.length,e.enqueue(t))},flush:function(){var t=u.signature;Object.assign(o,{signature:t,size:s})}});return Object.defineProperty(o,"readable",{get:function(){return c.pipeThrough(u).pipeThrough(l)}}),r}return(0,f.A)(e,t),(0,h.A)(e)}(TransformStream),fe=("undefined"==typeof Worker?"undefined":(0,u.A)(Worker))!=$,he=(0,h.A)((function t(e,n,r,i){var o=n.readable,a=n.writable,s=r.options,u=r.config,c=r.streamOptions,l=r.useWebWorkers,f=r.transferStreams,h=r.scripts;(0,d.A)(this,t);var p=c.signal;return Object.assign(e,{busy:!0,readable:o.pipeThrough(new de(o,c,u),{signal:p}),writable:a,options:Object.assign({},s),scripts:h,transferStreams:f,terminate:function(){var t=e.worker,n=e.busy;t&&!n&&(t.terminate(),e.interface=null)},onTaskFinished:function(){e.busy=!1,i(e)}}),(l&&fe?ye:ge)(e,u)})),de=function(t){function e(t,n,r){var i=n.onstart,o=n.onprogress,s=n.size,u=n.onend,c=r.chunkSize;(0,d.A)(this,e);var l=0;return m(this,e,[{start:function(){i&&pe(i,s)},transform:function(t,e){return(0,a.A)(y().mark((function n(){return y().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(l+=t.length,!o){n.next=1;break}return n.next=1,pe(o,l,s);case 1:e.enqueue(t);case 2:case"end":return n.stop()}}),n)})))()},flush:function(){t.size=l,u&&pe(u,l)}},{highWaterMark:1,size:function(){return c}}])}return(0,f.A)(e,t),(0,h.A)(e)}(TransformStream);function pe(t){return ve.apply(this,arguments)}function ve(){return ve=(0,a.A)(y().mark((function t(e){var n,r,i,o=arguments;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(t.prev=0,n=o.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=o[i];return t.next=1,e.apply(void 0,r);case 1:t.next=3;break;case 2:t.prev=2,t.catch(0);case 3:case"end":return t.stop()}}),t,null,[[0,2]])}))),ve.apply(this,arguments)}function ge(t,e){return{run:function(){return(n=(0,a.A)(y().mark((function t(e,n){var r,i,o,a,s,u,c;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.options,i=e.readable,o=e.writable,a=e.onTaskFinished,s=new le(r,n),t.prev=1,t.next=2,i.pipeThrough(s).pipeTo(o,{preventClose:!0,preventAbort:!0});case 2:return u=s.signature,c=s.size,t.abrupt("return",{signature:u,size:c});case 3:return t.prev=3,a(),t.finish(3);case 4:case"end":return t.stop()}}),t,null,[[1,,3,4]])}))),function(t,e){return n.apply(this,arguments)})(t,e);var n}}}function ye(t,e){var n=e.baseURL,r=e.chunkSize;return t.interface||Object.assign(t,{worker:xe(t.scripts[0],n,t),interface:{run:function(){return(e=(0,a.A)(y().mark((function t(e,n){var r,i,o,s,u,c,l,f,h,d;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o=new Promise((function(t,e){r=t,i=e})),Object.assign(e,{reader:null,writer:null,resolveResult:r,rejectResult:i,result:o}),s=e.readable,u=e.options,c=e.scripts,l=function(t){var e,n=t.getWriter(),r=new Promise((function(t){return e=t})),i=new WritableStream({write:function(t){return(0,a.A)(y().mark((function e(){return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=1,n.ready;case 1:return e.next=2,n.write(t);case 2:case"end":return e.stop()}}),e)})))()},close:function(){n.releaseLock(),e()},abort:function(t){return n.abort(t)}});return{writable:i,closed:r}}(e.writable),f=l.writable,h=l.closed,we({type:"start",scripts:c.slice(1),options:u,config:n,readable:s,writable:f},e)||Object.assign(e,{reader:s.getReader(),writer:f.getWriter()}),t.next=1,o;case 1:return d=t.sent,t.prev=2,t.next=3,f.getWriter().close();case 3:t.next=5;break;case 4:t.prev=4,t.catch(2);case 5:return t.next=6,h;case 6:return t.abrupt("return",d);case 7:case"end":return t.stop()}}),t,null,[[2,4]])}))),function(t,n){return e.apply(this,arguments)})(t,{chunkSize:r});var e}}}),t.interface}var be=!0,me=!0;function xe(t,e,n){var r,i,o={type:"module"};(0,u.A)(t)==G&&(t=t());try{r=new URL(t,e)}catch(e){r=t}if(be)try{i=new Worker(r)}catch(t){be=!1,i=new Worker(r,o)}else i=new Worker(r,o);return i.addEventListener("message",(function(t){return(e=(0,a.A)(y().mark((function t(e,n){var r,i,o,a,s,u,c,l,f,h,d,p,v,g,b,m,x,w,k,A,S;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(A=function(t,e){t?h(t):f(e),l&&l.releaseLock(),d()},r=e.data,i=r.type,o=r.value,a=r.messageId,s=r.result,u=r.error,c=n.reader,l=n.writer,f=n.resolveResult,h=n.rejectResult,d=n.onTaskFinished,t.prev=1,!u){t.next=2;break}p=u.message,v=u.stack,g=u.code,b=u.name,m=new Error(p),Object.assign(m,{stack:v,code:g,name:b}),A(m),t.next=8;break;case 2:if("pull"!=i){t.next=4;break}return t.next=3,c.read();case 3:x=t.sent,w=x.value,k=x.done,we({type:ue,value:w,done:k,messageId:a},n);case 4:if(i!=ue){t.next=7;break}return t.next=5,l.ready;case 5:return t.next=6,l.write(new Uint8Array(o));case 6:we({type:"ack",messageId:a},n);case 7:"close"==i&&A(null,s);case 8:t.next=10;break;case 9:t.prev=9,S=t.catch(1),A(S);case 10:case"end":return t.stop()}}),t,null,[[1,9]])}))),function(t,n){return e.apply(this,arguments)})(t,n);var e})),i}function we(t,e){var n=e.worker,r=e.writer,i=e.onTaskFinished,o=e.transferStreams;try{var a=t.value,s=t.readable,u=t.writable,c=[];if(a&&(t.value=a.buffer,c.push(t.value)),o&&me?(s&&c.push(s),u&&c.push(u)):t.readable=t.writable=null,c.length)try{return n.postMessage(t,c),!0}catch(r){me=!1,t.readable=t.writable=null,n.postMessage(t)}else n.postMessage(t)}catch(t){throw r&&r.releaseLock(),i(),t}}var ke=[],Ae=[],Se=0;function Ee(t){var e=t.terminateTimeout;e&&(clearTimeout(e),t.terminateTimeout=null)}var _e="writable",Te=function(){return(0,h.A)((function t(){(0,d.A)(this,t),this.size=0}),[{key:"init",value:function(){this.initialized=!0}}])}(),Oe=function(t){function e(){return(0,d.A)(this,e),m(this,e,arguments)}return(0,f.A)(e,t),(0,h.A)(e,[{key:"readable",get:function(){var t=this,e=t.chunkSize,n=void 0===e?65536:e,r=new ReadableStream({start:function(){this.chunkOffset=0},pull:function(e){var i=this;return(0,a.A)(y().mark((function o(){var a,s,u,c,l,f,h;return y().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return a=r.offset,s=void 0===a?0:a,u=r.size,c=r.diskNumberStart,l=i.chunkOffset,f=e,o.next=1,Ne(t,s+l,Math.min(n,u-l),c);case 1:h=o.sent,f.enqueue.call(f,h),l+n>u?e.close():i.chunkOffset+=n;case 2:case"end":return o.stop()}}),o)})))()}});return r}}])}(Te),Me=function(t){function e(t){var n;return(0,d.A)(this,e),n=m(this,e),Object.assign((0,s.A)(n),{blob:t,size:t.size}),n}return(0,f.A)(e,t),(0,h.A)(e,[{key:"readUint8Array",value:(n=(0,a.A)(y().mark((function t(e,n){var r,i,o,a;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=this,i=e+n,o=e||i<r.size?r.blob.slice(e,i):r.blob,t.next=1,o.arrayBuffer();case 1:return a=t.sent,t.abrupt("return",(a.byteLength>n&&(a=a.slice(e,i)),new Uint8Array(a)));case 2:case"end":return t.stop()}}),t,this)}))),function(t,e){return n.apply(this,arguments)})}]);var n}(Oe),Le=function(t){function e(t){var n;(0,d.A)(this,e),n=m(this,e);var r=new TransformStream,i=[];return t&&i.push(["Content-Type",t]),Object.defineProperty(n,_e,{get:function(){return r.writable}}),n.blob=new Response(r.readable,{headers:i}).blob(),n}return(0,f.A)(e,t),(0,h.A)(e,[{key:"getData",value:function(){return this.blob}}])}(Te),Ce=function(t){function e(t){var n;return(0,d.A)(this,e),n=m(this,e,[t]),Object.assign((0,s.A)(n),{encoding:t,utf8:!t||"utf-8"==t.toLowerCase()}),n}return(0,f.A)(e,t),(0,h.A)(e,[{key:"getData",value:(n=(0,a.A)(y().mark((function t(){var n,r,i,o;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=this.encoding,r=this.utf8,t.next=1,b(e,"getData",this,3)([]);case 1:if(!(i=t.sent).text||!r){t.next=2;break}return t.abrupt("return",i.text());case 2:return o=new FileReader,t.abrupt("return",new Promise((function(t,e){Object.assign(o,{onload:function(e){var n=e.target;return t(n.result)},onerror:function(){return e(o.error)}}),o.readAsText(i,n)})));case 3:case"end":return t.stop()}}),t,this)}))),function(){return n.apply(this,arguments)})}]);var n}(Le),Ie=function(t){function e(t){var n;return(0,d.A)(this,e),(n=m(this,e)).readers=t,n}return(0,f.A)(e,t),(0,h.A)(e,[{key:"init",value:(r=(0,a.A)(y().mark((function t(){var n,r;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=(n=this).readers,n.lastDiskNumber=0,n.lastDiskOffset=0,t.next=1,Promise.all(r.map(function(){var t=(0,a.A)(y().mark((function t(e,i){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,e.init();case 1:i!=r.length-1&&(n.lastDiskOffset+=e.size),n.size+=e.size;case 2:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()));case 1:b(e,"init",this,3)([]);case 2:case"end":return t.stop()}}),t,this)}))),function(){return r.apply(this,arguments)})},{key:"readUint8Array",value:(n=(0,a.A)(y().mark((function t(e,n){var r,i,o,a,s,u,c,l,f,h,d,p,v,g,b=arguments;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(r=b.length>2&&void 0!==b[2]?b[2]:0,i=this,o=this.readers,-1==(s=r)&&(s=o.length-1),u=e;u>=o[s].size;)u-=o[s].size,s++;if(c=o[s],l=c.size,!(u+n<=l)){t.next=2;break}return t.next=1,Ne(c,u,n);case 1:a=t.sent,t.next=5;break;case 2:return f=l-u,a=new Uint8Array(n),h=a,t.next=3,Ne(c,u,f);case 3:return d=t.sent,h.set.call(h,d),p=a,t.next=4,i.readUint8Array(e+f,n-f,r);case 4:v=t.sent,g=f,p.set.call(p,v,g);case 5:return t.abrupt("return",(i.lastDiskNumber=Math.max(s,i.lastDiskNumber),a));case 6:case"end":return t.stop()}}),t,this)}))),function(t,e){return n.apply(this,arguments)})}]);var n,r}(Oe),Re=function(t){function e(t){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4294967295;(0,d.A)(this,e);var i,o,s,u=n=m(this,e);Object.assign(u,{diskNumber:0,diskOffset:0,size:0,maxSize:r,availableSize:r});var c=new WritableStream({write:function(e){var n=this;return(0,a.A)(y().mark((function r(){var a,c,f;return y().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(a=u.availableSize,!s){r.next=6;break}if(!(e.length>=a)){r.next=4;break}return r.next=1,l(e.slice(0,a));case 1:return r.next=2,h();case 2:return u.diskOffset+=i.size,u.diskNumber++,s=null,r.next=3,n.write(e.slice(a));case 3:r.next=5;break;case 4:return r.next=5,l(e);case 5:r.next=10;break;case 6:return r.next=7,t.next();case 7:if(c=r.sent,f=c.value,!c.done||f){r.next=8;break}throw new Error("Writer iterator completed too soon");case 8:return(i=f).size=0,i.maxSize&&(u.maxSize=i.maxSize),u.availableSize=u.maxSize,r.next=9,je(i);case 9:return o=f.writable,s=o.getWriter(),r.next=10,n.write(e);case 10:case"end":return r.stop()}}),r)})))()},close:function(){return(0,a.A)(y().mark((function t(){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,s.ready;case 1:return t.next=2,h();case 2:case"end":return t.stop()}}),t)})))()}});function l(t){return f.apply(this,arguments)}function f(){return f=(0,a.A)(y().mark((function t(e){var n;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(n=e.length)){t.next=3;break}return t.next=1,s.ready;case 1:return t.next=2,s.write(e);case 2:i.size+=n,u.size+=n,u.availableSize-=n;case 3:case"end":return t.stop()}}),t)}))),f.apply(this,arguments)}function h(){return p.apply(this,arguments)}function p(){return(p=(0,a.A)(y().mark((function t(){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o.size=i.size,t.next=1,s.close();case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}return Object.defineProperty(u,_e,{get:function(){return c}}),n}return(0,f.A)(e,t),(0,h.A)(e)}(Te);function je(t,e){return Pe.apply(this,arguments)}function Pe(){return(Pe=(0,a.A)(y().mark((function t(e,n){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.init||e.initialized){t.next=1;break}return t.next=1,e.init(n);case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function Fe(t){return Array.isArray(t)&&(t=new Ie(t)),t instanceof ReadableStream&&(t={readable:t}),t}function Ne(t,e,n,r){return t.readUint8Array(e,n,r)}var De="\0☺☻♥♦♣♠•◘○◙♂♀♪♫☼►◄↕‼¶§▬↨↑↓→←∟↔▲▼ !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~⌂ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ ".split(""),ze=256==De.length;function Be(t,e){return e&&"cp437"==e.trim().toLowerCase()?function(t){if(ze){for(var e="",n=0;n<t.length;n++)e+=De[t[n]];return e}return(new TextDecoder).decode(t)}(t):new TextDecoder(e).decode(t)}var Ue="filename",We="rawFilename",He="comment",qe="rawComment",Ve="uncompressedSize",$e="compressedSize",Ge="offset",Xe="diskNumberStart",Ye="lastModDate",Ke="rawLastModDate",Je="lastAccessDate",Ze="creationDate",Qe=[Ue,We,$e,Ve,Ye,Ke,He,qe,Je,Ze,Ge,Xe,Xe,"internalFileAttribute","externalFileAttribute","msDosCompatible","zip64","directory","bitFlag","encrypted","signature","filenameUTF8","commentUTF8","compressionMethod","version","versionMadeBy","extraField","rawExtraField","extraFieldZip64","extraFieldUnicodePath","extraFieldUnicodeComment","extraFieldAES","extraFieldNTFS","extraFieldExtendedTimestamp"],tn=(0,h.A)((function t(e){var n=this;(0,d.A)(this,t),Qe.forEach((function(t){return n[t]=e[t]}))})),en="File format is not recognized",nn="Compression method not supported",rn="Split zip file",on="utf-8",an="cp437",sn=[[Ve,U],[$e,U],[Ge,U],[Xe,W]],un=(0,r.A)((0,r.A)({},W,{getValue:wn,bytes:4}),U,{getValue:kn,bytes:8}),cn=function(){return(0,h.A)((function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,d.A)(this,t),Object.assign(this,{reader:Fe(e),options:n,config:J})}),[{key:"getEntriesGenerator",value:function(){var t=this;return(0,v.A)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return y().mark((function n(){var r,i,s,u,c,l,f,h,d,v,g,b,m,x,w,k,A,S,E,_,T,O,M,L,C,I,R,j,P,F,N,D,z,B,$,G,X,Y,K,J,Z,Q,tt,et,nt;return y().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return i=(r=t).reader,s=r.config,n.next=1,(0,p.A)(je(i));case 1:if(i.size!==V&&i.readUint8Array){n.next=3;break}return B=Me,n.next=2,(0,p.A)(new Response(i.readable).blob());case 2:return $=n.sent,i=new B($),n.next=3,(0,p.A)(je(i));case 3:if(!(i.size<22)){n.next=4;break}throw new Error(en);case 4:return i.chunkSize=function(t){return Math.max(t.chunkSize,64)}(s),n.next=5,(0,p.A)(function(){var t=(0,a.A)(y().mark((function t(e,n,r,i,o){var s,u,c,l,f;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return l=function(){return l=(0,a.A)(y().mark((function t(n){var o,a,u;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o=r-n,t.next=1,Ne(e,o,n);case 1:a=t.sent,u=a.length-i;case 2:if(!(u>=0)){t.next=4;break}if(a[u]!=s[0]||a[u+1]!=s[1]||a[u+2]!=s[2]||a[u+3]!=s[3]){t.next=3;break}return t.abrupt("return",{offset:o+u,buffer:a.slice(u,u+i).buffer});case 3:u--,t.next=2;break;case 4:case"end":return t.stop()}}),t)}))),l.apply(this,arguments)},c=function(t){return l.apply(this,arguments)},function(t,e,n){t.setUint32(0,n,!0)}(An(s=new Uint8Array(4)),0,n),u=i+o,t.next=1,c(i);case 1:if(f=t.sent){t.next=3;break}return t.next=2,c(Math.min(u,r));case 2:f=t.sent;case 3:return t.abrupt("return",f);case 4:case"end":return t.stop()}}),t)})));return function(e,n,r,i,o){return t.apply(this,arguments)}}()(i,101010256,i.size,22,1048560));case 5:if(u=n.sent){n.next=9;break}return G=wn,X=An,n.next=6,(0,p.A)(Ne(i,0,4));case 6:if(Y=n.sent,K=X(Y),134695760!=G(K)){n.next=7;break}J=new Error(rn),n.next=8;break;case 7:J=new Error("End of central directory not found");case 8:throw J;case 9:if(c=An(u),l=wn(c,12),f=wn(c,16),h=u.offset,d=xn(c,20),v=h+22+d,g=xn(c,4),b=i.lastDiskNumber||0,m=xn(c,6),x=xn(c,8),w=0,k=0,f!=U&&l!=U&&x!=W&&m!=W){n.next=16;break}return Z=An,n.next=10,(0,p.A)(Ne(i,u.offset-20,20));case 10:if(Q=n.sent,117853008==wn(A=Z(Q),0)){n.next=11;break}throw new Error("End of Zip64 central directory not found");case 11:return f=kn(A,8),n.next=12,(0,p.A)(Ne(i,f,56,-1));case 12:if(S=n.sent,E=An(S),_=u.offset-20-56,wn(E,0)==q||f==_){n.next=14;break}return T=f,w=(f=_)-T,n.next=13,(0,p.A)(Ne(i,f,56,-1));case 13:S=n.sent,E=An(S);case 14:if(wn(E,0)==q){n.next=15;break}throw new Error("End of Zip64 central directory locator not found");case 15:g==W&&(g=wn(E,16)),m==W&&(m=wn(E,20)),x==W&&(x=kn(E,32)),l==U&&(l=kn(E,40)),f-=l;case 16:if(b==g){n.next=17;break}throw new Error(rn);case 17:if(!(f<0||f>=i.size)){n.next=18;break}throw new Error(en);case 18:return O=0,n.next=19,(0,p.A)(Ne(i,f,l,m));case 19:if(M=n.sent,L=An(M),!l){n.next=21;break}if(C=u.offset-l,wn(L,O)==H||f==C){n.next=21;break}return I=f,w=(f=C)-I,n.next=20,(0,p.A)(Ne(i,f,l,m));case 20:M=n.sent,L=An(M);case 21:if(R=u.offset-f-(i.lastDiskOffset||0),!(l!=R&&R>=0)){n.next=23;break}return l=R,n.next=22,(0,p.A)(Ne(i,f,l,m));case 22:M=n.sent,L=An(M);case 23:if(!(f<0||f>=i.size)){n.next=24;break}throw new Error(en);case 24:j=gn(r,e,"filenameEncoding"),P=gn(r,e,"commentEncoding"),F=y().mark((function t(){var n,a,u,c,l,f,h,d,v,g,b,m,A,S,E,_,T,C,I,R;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=new ln(i,s,r.options),wn(L,O)==H){t.next=1;break}throw new Error("Central directory header not found");case 1:return fn(n,L,O+6),a=Boolean(n.bitFlag.languageEncodingFlag),c=(u=O+46)+n.filenameLength,l=c+n.extraFieldLength,f=xn(L,O+4),h=M.subarray(u,c),d=xn(L,O+32),v=l+d,g=M.subarray(l,v),b=a,m=a,A=!(16&~mn(L,O+38)),S=wn(L,O+42)+w,Object.assign(n,{versionMadeBy:f,msDosCompatible:true,compressedSize:0,uncompressedSize:0,commentLength:d,directory:A,offset:S,diskNumberStart:xn(L,O+34),internalFileAttribute:xn(L,O+36),externalFileAttribute:wn(L,O+38),rawFilename:h,filenameUTF8:b,commentUTF8:m,rawExtraField:M.subarray(c,l)}),t.next=2,(0,p.A)(Promise.all([Be(h,b?on:j||an),Be(g,m?on:P||an)]));case 2:return E=t.sent,_=(0,o.A)(E,2),T=_[0],C=_[1],Object.assign(n,{rawComment:g,filename:T,comment:C,directory:A||T.endsWith("/")}),k=Math.max(S,k),t.next=3,(0,p.A)(hn(n,n,L,O+6));case 3:if((I=new tn(n)).getData=function(t,e){return n.getData(t,I,e)},O=v,!(R=e.onprogress)){t.next=7;break}return t.prev=4,t.next=5,(0,p.A)(R(N+1,x,new tn(n)));case 5:t.next=7;break;case 6:t.prev=6,t.catch(4);case 7:return t.next=8,I;case 8:case"end":return t.stop()}}),t,null,[[4,6]])})),N=0;case 25:if(!(N<x)){n.next=27;break}return n.delegateYield(F(),"t0",26);case 26:N++,n.next=25;break;case 27:if(D=gn(r,e,"extractPrependedData"),z=gn(r,e,"extractAppendedData"),!D){n.next=31;break}if(!(k>0)){n.next=29;break}return n.next=28,(0,p.A)(Ne(i,0,k));case 28:tt=n.sent,n.next=30;break;case 29:tt=new Uint8Array;case 30:r.prependedData=tt;case 31:if(!d){n.next=33;break}return n.next=32,(0,p.A)(Ne(i,h+22,d));case 32:et=n.sent,n.next=34;break;case 33:et=new Uint8Array;case 34:if(r.comment=et,!z){n.next=38;break}if(!(v<i.size)){n.next=36;break}return n.next=35,(0,p.A)(Ne(i,v,i.size-v));case 35:nt=n.sent,n.next=37;break;case 36:nt=new Uint8Array;case 37:r.appendedData=nt;case 38:return n.abrupt("return",!0);case 39:case"end":return n.stop()}}),n)}))()})).apply(this,arguments)}},{key:"getEntries",value:(e=(0,a.A)(y().mark((function t(){var e,n,r,i,o,a,s,u,c,l=arguments;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=l.length>0&&void 0!==l[0]?l[0]:{},n=[],r=!1,i=!1,t.prev=1,a=w(this.getEntriesGenerator(e));case 2:return t.next=3,a.next();case 3:if(!(r=!(s=t.sent).done)){t.next=5;break}u=s.value,n.push(u);case 4:r=!1,t.next=2;break;case 5:t.next=7;break;case 6:t.prev=6,c=t.catch(1),i=!0,o=c;case 7:if(t.prev=7,t.prev=8,!r||null==a.return){t.next=9;break}return t.next=9,a.return();case 9:if(t.prev=9,!i){t.next=10;break}throw o;case 10:return t.finish(9);case 11:return t.finish(7);case 12:return t.abrupt("return",n);case 13:case"end":return t.stop()}}),t,this,[[1,6,7,12],[8,,9,11]])}))),function(){return e.apply(this,arguments)})},{key:"close",value:(t=(0,a.A)(y().mark((function t(){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)}))),function(){return t.apply(this,arguments)})}]);var t,e}(),ln=function(){return(0,h.A)((function t(e,n,r){(0,d.A)(this,t),Object.assign(this,{reader:e,config:n,options:r})}),[{key:"getData",value:(t=(0,a.A)(y().mark((function t(e,n){var r,i,s,c,l,f,h,d,p,v,g,b,m,x,w,k,A,S,E,_,T,O,M,L,C,I,R,j,P,F,N,D,z,B,U,W=arguments;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=W.length>2&&void 0!==W[2]?W[2]:{},s=(i=this).reader,c=i.offset,l=i.diskNumberStart,f=i.extraFieldAES,h=i.compressionMethod,d=i.config,p=i.bitFlag,v=i.signature,g=i.rawLastModDate,b=i.uncompressedSize,m=i.compressedSize,x=n.localDirectory={},D=An,t.next=1,Ne(s,c,30,l);case 1:if(z=t.sent,w=D(z),k=(k=gn(i,r,"password"))&&k.length&&k,!f||99==f.originalCompressionMethod){t.next=2;break}throw new Error(nn);case 2:if(0==h||8==h){t.next=3;break}throw new Error(nn);case 3:if(67324752==wn(w,0)){t.next=4;break}throw new Error("Local file header not found");case 4:if(fn(x,w,4),!x.extraFieldLength){t.next=6;break}return t.next=5,Ne(s,c+30+x.filenameLength,x.extraFieldLength,l);case 5:B=t.sent,t.next=7;break;case 6:B=new Uint8Array;case 7:return x.rawExtraField=B,t.next=8,hn(i,x,w,4,!0);case 8:if(Object.assign(n,{lastAccessDate:x.lastAccessDate,creationDate:x.creationDate}),A=i.encrypted&&x.encrypted,S=A&&!f,!A){t.next=10;break}if(S||f.strength!==V){t.next=9;break}throw new Error("Encryption method not supported");case 9:if(k){t.next=10;break}throw new Error("File contains encrypted entry");case 10:return E=c+30+x.filenameLength+x.extraFieldLength,_=m,T=s.readable,Object.assign(T,{diskNumberStart:l,offset:E,size:_}),O=gn(i,r,"signal"),(M=gn(i,r,"checkPasswordOnly"))&&(e=new WritableStream),e=function(t){t.writable===V&&(0,u.A)(t.next)==G&&(t=new Re(t)),t instanceof WritableStream&&(t={writable:t});var e=t.writable;return e.size===V&&(e.size=0),t instanceof Re||Object.assign(t,{diskNumber:0,diskOffset:0,availableSize:1/0,maxSize:1/0}),t}(e),t.next=11,je(e,b);case 11:return L=e.writable,C=r.onstart,I=r.onprogress,R=r.onend,j={options:{codecType:ce,password:k,zipCrypto:S,encryptionStrength:f&&f.strength,signed:gn(i,r,"checkSignature"),passwordVerification:S&&(p.dataDescriptor?g>>>8&255:v>>>24&255),signature:v,compressed:0!=h,encrypted:A,useWebWorkers:gn(i,r,"useWebWorkers"),useCompressionStream:gn(i,r,"useCompressionStream"),transferStreams:gn(i,r,"transferStreams"),checkPasswordOnly:M},config:d,streamOptions:{signal:O,size:_,onstart:C,onprogress:I,onend:R}},P=0,t.prev=12,t.next=13,function(){var t=(0,a.A)(y().mark((function t(e,n){var r,i,a,s,u,c,l,f,h,d,p,v,g,b,m,x,w;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(w=function(t){if(Ae.length){var e=Ae.splice(0,1),n=(0,o.A)(e,1)[0],r=n.resolve,i=n.stream,a=n.workerOptions;r(new he(t,i,a,w))}else t.worker?(Ee(t),Number.isFinite(v)&&v>=0&&(t.terminateTimeout=setTimeout((function(){ke=ke.filter((function(e){return e!=t})),t.terminate()}),v))):ke=ke.filter((function(e){return e!=t}))},r=n.options,i=n.config,a=r.transferStreams,s=r.useWebWorkers,u=r.useCompressionStream,c=r.codecType,l=r.compressed,f=r.signed,h=r.encrypted,d=i.workerScripts,p=i.maxWorkers,v=i.terminateWorkerTimeout,n.transferStreams=a||a===V,g=!(l||f||h||n.transferStreams),n.useWebWorkers=!g&&(s||s===V&&i.useWebWorkers),n.scripts=n.useWebWorkers&&d?d[c]:[],r.useCompressionStream=u||u===V&&i.useCompressionStream,m=ke.find((function(t){return!t.busy})),!m){t.next=1;break}Ee(m),b=new he(m,e,n,w),t.next=4;break;case 1:if(!(ke.length<p)){t.next=2;break}x={indexWorker:Se},Se++,ke.push(x),b=new he(x,e,n,w),t.next=4;break;case 2:return t.next=3,new Promise((function(t){return Ae.push({resolve:t,stream:e,workerOptions:n})}));case 3:b=t.sent;case 4:return t.abrupt("return",b.run());case 5:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()({readable:T,writable:L},j);case 13:F=t.sent,P=F.outputSize,t.next=15;break;case 14:if(t.prev=14,U=t.catch(12),M&&U.message==vt){t.next=15;break}throw U;case 15:if(t.prev=15,N=gn(i,r,"preventClose"),L.size+=P,N||L.locked){t.next=16;break}return t.next=16,L.getWriter().close();case 16:return t.finish(15);case 17:return t.abrupt("return",M?void 0:e.getData?e.getData():L);case 18:case"end":return t.stop()}}),t,this,[[12,14,15,17]])}))),function(e,n){return t.apply(this,arguments)})}]);var t}();function fn(t,e,n){var r=t.rawBitFlag=xn(e,n+2),i=!(1&~r),o=wn(e,n+6);Object.assign(t,{encrypted:i,version:xn(e,n),bitFlag:{level:(6&r)>>1,dataDescriptor:!(8&~r),languageEncodingFlag:!(2048&~r)},rawLastModDate:o,lastModDate:yn(o),filenameLength:xn(e,n+22),extraFieldLength:xn(e,n+24)})}function hn(t,e,n,r,i){return dn.apply(this,arguments)}function dn(){return dn=(0,a.A)(y().mark((function t(e,n,r,i,a){var s,u,c,l,f,h,d,p,v,g,b,m,x,w;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:s=n.rawExtraField,u=n.extraField=new Map,c=An(new Uint8Array(s)),l=0;try{for(;l<s.length;)f=xn(c,l),h=xn(c,l+2),u.set(f,{type:f,data:s.slice(l+4,l+4+h)}),l+=4+h}catch(t){}if(d=xn(r,i+4),Object.assign(n,{signature:wn(r,i+10),uncompressedSize:wn(r,i+18),compressedSize:wn(r,i+14)}),(p=u.get(1))&&(function(t,e){e.zip64=!0;for(var n=An(t.data),r=sn.filter((function(t){var n=(0,o.A)(t,2),r=n[0],i=n[1];return e[r]==i})),i=0,a=0;i<r.length;i++){var s=(0,o.A)(r[i],2),u=s[0],c=s[1];if(e[u]==c){var l=un[c];e[u]=t[u]=l.getValue(n,a),a+=l.bytes}else if(t[u])throw new Error("Zip64 extra field not found")}}(p,n),n.extraFieldZip64=p),!(v=u.get(28789))){t.next=2;break}return t.next=1,pn(v,Ue,We,n,e);case 1:n.extraFieldUnicodePath=v;case 2:if(!(g=u.get(25461))){t.next=4;break}return t.next=3,pn(g,He,qe,n,e);case 3:n.extraFieldUnicodeComment=g;case 4:(b=u.get(39169))?(function(t,e,n){var r=An(t.data),i=mn(r,4);Object.assign(t,{vendorVersion:mn(r,0),vendorId:mn(r,2),strength:i,originalCompressionMethod:n,compressionMethod:xn(r,5)}),e.compressionMethod=t.compressionMethod}(b,n,d),n.extraFieldAES=b):n.compressionMethod=d,(m=u.get(10))&&(function(t,e){var n,r=An(t.data),i=4;try{for(;i<t.data.length&&!n;){var o=xn(r,i),a=xn(r,i+2);1==o&&(n=t.data.slice(i+4,i+4+a)),i+=4+a}}catch(t){}try{if(n&&24==n.length){var s=An(n),u=s.getBigUint64(0,!0),c=s.getBigUint64(8,!0),l=s.getBigUint64(16,!0);Object.assign(t,{rawLastModDate:u,rawLastAccessDate:c,rawCreationDate:l});var f={lastModDate:bn(u),lastAccessDate:bn(c),creationDate:bn(l)};Object.assign(t,f),Object.assign(e,f)}}catch(t){}}(m,n),n.extraFieldNTFS=m),(x=u.get(21589))&&(function(t,e,n){var r=An(t.data),i=mn(r,0),o=[],a=[];n?(!(1&~i)&&(o.push(Ye),a.push(Ke)),!(2&~i)&&(o.push(Je),a.push("rawLastAccessDate")),!(4&~i)&&(o.push(Ze),a.push("rawCreationDate"))):t.data.length>=5&&(o.push(Ye),a.push(Ke));var s=1;o.forEach((function(n,i){if(t.data.length>=s+4){var o=wn(r,s);e[n]=t[n]=new Date(1e3*o);var u=a[i];t[u]=o}s+=4}))}(x,n,a),n.extraFieldExtendedTimestamp=x),(w=u.get(6534))&&(n.extraFieldUSDZ=w);case 5:case"end":return t.stop()}}),t)}))),dn.apply(this,arguments)}function pn(t,e,n,r,i){return vn.apply(this,arguments)}function vn(){return vn=(0,a.A)(y().mark((function t(e,n,i,o,a){var s,u,c,l;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:s=An(e.data),(u=new it).append(a[i]),(c=An(new Uint8Array(4))).setUint32(0,u.get(),!0),l=wn(s,1),Object.assign(e,(0,r.A)((0,r.A)({version:mn(s,0)},n,Be(e.data.subarray(5))),"valid",!a.bitFlag.languageEncodingFlag&&l==wn(c,0))),e.valid&&(o[n]=e[n],o[n+"UTF8"]=!0);case 1:case"end":return t.stop()}}),t)}))),vn.apply(this,arguments)}function gn(t,e,n){return e[n]===V?t.options[n]:e[n]}function yn(t){var e=(4294901760&t)>>16,n=65535&t;try{return new Date(1980+((65024&e)>>9),((480&e)>>5)-1,31&e,(63488&n)>>11,(2016&n)>>5,2*(31&n),0)}catch(t){}}function bn(t){return new Date(Number(t/BigInt(1e4)-BigInt(116444736e5)))}function mn(t,e){return t.getUint8(e)}function xn(t,e){return t.getUint16(e,!0)}function wn(t,e){return t.getUint32(e,!0)}function kn(t,e){return Number(t.getBigUint64(e,!0))}function An(t){return new DataView(t.buffer)}Z({Inflate:function(t){var e=new B,n=t&&t.chunkSize?Math.floor(2*t.chunkSize):131072,r=new Uint8Array(n),i=!1;e.inflateInit(),e.next_out=r,this.append=function(t,o){var a,s,u=[],c=0,l=0,f=0;if(0!==t.length){e.next_in_index=0,e.next_in=t,e.avail_in=t.length;do{if(e.next_out_index=0,e.avail_out=n,0!==e.avail_in||i||(e.next_in_index=0,i=!0),a=e.inflate(0),i&&a===E){if(0!==e.avail_in)throw new Error("inflating: bad input")}else if(0!==a&&1!==a)throw new Error("inflating: "+e.msg);if((i||1===a)&&e.avail_in===t.length)throw new Error("inflating: bad input");e.next_out_index&&(e.next_out_index===n?u.push(new Uint8Array(r)):u.push(r.subarray(0,e.next_out_index))),f+=e.next_out_index,o&&e.next_in_index>0&&e.next_in_index!=c&&(o(e.next_in_index),c=e.next_in_index)}while(e.avail_in>0||0===e.avail_out);return u.length>1?(s=new Uint8Array(f),u.forEach((function(t){s.set(t,l),l+=t.length}))):s=u[0]?new Uint8Array(u[0]):new Uint8Array,s}},this.flush=function(){e.inflateEnd()}}})},3237:function(t,e,n){"use strict";n(511)("replace")},3250:function(t){"use strict";var e=Math.expm1,n=Math.exp;t.exports=!e||e(10)>22025.465794806718||e(10)<22025.465794806718||-2e-17!==e(-2e-17)?function(t){var e=+t;return 0===e?e:e>-1e-6&&e<1e-6?e+e*e/2:n(e)-1}:e},3251:function(t,e,n){"use strict";var r=n(6080),i=n(9565),o=n(5548),a=n(8981),s=n(6198),u=n(81),c=n(851),l=n(4209),f=n(1108),h=n(4644).aTypedArrayConstructor,d=n(5854);t.exports=function(t){var e,n,p,v,g,y,b,m,x=o(this),w=a(t),k=arguments.length,A=k>1?arguments[1]:void 0,S=void 0!==A,E=c(w);if(E&&!l(E))for(m=(b=u(w,E)).next,w=[];!(y=i(m,b)).done;)w.push(y.value);for(S&&k>2&&(A=r(A,arguments[2])),n=s(w),p=new(h(x))(n),v=f(p),e=0;n>e;e++)g=S?A(w[e],e):w[e],p[e]=v?d(g):+g;return p}},3296:function(t,e,n){"use strict";n(5806)},3313:function(t,e,n){"use strict";var r=n(6518),i=n(8866);r({target:"String",proto:!0,name:"trimEnd",forced:"".trimRight!==i},{trimRight:i})},3359:function(t,e,n){"use strict";n(8934);var r=n(6518),i=n(3487);r({target:"String",proto:!0,name:"trimStart",forced:"".trimStart!==i},{trimStart:i})},3362:function(t,e,n){"use strict";n(436),n(6499),n(2003),n(7743),n(1481),n(280)},3365:function(t,e,n){"use strict";var r,i=n(2744),o=n(4576),a=n(9504),s=n(6279),u=n(3451),c=n(6468),l=n(4006),f=n(34),h=n(1181).enforce,d=n(9039),p=n(8622),v=Object,g=Array.isArray,y=v.isExtensible,b=v.isFrozen,m=v.isSealed,x=v.freeze,w=v.seal,k=!o.ActiveXObject&&"ActiveXObject"in o,A=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},S=c("WeakMap",A,l),E=S.prototype,_=a(E.set);if(p)if(k){r=l.getConstructor(A,"WeakMap",!0),u.enable();var T=a(E.delete),O=a(E.has),M=a(E.get);s(E,{delete:function(t){if(f(t)&&!y(t)){var e=h(this);return e.frozen||(e.frozen=new r),T(this,t)||e.frozen.delete(t)}return T(this,t)},has:function(t){if(f(t)&&!y(t)){var e=h(this);return e.frozen||(e.frozen=new r),O(this,t)||e.frozen.has(t)}return O(this,t)},get:function(t){if(f(t)&&!y(t)){var e=h(this);return e.frozen||(e.frozen=new r),O(this,t)?M(this,t):e.frozen.get(t)}return M(this,t)},set:function(t,e){if(f(t)&&!y(t)){var n=h(this);n.frozen||(n.frozen=new r),O(this,t)?_(this,t,e):n.frozen.set(t,e)}else _(this,t,e);return this}})}else i&&d((function(){var t=x([]);return _(new S,t,1),!b(t)}))&&s(E,{set:function(t,e){var n;return g(t)&&(b(t)?n=x:m(t)&&(n=w)),_(this,t,e),n&&n(t),this}})},3389:function(t,e,n){"use strict";var r=n(4576),i=n(3724),o=Object.getOwnPropertyDescriptor;t.exports=function(t){if(!i)return r[t];var e=o(r,t);return e&&e.value}},3392:function(t,e,n){"use strict";var r=n(9504),i=0,o=Math.random(),a=r(1.1.toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++i+o,36)}},3418:function(t,e,n){"use strict";var r=n(6518),i=n(7916);r({target:"Array",stat:!0,forced:!n(4428)((function(t){Array.from(t)}))},{from:i})},3438:function(t,e,n){"use strict";var r=n(8551),i=n(34),o=n(6043);t.exports=function(t,e){if(r(t),i(e)&&e.constructor===t)return e;var n=o.f(t);return(0,n.resolve)(e),n.promise}},3451:function(t,e,n){"use strict";var r=n(6518),i=n(9504),o=n(421),a=n(34),s=n(9297),u=n(4913).f,c=n(8480),l=n(298),f=n(4124),h=n(3392),d=n(2744),p=!1,v=h("meta"),g=0,y=function(t){u(t,v,{value:{objectID:"O"+g++,weakData:{}}})},b=t.exports={enable:function(){b.enable=function(){},p=!0;var t=c.f,e=i([].splice),n={};n[v]=1,t(n).length&&(c.f=function(n){for(var r=t(n),i=0,o=r.length;i<o;i++)if(r[i]===v){e(r,i,1);break}return r},r({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:l.f}))},fastKey:function(t,e){if(!a(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!s(t,v)){if(!f(t))return"F";if(!e)return"E";y(t)}return t[v].objectID},getWeakData:function(t,e){if(!s(t,v)){if(!f(t))return!0;if(!e)return!1;y(t)}return t[v].weakData},onFreeze:function(t){return d&&p&&f(t)&&!s(t,v)&&y(t),t}};o[v]=!0},3470:function(t){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}},3487:function(t,e,n){"use strict";var r=n(3802).start,i=n(706);t.exports=i("trimStart")?function(){return r(this)}:"".trimStart},3500:function(t,e,n){"use strict";var r=n(4576),i=n(7400),o=n(9296),a=n(235),s=n(6699),u=function(t){if(t&&t.forEach!==a)try{s(t,"forEach",a)}catch(e){t.forEach=a}};for(var c in i)i[c]&&u(r[c]&&r[c].prototype);u(o)},3506:function(t,e,n){"use strict";var r=n(3925),i=String,o=TypeError;t.exports=function(t){if(r(t))return t;throw new o("Can't set "+i(t)+" as a prototype")}},3514:function(t,e,n){"use strict";n(6469)("flat")},3517:function(t,e,n){"use strict";var r=n(9504),i=n(9039),o=n(4901),a=n(6955),s=n(7751),u=n(3706),c=function(){},l=s("Reflect","construct"),f=/^\s*(?:class|function)\b/,h=r(f.exec),d=!f.test(c),p=function(t){if(!o(t))return!1;try{return l(c,[],t),!0}catch(t){return!1}},v=function(t){if(!o(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return d||!!h(f,u(t))}catch(t){return!0}};v.sham=!0,t.exports=!l||i((function(){var t;return p(p.call)||!p(Object)||!p((function(){t=!0}))||t}))?v:p},3602:function(t){"use strict";var e=4503599627370496;t.exports=function(t){return t+e-e}},3607:function(t,e,n){"use strict";var r=n(2839).match(/AppleWebKit\/(\d+)\./);t.exports=!!r&&+r[1]},3635:function(t,e,n){"use strict";var r=n(9039),i=n(4576).RegExp;t.exports=r((function(){var t=i(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)}))},3640:function(t,e,n){"use strict";var r=n(8551),i=n(4270),o=TypeError;t.exports=function(t){if(r(this),"string"===t||"default"===t)t="string";else if("number"!==t)throw new o("Incorrect hint");return i(this,t)}},3662:function(t,e,n){"use strict";function r(t,e){return r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},r(t,e)}n.d(e,{A:function(){return r}})},3684:function(t,e,n){"use strict";var r=n(4644).exportTypedArrayMethod,i=n(9039),o=n(4576),a=n(9504),s=o.Uint8Array,u=s&&s.prototype||{},c=[].toString,l=a([].join);i((function(){c.call({})}))&&(c=function(){return l(this)});var f=u.toString!==c;r("toString",c,f)},3690:function(t,e,n){"use strict";n(5823)("Uint16",(function(t){return function(e,n,r){return t(this,e,n,r)}}))},3706:function(t,e,n){"use strict";var r=n(9504),i=n(4901),o=n(7629),a=r(Function.toString);i(o.inspectSource)||(o.inspectSource=function(t){return a(t)}),t.exports=o.inspectSource},3709:function(t,e,n){"use strict";var r=n(2839).match(/firefox\/(\d+)/i);t.exports=!!r&&+r[1]},3717:function(t,e){"use strict";e.f=Object.getOwnPropertySymbols},3724:function(t,e,n){"use strict";var r=n(9039);t.exports=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3738:function(t){function e(n){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(n)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},3763:function(t,e,n){"use strict";var r=n(2839);t.exports=/MSIE|Trident/.test(r)},3771:function(t,e,n){"use strict";var r=n(6518),i=n(6754),o=n(6469);r({target:"Array",proto:!0},{fill:i}),o("fill")},3772:function(t,e,n){"use strict";n(3365)},3792:function(t,e,n){"use strict";var r=n(5397),i=n(6469),o=n(6269),a=n(1181),s=n(4913).f,u=n(1088),c=n(2529),l=n(6395),f=n(3724),h="Array Iterator",d=a.set,p=a.getterFor(h);t.exports=u(Array,"Array",(function(t,e){d(this,{type:h,target:r(t),index:0,kind:e})}),(function(){var t=p(this),e=t.target,n=t.index++;if(!e||n>=e.length)return t.target=null,c(void 0,!0);switch(t.kind){case"keys":return c(n,!1);case"values":return c(e[n],!1)}return c([n,e[n]],!1)}),"values");var v=o.Arguments=o.Array;if(i("keys"),i("values"),i("entries"),!l&&f&&"values"!==v.name)try{s(v,"name",{value:"values"})}catch(t){}},3802:function(t,e,n){"use strict";var r=n(9504),i=n(7750),o=n(655),a=n(7452),s=r("".replace),u=RegExp("^["+a+"]+"),c=RegExp("(^|[^"+a+"])["+a+"]+$"),l=function(t){return function(e){var n=o(i(e));return 1&t&&(n=s(n,u,"")),2&t&&(n=s(n,c,"$1")),n}};t.exports={start:l(1),end:l(2),trim:l(3)}},3851:function(t,e,n){"use strict";var r=n(6518),i=n(9039),o=n(5397),a=n(7347).f,s=n(3724);r({target:"Object",stat:!0,forced:!s||i((function(){a(1)})),sham:!s},{getOwnPropertyDescriptor:function(t,e){return a(o(t),e)}})},3860:function(t,e,n){"use strict";var r=n(6518),i=n(8183).codeAt;r({target:"String",proto:!0},{codePointAt:function(t){return i(this,t)}})},3904:function(t,e,n){"use strict";var r=n(4576),i=n(9039),o=n(9504),a=n(655),s=n(3802).trim,u=n(7452),c=o("".charAt),l=r.parseFloat,f=r.Symbol,h=f&&f.iterator,d=1/l(u+"-0")!=-1/0||h&&!i((function(){l(Object(h))}));t.exports=d?function(t){var e=s(a(t)),n=l(e);return 0===n&&"-"===c(e,0)?-0:n}:l},3921:function(t,e,n){"use strict";var r=n(6518),i=n(2652),o=n(4659);r({target:"Object",stat:!0},{fromEntries:function(t){var e={};return i(t,(function(t,n){o(e,t,n)}),{AS_ENTRIES:!0}),e}})},3925:function(t,e,n){"use strict";var r=n(34);t.exports=function(t){return r(t)||null===t}},3941:function(t,e,n){"use strict";var r=n(6518),i=n(34),o=n(3451).onFreeze,a=n(2744),s=n(9039),u=Object.seal;r({target:"Object",stat:!0,forced:s((function(){u(1)})),sham:!a},{seal:function(t){return u&&i(t)?u(o(t)):t}})},3954:function(t,e,n){"use strict";function r(t){return r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},r(t)}n.d(e,{A:function(){return r}})},3967:function(t,e,n){"use strict";var r=n(6518),i=n(34),o=n(3451).onFreeze,a=n(2744),s=n(9039),u=Object.preventExtensions;r({target:"Object",stat:!0,forced:s((function(){u(1)})),sham:!a},{preventExtensions:function(t){return u&&i(t)?u(o(t)):t}})},3994:function(t,e,n){"use strict";var r=n(7657).IteratorPrototype,i=n(2360),o=n(6980),a=n(687),s=n(6269),u=function(){return this};t.exports=function(t,e,n,c){var l=e+" Iterator";return t.prototype=i(r,{next:o(+!c,n)}),a(t,l,!1,!0),s[l]=u,t}},4003:function(t,e,n){"use strict";var r=n(6518),i=n(9039),o=n(34),a=n(2195),s=n(5652),u=Object.isFrozen;r({target:"Object",stat:!0,forced:s||i((function(){u(1)}))},{isFrozen:function(t){return!o(t)||!(!s||"ArrayBuffer"!==a(t))||!!u&&u(t)}})},4006:function(t,e,n){"use strict";var r=n(9504),i=n(6279),o=n(3451).getWeakData,a=n(679),s=n(8551),u=n(4117),c=n(34),l=n(2652),f=n(9213),h=n(9297),d=n(1181),p=d.set,v=d.getterFor,g=f.find,y=f.findIndex,b=r([].splice),m=0,x=function(t){return t.frozen||(t.frozen=new w)},w=function(){this.entries=[]},k=function(t,e){return g(t.entries,(function(t){return t[0]===e}))};w.prototype={get:function(t){var e=k(this,t);if(e)return e[1]},has:function(t){return!!k(this,t)},set:function(t,e){var n=k(this,t);n?n[1]=e:this.entries.push([t,e])},delete:function(t){var e=y(this.entries,(function(e){return e[0]===t}));return~e&&b(this.entries,e,1),!!~e}},t.exports={getConstructor:function(t,e,n,r){var f=t((function(t,i){a(t,d),p(t,{type:e,id:m++,frozen:null}),u(i)||l(i,t[r],{that:t,AS_ENTRIES:n})})),d=f.prototype,g=v(e),y=function(t,e,n){var r=g(t),i=o(s(e),!0);return!0===i?x(r).set(e,n):i[r.id]=n,t};return i(d,{delete:function(t){var e=g(this);if(!c(t))return!1;var n=o(t);return!0===n?x(e).delete(t):n&&h(n,e.id)&&delete n[e.id]},has:function(t){var e=g(this);if(!c(t))return!1;var n=o(t);return!0===n?x(e).has(t):n&&h(n,e.id)}}),i(d,n?{get:function(t){var e=g(this);if(c(t)){var n=o(t);if(!0===n)return x(e).get(t);if(n)return n[e.id]}},set:function(t,e){return y(this,t,e)}}:{add:function(t){return y(this,t,!0)}}),f}}},4052:function(t,e,n){"use strict";var r=n(6518),i=n(4124);r({target:"Object",stat:!0,forced:Object.isExtensible!==i},{isExtensible:i})},4055:function(t,e,n){"use strict";var r=n(4576),i=n(34),o=r.document,a=i(o)&&i(o.createElement);t.exports=function(t){return a?o.createElement(t):{}}},4117:function(t){"use strict";t.exports=function(t){return null==t}},4124:function(t,e,n){"use strict";var r=n(9039),i=n(34),o=n(2195),a=n(5652),s=Object.isExtensible,u=r((function(){s(1)}));t.exports=u||a?function(t){return!!i(t)&&(!a||"ArrayBuffer"!==o(t))&&(!s||s(t))}:s},4185:function(t,e,n){"use strict";var r=n(6518),i=n(3724),o=n(4913).f;r({target:"Object",stat:!0,forced:Object.defineProperty!==o,sham:!i},{defineProperty:o})},4209:function(t,e,n){"use strict";var r=n(8227),i=n(6269),o=r("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(i.Array===t||a[o]===t)}},4213:function(t,e,n){"use strict";var r=n(3724),i=n(9504),o=n(9565),a=n(9039),s=n(1072),u=n(3717),c=n(8773),l=n(8981),f=n(7055),h=Object.assign,d=Object.defineProperty,p=i([].concat);t.exports=!h||a((function(){if(r&&1!==h({b:1},h(d({},"a",{enumerable:!0,get:function(){d(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol("assign detection"),i="abcdefghijklmnopqrst";return t[n]=7,i.split("").forEach((function(t){e[t]=t})),7!==h({},t)[n]||s(h({},e)).join("")!==i}))?function(t,e){for(var n=l(t),i=arguments.length,a=1,h=u.f,d=c.f;i>a;)for(var v,g=f(arguments[a++]),y=h?p(s(g),h(g)):s(g),b=y.length,m=0;b>m;)v=y[m++],r&&!o(d,g,v)||(n[v]=g[v]);return n}:h},4215:function(t,e,n){"use strict";var r=n(4576),i=n(2839),o=n(2195),a=function(t){return i.slice(0,t.length)===t};t.exports=a("Bun/")?"BUN":a("Cloudflare-Workers")?"CLOUDFLARE":a("Deno/")?"DENO":a("Node.js/")?"NODE":r.Bun&&"string"==typeof Bun.version?"BUN":r.Deno&&"object"==typeof Deno.version?"DENO":"process"===o(r.process)?"NODE":r.window&&r.document?"BROWSER":"REST"},4265:function(t,e,n){"use strict";var r=n(2839);t.exports=/ipad|iphone|ipod/i.test(r)&&"undefined"!=typeof Pebble},4268:function(t,e,n){"use strict";var r=n(6518),i=n(9565),o=n(8551),a=n(34),s=n(6575),u=n(9039),c=n(4913),l=n(7347),f=n(2787),h=n(6980);r({target:"Reflect",stat:!0,forced:u((function(){var t=function(){},e=c.f(new t,"a",{configurable:!0});return!1!==Reflect.set(t.prototype,"a",1,e)}))},{set:function t(e,n,r){var u,d,p,v=arguments.length<4?e:arguments[3],g=l.f(o(e),n);if(!g){if(a(d=f(e)))return t(d,n,r,v);g=h(0)}if(s(g)){if(!1===g.writable||!a(v))return!1;if(u=l.f(v,n)){if(u.get||u.set||!1===u.writable)return!1;u.value=r,c.f(v,n,u)}else c.f(v,n,h(0,r))}else{if(void 0===(p=g.set))return!1;i(p,v,r)}return!0}})},4270:function(t,e,n){"use strict";var r=n(9565),i=n(4901),o=n(34),a=TypeError;t.exports=function(t,e){var n,s;if("string"===e&&i(n=t.toString)&&!o(s=r(n,t)))return s;if(i(n=t.valueOf)&&!o(s=r(n,t)))return s;if("string"!==e&&i(n=t.toString)&&!o(s=r(n,t)))return s;throw new a("Can't convert object to primitive value")}},4373:function(t){t.exports=function(t){var e=Object(t),n=[];for(var r in e)n.unshift(r);return function t(){for(;n.length;)if((r=n.pop())in e)return t.value=r,t.done=!1,t;return t.done=!0,t}},t.exports.__esModule=!0,t.exports.default=t.exports},4376:function(t,e,n){"use strict";var r=n(2195);t.exports=Array.isArray||function(t){return"Array"===r(t)}},4423:function(t,e,n){"use strict";var r=n(6518),i=n(9617).includes,o=n(9039),a=n(6469);r({target:"Array",proto:!0,forced:o((function(){return!Array(1).includes()}))},{includes:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),a("includes")},4428:function(t,e,n){"use strict";var r=n(8227)("iterator"),i=!1;try{var o=0,a={next:function(){return{done:!!o++}},return:function(){i=!0}};a[r]=function(){return this},Array.from(a,(function(){throw 2}))}catch(t){}t.exports=function(t,e){try{if(!e&&!i)return!1}catch(t){return!1}var n=!1;try{var o={};o[r]=function(){return{next:function(){return{done:n=!0}}}},t(o)}catch(t){}return n}},4435:function(t,e,n){"use strict";n(6518)({target:"Number",stat:!0,nonConfigurable:!0,nonWritable:!0},{MIN_SAFE_INTEGER:-9007199254740991})},4444:function(t,e,n){"use strict";var r=n(6518),i=n(7782),o=Math.abs,a=Math.pow;r({target:"Math",stat:!0},{cbrt:function(t){var e=+t;return i(e)*a(o(e),1/3)}})},4467:function(t,e,n){"use strict";n.d(e,{A:function(){return i}});var r=n(816);function i(t,e,n){return(e=(0,r.A)(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}},4488:function(t,e,n){"use strict";var r=n(7680),i=Math.floor,o=function(t,e){var n=t.length;if(n<8)for(var a,s,u=1;u<n;){for(s=u,a=t[u];s&&e(t[s-1],a)>0;)t[s]=t[--s];s!==u++&&(t[s]=a)}else for(var c=i(n/2),l=o(r(t,0,c),e),f=o(r(t,c),e),h=l.length,d=f.length,p=0,v=0;p<h||v<d;)t[p+v]=p<h&&v<d?e(l[p],f[v])<=0?l[p++]:f[v++]:p<h?l[p++]:f[v++];return t};t.exports=o},4490:function(t,e,n){"use strict";var r=n(6518),i=n(9504),o=n(4376),a=i([].reverse),s=[1,2];r({target:"Array",proto:!0,forced:String(s)===String(s.reverse())},{reverse:function(){return o(this)&&(this.length=this.length),a(this)}})},4495:function(t,e,n){"use strict";var r=n(9519),i=n(9039),o=n(4576).String;t.exports=!!Object.getOwnPropertySymbols&&!i((function(){var t=Symbol("symbol detection");return!o(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},4496:function(t,e,n){"use strict";var r=n(4644),i=n(9617).includes,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("includes",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},4527:function(t,e,n){"use strict";var r=n(3724),i=n(4376),o=TypeError,a=Object.getOwnPropertyDescriptor,s=r&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=s?function(t,e){if(i(t)&&!a(t,"length").writable)throw new o("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},4554:function(t,e,n){"use strict";var r=n(6518),i=n(8981),o=n(5610),a=n(1291),s=n(6198),u=n(4527),c=n(6837),l=n(1469),f=n(4659),h=n(4606),d=n(597)("splice"),p=Math.max,v=Math.min;r({target:"Array",proto:!0,forced:!d},{splice:function(t,e){var n,r,d,g,y,b,m=i(this),x=s(m),w=o(t,x),k=arguments.length;for(0===k?n=r=0:1===k?(n=0,r=x-w):(n=k-2,r=v(p(a(e),0),x-w)),c(x+n-r),d=l(m,r),g=0;g<r;g++)(y=w+g)in m&&f(d,g,m[y]);if(d.length=r,n<r){for(g=w;g<x-r;g++)b=g+n,(y=g+r)in m?m[b]=m[y]:h(m,b);for(g=x;g>x-r+n;g--)h(m,g-1)}else if(n>r)for(g=x-r;g>w;g--)b=g+n-1,(y=g+r-1)in m?m[b]=m[y]:h(m,b);for(g=0;g<n;g++)m[g+w]=arguments[g+2];return u(m,x-r+n),d}})},4576:function(t,e,n){"use strict";var r=function(t){return t&&t.Math===Math&&t};t.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()},4594:function(t,e,n){"use strict";n(5823)("Float32",(function(t){return function(e,n,r){return t(this,e,n,r)}}))},4598:function(t,e,n){"use strict";var r=n(9039);t.exports=function(t,e){var n=[][t];return!!n&&r((function(){n.call(null,e||function(){return 1},1)}))}},4601:function(t,e,n){"use strict";n(6518)({target:"Number",stat:!0,nonConfigurable:!0,nonWritable:!0},{MAX_SAFE_INTEGER:9007199254740991})},4606:function(t,e,n){"use strict";var r=n(6823),i=TypeError;t.exports=function(t,e){if(!delete t[e])throw new i("Cannot delete property "+r(e)+" of "+r(t))}},4633:function(t,e,n){var r=n(5172),i=n(6993),o=n(5869),a=n(887),s=n(1791),u=n(4373),c=n(579);function l(){"use strict";var e=i(),n=e.m(l),f=(Object.getPrototypeOf?Object.getPrototypeOf(n):n.__proto__).constructor;function h(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))}var d={throw:1,return:2,break:3,continue:3};function p(t){var e,n;return function(r){e||(e={stop:function(){return n(r.a,2)},catch:function(){return r.v},abrupt:function(t,e){return n(r.a,d[t],e)},delegateYield:function(t,i,o){return e.resultName=i,n(r.d,c(t),o)},finish:function(t){return n(r.f,t)}},n=function(t,n,i){r.p=e.prev,r.n=e.next;try{return t(n,i)}finally{e.next=r.n}}),e.resultName&&(e[e.resultName]=r.v,e.resultName=void 0),e.sent=r.v,e.next=r.n;try{return t.call(this,e)}finally{r.p=e.prev,r.n=e.next}}}return(t.exports=l=function(){return{wrap:function(t,n,r,i){return e.w(p(t),n,r,i&&i.reverse())},isGeneratorFunction:h,mark:e.m,awrap:function(t,e){return new r(t,e)},AsyncIterator:s,async:function(t,e,n,r,i){return(h(e)?a:o)(p(t),e,n,r,i)},keys:u,values:c}},t.exports.__esModule=!0,t.exports.default=t.exports)()}t.exports=l,t.exports.__esModule=!0,t.exports.default=t.exports},4634:function(t,e,n){"use strict";var r=n(6518),i=n(8551),o=n(4124);r({target:"Reflect",stat:!0},{isExtensible:function(t){return i(t),o(t)}})},4644:function(t,e,n){"use strict";var r,i,o,a=n(7811),s=n(3724),u=n(4576),c=n(4901),l=n(34),f=n(9297),h=n(6955),d=n(6823),p=n(6699),v=n(6840),g=n(2106),y=n(1625),b=n(2787),m=n(2967),x=n(8227),w=n(3392),k=n(1181),A=k.enforce,S=k.get,E=u.Int8Array,_=E&&E.prototype,T=u.Uint8ClampedArray,O=T&&T.prototype,M=E&&b(E),L=_&&b(_),C=Object.prototype,I=u.TypeError,R=x("toStringTag"),j=w("TYPED_ARRAY_TAG"),P="TypedArrayConstructor",F=a&&!!m&&"Opera"!==h(u.opera),N=!1,D={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},z={BigInt64Array:8,BigUint64Array:8},B=function(t){var e=b(t);if(l(e)){var n=S(e);return n&&f(n,P)?n[P]:B(e)}},U=function(t){if(!l(t))return!1;var e=h(t);return f(D,e)||f(z,e)};for(r in D)(o=(i=u[r])&&i.prototype)?A(o)[P]=i:F=!1;for(r in z)(o=(i=u[r])&&i.prototype)&&(A(o)[P]=i);if((!F||!c(M)||M===Function.prototype)&&(M=function(){throw new I("Incorrect invocation")},F))for(r in D)u[r]&&m(u[r],M);if((!F||!L||L===C)&&(L=M.prototype,F))for(r in D)u[r]&&m(u[r].prototype,L);if(F&&b(O)!==L&&m(O,L),s&&!f(L,R))for(r in N=!0,g(L,R,{configurable:!0,get:function(){return l(this)?this[j]:void 0}}),D)u[r]&&p(u[r],j,r);t.exports={NATIVE_ARRAY_BUFFER_VIEWS:F,TYPED_ARRAY_TAG:N&&j,aTypedArray:function(t){if(U(t))return t;throw new I("Target is not a typed array")},aTypedArrayConstructor:function(t){if(c(t)&&(!m||y(M,t)))return t;throw new I(d(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,e,n,r){if(s){if(n)for(var i in D){var o=u[i];if(o&&f(o.prototype,t))try{delete o.prototype[t]}catch(n){try{o.prototype[t]=e}catch(t){}}}L[t]&&!n||v(L,t,n?e:F&&_[t]||e,r)}},exportTypedArrayStaticMethod:function(t,e,n){var r,i;if(s){if(m){if(n)for(r in D)if((i=u[r])&&f(i,t))try{delete i[t]}catch(t){}if(M[t]&&!n)return;try{return v(M,t,n?e:F&&M[t]||e)}catch(t){}}for(r in D)!(i=u[r])||i[t]&&!n||v(i,t,e)}},getTypedArrayConstructor:B,isView:function(t){if(!l(t))return!1;var e=h(t);return"DataView"===e||f(D,e)||f(z,e)},isTypedArray:U,TypedArray:M,TypedArrayPrototype:L}},4659:function(t,e,n){"use strict";var r=n(3724),i=n(4913),o=n(6980);t.exports=function(t,e,n){r?i.f(t,e,o(0,n)):t[e]=n}},4731:function(t,e,n){"use strict";var r=n(4576);n(687)(r.JSON,"JSON",!0)},4743:function(t,e,n){"use strict";var r=n(6518),i=n(4576),o=n(6346),a=n(7633),s="ArrayBuffer",u=o[s];r({global:!0,constructor:!0,forced:i[s]!==u},{ArrayBuffer:u}),a(s)},4756:function(t,e,n){var r=n(4633)();t.exports=r;try{regeneratorRuntime=r}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}},4782:function(t,e,n){"use strict";var r=n(6518),i=n(4376),o=n(3517),a=n(34),s=n(5610),u=n(6198),c=n(5397),l=n(4659),f=n(8227),h=n(597),d=n(7680),p=h("slice"),v=f("species"),g=Array,y=Math.max;r({target:"Array",proto:!0,forced:!p},{slice:function(t,e){var n,r,f,h=c(this),p=u(h),b=s(t,p),m=s(void 0===e?p:e,p);if(i(h)&&(n=h.constructor,(o(n)&&(n===g||i(n.prototype))||a(n)&&null===(n=n[v]))&&(n=void 0),n===g||void 0===n))return d(h,b,m);for(r=new(void 0===n?g:n)(y(m-b,0)),f=0;b<m;b++,f++)b in h&&l(r,f,h[b]);return r.length=f,r}})},4864:function(t,e,n){"use strict";var r=n(3724),i=n(4576),o=n(9504),a=n(2796),s=n(3167),u=n(6699),c=n(2360),l=n(8480).f,f=n(1625),h=n(788),d=n(655),p=n(1034),v=n(8429),g=n(1056),y=n(6840),b=n(9039),m=n(9297),x=n(1181).enforce,w=n(7633),k=n(8227),A=n(3635),S=n(8814),E=k("match"),_=i.RegExp,T=_.prototype,O=i.SyntaxError,M=o(T.exec),L=o("".charAt),C=o("".replace),I=o("".indexOf),R=o("".slice),j=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,P=/a/g,F=/a/g,N=new _(P)!==P,D=v.MISSED_STICKY,z=v.UNSUPPORTED_Y;if(a("RegExp",r&&(!N||D||A||S||b((function(){return F[E]=!1,_(P)!==P||_(F)===F||"/a/i"!==String(_(P,"i"))}))))){for(var B=function(t,e){var n,r,i,o,a,l,v=f(T,this),g=h(t),y=void 0===e,b=[],w=t;if(!v&&g&&y&&t.constructor===B)return t;if((g||f(T,t))&&(t=t.source,y&&(e=p(w))),t=void 0===t?"":d(t),e=void 0===e?"":d(e),w=t,A&&"dotAll"in P&&(r=!!e&&I(e,"s")>-1)&&(e=C(e,/s/g,"")),n=e,D&&"sticky"in P&&(i=!!e&&I(e,"y")>-1)&&z&&(e=C(e,/y/g,"")),S&&(o=function(t){for(var e,n=t.length,r=0,i="",o=[],a=c(null),s=!1,u=!1,l=0,f="";r<=n;r++){if("\\"===(e=L(t,r)))e+=L(t,++r);else if("]"===e)s=!1;else if(!s)switch(!0){case"["===e:s=!0;break;case"("===e:if(i+=e,"?:"===R(t,r+1,r+3))continue;M(j,R(t,r+1))&&(r+=2,u=!0),l++;continue;case">"===e&&u:if(""===f||m(a,f))throw new O("Invalid capture group name");a[f]=!0,o[o.length]=[f,l],u=!1,f="";continue}u?f+=e:i+=e}return[i,o]}(t),t=o[0],b=o[1]),a=s(_(t,e),v?this:T,B),(r||i||b.length)&&(l=x(a),r&&(l.dotAll=!0,l.raw=B(function(t){for(var e,n=t.length,r=0,i="",o=!1;r<=n;r++)"\\"!==(e=L(t,r))?o||"."!==e?("["===e?o=!0:"]"===e&&(o=!1),i+=e):i+="[\\s\\S]":i+=e+L(t,++r);return i}(t),n)),i&&(l.sticky=!0),b.length&&(l.groups=b)),t!==w)try{u(a,"source",""===w?"(?:)":w)}catch(t){}return a},U=l(_),W=0;U.length>W;)g(B,_,U[W++]);T.constructor=B,B.prototype=T,y(i,"RegExp",B,{constructor:!0})}w("RegExp")},4873:function(t,e,n){"use strict";var r=n(6518),i=n(8551),o=n(3506),a=n(2967);a&&r({target:"Reflect",stat:!0},{setPrototypeOf:function(t,e){i(t),o(e);try{return a(t,e),!0}catch(t){return!1}}})},4885:function(t,e,n){"use strict";n.r(e),n.d(e,{MOBI:function(){return yt},isMOBI:function(){return at}});var r=n(6822),i=n(3954),o=n(991),a=n(5501),s=n(5458),u=n(3029),c=n(2901),l=n(4467),f=n(467),h=n(296),d=n(4756),p=n.n(d);function v(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?v(Object(n),!0).forEach((function(e){(0,l.A)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function y(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(y=function(){return!!t})()}function b(t,e,n,r){var a=(0,o.A)((0,i.A)(1&r?t.prototype:t),e,n);return 2&r&&"function"==typeof a?function(t){return a.apply(n,t)}:a}function m(t,e){w(t,e),e.add(t)}function x(t,e,n){w(t,e),e.set(t,n)}function w(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function k(t,e){return t.get(S(t,e))}function A(t,e,n){return t.set(S(t,e),n),n}function S(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}function E(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return _(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var T=function(t){if(!t)return"";var e=document.createElement("textarea");return e.innerHTML=t,e.value},O={XML:"application/xml",XHTML:"application/xhtml+xml",HTML:"text/html",CSS:"text/css",SVG:"image/svg+xml"},M={name:[0,32,"string"],type:[60,4,"string"],creator:[64,4,"string"],numRecords:[76,2,"uint"]},L={compression:[0,2,"uint"],numTextRecords:[8,2,"uint"],recordSize:[10,2,"uint"],encryption:[12,2,"uint"]},C={magic:[16,4,"string"],length:[20,4,"uint"],type:[24,4,"uint"],encoding:[28,4,"uint"],uid:[32,4,"uint"],version:[36,4,"uint"],titleOffset:[84,4,"uint"],titleLength:[88,4,"uint"],localeRegion:[94,1,"uint"],localeLanguage:[95,1,"uint"],resourceStart:[108,4,"uint"],huffcdic:[112,4,"uint"],numHuffcdic:[116,4,"uint"],exthFlag:[128,4,"uint"],trailingFlags:[240,4,"uint"],indx:[244,4,"uint"]},I={resourceStart:[108,4,"uint"],fdst:[192,4,"uint"],numFdst:[196,4,"uint"],frag:[248,4,"uint"],skel:[252,4,"uint"],guide:[260,4,"uint"]},R={magic:[0,4,"string"],length:[4,4,"uint"],count:[8,4,"uint"]},j={magic:[0,4,"string"],length:[4,4,"uint"],type:[8,4,"uint"],idxt:[20,4,"uint"],numRecords:[24,4,"uint"],encoding:[28,4,"uint"],language:[32,4,"uint"],total:[36,4,"uint"],ordt:[40,4,"uint"],ligt:[44,4,"uint"],numLigt:[48,4,"uint"],numCncx:[52,4,"uint"]},P={magic:[0,4,"string"],length:[4,4,"uint"],numControlBytes:[8,4,"uint"]},F={magic:[0,4,"string"],offset1:[8,4,"uint"],offset2:[12,4,"uint"]},N={magic:[0,4,"string"],length:[4,4,"uint"],numEntries:[8,4,"uint"],codeLength:[12,4,"uint"]},D={magic:[0,4,"string"],numEntries:[8,4,"uint"]},z={flags:[8,4,"uint"],dataStart:[12,4,"uint"],keyLength:[16,4,"uint"],keyStart:[20,4,"uint"]},B={1252:"windows-1252",65001:"utf-8"},U={100:["creator","string",!0],101:["publisher"],103:["description"],104:["isbn"],105:["subject","string",!0],106:["date"],108:["contributor","string",!0],109:["rights"],110:["subjectCode","string",!0],112:["source","string",!0],113:["asin"],121:["boundary","uint"],122:["fixedLayout"],125:["numResources","uint"],126:["originalResolution"],127:["zeroGutter"],128:["zeroMargin"],129:["coverURI"],132:["regionMagnification"],201:["coverOffset","uint"],202:["thumbnailOffset","uint"],503:["title"],524:["language","string",!0],527:["pageProgressionDirection"]},W={1:["ar","ar-SA","ar-IQ","ar-EG","ar-LY","ar-DZ","ar-MA","ar-TN","ar-OM","ar-YE","ar-SY","ar-JO","ar-LB","ar-KW","ar-AE","ar-BH","ar-QA"],2:["bg"],3:["ca"],4:["zh","zh-TW","zh-CN","zh-HK","zh-SG"],5:["cs"],6:["da"],7:["de","de-DE","de-CH","de-AT","de-LU","de-LI"],8:["el"],9:["en","en-US","en-GB","en-AU","en-CA","en-NZ","en-IE","en-ZA","en-JM",null,"en-BZ","en-TT","en-ZW","en-PH"],10:["es","es-ES","es-MX",null,"es-GT","es-CR","es-PA","es-DO","es-VE","es-CO","es-PE","es-AR","es-EC","es-CL","es-UY","es-PY","es-BO","es-SV","es-HN","es-NI","es-PR"],11:["fi"],12:["fr","fr-FR","fr-BE","fr-CA","fr-CH","fr-LU","fr-MC"],13:["he"],14:["hu"],15:["is"],16:["it","it-IT","it-CH"],17:["ja"],18:["ko"],19:["nl","nl-NL","nl-BE"],20:["no","nb","nn"],21:["pl"],22:["pt","pt-BR","pt-PT"],23:["rm"],24:["ro"],25:["ru"],26:["hr",null,"sr"],27:["sk"],28:["sq"],29:["sv","sv-SE","sv-FI"],30:["th"],31:["tr"],32:["ur"],33:["id"],34:["uk"],35:["be"],36:["sl"],37:["et"],38:["lv"],39:["lt"],41:["fa"],42:["vi"],43:["hy"],44:["az"],45:["eu"],46:["hsb"],47:["mk"],48:["st"],49:["ts"],50:["tn"],52:["xh"],53:["zu"],54:["af"],55:["ka"],56:["fo"],57:["hi"],58:["mt"],59:["se"],62:["ms"],63:["kk"],65:["sw"],67:["uz",null,"uz-UZ"],68:["tt"],69:["bn"],70:["pa"],71:["gu"],72:["or"],73:["ta"],74:["te"],75:["kn"],76:["ml"],77:["as"],78:["mr"],79:["sa"],82:["cy","cy-GB"],83:["gl","gl-ES"],87:["kok"],97:["ne"],98:["fy"]},H=function(t,e){var n=new t.constructor(t.length+e.length);return n.set(t),n.set(e,t.length),n},q=function(t,e,n){var r=new t.constructor(t.length+e.length+n.length);return r.set(t),r.set(e,t.length),r.set(n,t.length+e.length),r},V=new TextDecoder,$=function(t){return V.decode(t)},G=function(t){if(t){var e=t.byteLength,n=4===e?"getUint32":2===e?"getUint16":"getUint8";return new DataView(t)[n](0)}},X=function(t,e){return Object.fromEntries(Array.from(Object.entries(t)).map((function(t){var n=(0,h.A)(t,2),r=n[0],i=(0,h.A)(n[1],3),o=i[0],a=i[1];return[r,("string"===i[2]?$:G)(e.slice(o,o+a))]})))},Y=function(t){return new TextDecoder(B[t])},K=function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=0,i=0,o=E(t.subarray(n,n+4));try{for(o.s();!(e=o.n()).done;){var a=e.value;if(r=r<<7|(127&a)>>>0,i++,128&a)break}}catch(t){o.e(t)}finally{o.f()}return{value:r,length:i}},J=function(t){var e,n=0,r=E(t.subarray(-4));try{for(r.s();!(e=r.n()).done;){var i=e.value;128&i&&(n=0),n=n<<7|127&i}}catch(t){r.e(t)}finally{r.f()}return n},Z=function(t){for(var e=0;t>0;t>>=1)1&~t||e++;return e},Q=function(t){for(var e=0;!(1&t);)t>>=1,e++;return e},tt=function(t){for(var e=[],n=0;n<t.length;n++){var r=t[n];if(0===r)e.push(0);else if(r<=8){var i,o=E(t.subarray(n+1,(n+=r)+1));try{for(o.s();!(i=o.n()).done;){var a=i.value;e.push(a)}}catch(t){o.e(t)}finally{o.f()}}else if(r<=127)e.push(r);else if(r<=191)for(var s=r<<8|t[1+n++],u=(16383&s)>>>3,c=3+(7&s),l=0;l<c;l++)e.push(e[e.length-u]);else e.push(32,128^r)}return Uint8Array.from(e)},et=function(t,e){for(var n=e+32,r=n>>3,i=0n,o=e>>3;o<=r;o++){var a;i=i<<8n|BigInt(null!==(a=t[o])&&void 0!==a?a:0)}return i>>8n-BigInt(7&n)&0xffffffffn},nt=function(){var t=(0,f.A)(p().mark((function t(e,n){var r,i,o,a,s,u,c,l,f,d,v,g,y,b,m,x,w,k,A,S;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,n(e.huffcdic);case 1:if(r=t.sent,i=X(F,r),o=i.magic,a=i.offset1,s=i.offset2,"HUFF"===o){t.next=2;break}throw new Error("Invalid HUFF record");case 2:u=Array.from({length:256},(function(t,e){return a+4*e})).map((function(t){return G(r.slice(t,t+4))})).map((function(t){return[128&t,31&t,t>>>8]})),c=[null].concat(Array.from({length:32},(function(t,e){return s+8*e})).map((function(t){return[G(r.slice(t,t+4)),G(r.slice(t+4,t+8))]}))),l=[],f=1;case 3:if(!(f<e.numHuffcdic)){t.next=7;break}return t.next=4,n(e.huffcdic+f);case 4:if(d=t.sent,"CDIC"===(v=X(N,d)).magic){t.next=5;break}throw new Error("Invalid CDIC record");case 5:for(g=Math.min(1<<v.codeLength,v.numEntries-l.length),y=d.slice(v.length),b=0;b<g;b++)m=G(y.slice(2*b,2*b+2)),x=G(y.slice(m,m+2)),w=32767&x,k=32768&x,A=new Uint8Array(y.slice(m+2,m+2+w)),l.push([A,k]);case 6:f++,t.next=3;break;case 7:return S=function(t){for(var e=new Uint8Array,n=8*t.byteLength,r=0;r<n;){var i=Number(et(t,r)),o=(0,h.A)(u[i>>>24],3),a=o[0],s=o[1],f=o[2];if(!a){for(;i>>>32-s<c[s][0];)s+=1;f=c[s][1]}if((r+=s)>n)break;var d=f-(i>>>32-s),p=(0,h.A)(l[d],2),v=p[0];p[1]||(v=S(v),l[d]=[v,!0]),e=H(e,v)}return e},t.abrupt("return",S);case 8:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),rt=function(){var t=(0,f.A)(p().mark((function t(e,n){var r,i,o,a,s,u,c,l,f,d,v,g,y,b,m,x,w,k,A,S,_,T,O,M,L,C,I,R,F,N,D,z,B,U,W,H,q,V,J,tt,et,nt,rt,it,ot,at,st,ut,ct,lt,ft,ht,dt,pt,vt,gt,yt,bt,mt,xt,wt;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,n(e);case 1:if(r=t.sent,"INDX"===(i=X(j,r)).magic){t.next=2;break}throw new Error("Invalid INDX record");case 2:if(o=Y(i.encoding),a=r.slice(i.length),"TAGX"===(s=X(P,a)).magic){t.next=3;break}throw new Error("Invalid TAGX section");case 3:u=(s.length-12)/4,c=Array.from({length:u},(function(t,e){return new Uint8Array(a.slice(12+4*e,12+4*e+4))})),l={},f=0,d=0;case 4:if(!(d<i.numCncx)){t.next=7;break}return t.next=5,n(e+i.numRecords+d+1);case 5:for(v=t.sent,g=new Uint8Array(v),y=0;y<g.byteLength;)b=y,m=K(g,y),x=m.value,w=m.length,y+=w,k=v.slice(y,y+x),y+=x,l[f+b]=o.decode(k);f+=65536;case 6:d++,t.next=4;break;case 7:A=[],S=0;case 8:if(!(S<i.numRecords)){t.next=22;break}return t.next=9,n(e+1+S);case 9:if(_=t.sent,T=new Uint8Array(_),"INDX"===(O=X(j,_)).magic){t.next=10;break}throw new Error("Invalid INDX record");case 10:M=0;case 11:if(!(M<O.numRecords)){t.next=21;break}L=O.idxt+4+2*M,C=G(_.slice(L,L+2)),I=G(_.slice(C,C+1)),R=$(_.slice(C+1,C+1+I)),F=[],D=0,z=(N=C+1+I)+s.numControlBytes,B=E(c),t.prev=12,B.s();case 13:if((U=B.n()).done){t.next=16;break}if(W=(0,h.A)(U.value,4),H=W[0],q=W[1],V=W[2],!(1&W[3])){t.next=14;break}return D++,t.abrupt("continue",15);case 14:J=N+D,(tt=G(_.slice(J,J+1))&V)===V?Z(V)>1?(et=K(T,z),nt=et.value,rt=et.length,F.push([H,null,nt,q]),z+=rt):F.push([H,1,null,q]):F.push([H,tt>>Q(V),null,q]);case 15:t.next=13;break;case 16:t.next=18;break;case 17:t.prev=17,wt=t.catch(12),B.e(wt);case 18:return t.prev=18,B.f(),t.finish(18);case 19:for(it={},ot=0,at=F;ot<at.length;ot++){if(st=(0,h.A)(at[ot],4),ut=st[0],ct=st[1],lt=st[2],ft=st[3],ht=[],null!=ct)for(dt=0;dt<ct*ft;dt++)pt=K(T,z),vt=pt.value,gt=pt.length,ht.push(vt),z+=gt;else for(yt=0;yt<lt;)bt=K(T,z),mt=bt.value,xt=bt.length,ht.push(mt),z+=xt,yt+=xt;it[ut]=ht}A.push({name:R,tagMap:it});case 20:M++,t.next=11;break;case 21:S++,t.next=8;break;case 22:return t.abrupt("return",{table:A,cncx:l});case 23:case"end":return t.stop()}}),t,null,[[12,17,18,19]])})));return function(e,n){return t.apply(this,arguments)}}(),it=function(){var t=(0,f.A)(p().mark((function t(e,n){var r,i,o,a,s;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,rt(e,n);case 1:return r=t.sent,i=r.table,o=r.cncx,a=i.map((function(t,e){var n,r,i,a,s,u,c,l=t.tagMap;return{index:e,offset:null===(n=l[1])||void 0===n?void 0:n[0],size:null===(r=l[2])||void 0===r?void 0:r[0],label:null!==(i=o[l[3]])&&void 0!==i?i:"",headingLevel:null===(a=l[4])||void 0===a?void 0:a[0],pos:l[6],parent:null===(s=l[21])||void 0===s?void 0:s[0],firstChild:null===(u=l[22])||void 0===u?void 0:u[0],lastChild:null===(c=l[23])||void 0===c?void 0:c[0]}})),s=function(t){return null==t.firstChild||(t.children=a.filter((function(e){return e.parent===t.index})).map(s)),t},t.abrupt("return",a.filter((function(t){return 0===t.headingLevel})).map(s));case 2:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),ot=function(){var t=(0,f.A)(p().mark((function t(e,n){var r,i,o,a,s,u,c,l,f,h,d;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=X(z,e),i=r.flags,o=r.dataStart,a=r.keyLength,s=r.keyStart,u=new Uint8Array(e.slice(o)),2&i)for(c=16===a?1024:1040,l=new Uint8Array(e.slice(s,s+a)),f=Math.min(c,u.length),h=0;h<f;h++)u[h]=u[h]^l[h%l.length];if(!(1&i)){t.next=4;break}return t.prev=1,t.next=2,n(u);case 2:return t.abrupt("return",t.sent);case 3:t.prev=3,d=t.catch(1),console.warn(d),console.warn("Failed to decompress font");case 4:return t.abrupt("return",u);case 5:case"end":return t.stop()}}),t,null,[[1,3]])})));return function(e,n){return t.apply(this,arguments)}}(),at=function(){var t=(0,f.A)(p().mark((function t(e){var n,r,i;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=$,t.next=1,e.slice(60,68).arrayBuffer();case 1:return i=t.sent,n=r(i),t.abrupt("return","BOOKMOBI"===n);case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),st=new WeakMap,ut=new WeakMap,ct=function(){return(0,c.A)((function t(){(0,u.A)(this,t),x(this,st,void 0),x(this,ut,void 0),(0,l.A)(this,"pdb",void 0)}),[{key:"open",value:(e=(0,f.A)(p().mark((function t(e){var n,r,i,o,a;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return A(st,this,e),i=X,o=M,t.next=1,e.slice(0,78).arrayBuffer();case 1:return a=t.sent,n=i(o,a),this.pdb=n,t.next=2,e.slice(78,78+8*n.numRecords).arrayBuffer();case 2:r=t.sent,A(ut,this,Array.from({length:n.numRecords},(function(t,e){return G(r.slice(8*e,8*e+4))})).map((function(t,e,n){return[t,n[e+1]]})));case 3:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"loadRecord",value:function(t){var e,n=k(ut,this)[t];if(!n)throw new RangeError("Record index out of bounds");return(e=k(st,this)).slice.apply(e,(0,s.A)(n)).arrayBuffer()}},{key:"loadMagic",value:(t=(0,f.A)(p().mark((function t(e){var n,r,i;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=k(ut,this)[e][0],r=$,t.next=1,k(st,this).slice(n,n+4).arrayBuffer();case 1:return i=t.sent,t.abrupt("return",r(i));case 2:case"end":return t.stop()}}),t,this)}))),function(e){return t.apply(this,arguments)})}]);var t,e}(),lt=new WeakMap,ft=new WeakMap,ht=new WeakMap,dt=new WeakMap,pt=new WeakMap,vt=new WeakMap,gt=new WeakSet,yt=function(t){function e(t){var n,o,a,s,c=t.unzlib;return(0,u.A)(this,e),o=this,a=e,a=(0,i.A)(a),m(n=(0,r.A)(o,y()?Reflect.construct(a,[],(0,i.A)(o).constructor):a.apply(o,s)),gt),x(n,lt,0),x(n,ft,void 0),x(n,ht,void 0),x(n,dt,void 0),x(n,pt,void 0),x(n,vt,void 0),n.unzlib=c,n}return(0,a.A)(e,t),(0,c.A)(e,[{key:"open",value:(s=(0,f.A)(p().mark((function t(n){var r,i,o,a,s,u,c,l,f,h;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,b(e,"open",this,3)([n]);case 1:return a=S(gt,this,bt),s=this,t.next=2,b(e,"loadRecord",this,3)([0]);case 2:if(u=t.sent,this.headers=a.call.call(a,s,u),A(ft,this,this.headers.mobi.resourceStart),r=this.headers.mobi.version>=8){t.next=6;break}if(!((o=null===(i=this.headers.exth)||void 0===i?void 0:i.boundary)<4294967295)){t.next=6;break}return t.prev=3,c=S(gt,this,bt),l=this,t.next=4,b(e,"loadRecord",this,3)([o]);case 4:f=t.sent,this.headers=c.call.call(c,l,f),A(lt,this,o),r=!0,t.next=6;break;case 5:t.prev=5,h=t.catch(3),console.warn(h),console.warn("Failed to open KF8; falling back to MOBI");case 6:return t.next=7,S(gt,this,mt).call(this);case 7:return t.abrupt("return",r?new Zt(this).init():new Lt(this).init());case 8:case"end":return t.stop()}}),t,this,[[3,5]])}))),function(t){return s.apply(this,arguments)})},{key:"decode",value:function(){var t;return(t=k(ht,this)).decode.apply(t,arguments)}},{key:"encode",value:function(){var t;return(t=k(dt,this)).encode.apply(t,arguments)}},{key:"loadRecord",value:function(t){return b(e,"loadRecord",this,3)([k(lt,this)+t])}},{key:"loadMagic",value:function(t){return b(e,"loadMagic",this,3)([k(lt,this)+t])}},{key:"loadText",value:function(t){return this.loadRecord(t+1).then((function(t){return new Uint8Array(t)})).then(k(vt,this)).then(k(pt,this))}},{key:"loadResource",value:(o=(0,f.A)(p().mark((function t(n){var r,i;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,b(e,"loadRecord",this,3)([k(ft,this)+n]);case 1:if(r=t.sent,"FONT"!==(i=$(r.slice(0,4)))){t.next=2;break}return t.abrupt("return",ot(r,this.unzlib));case 2:if("VIDE"!==i&&"AUDI"!==i){t.next=3;break}return t.abrupt("return",r.slice(12));case 3:return t.abrupt("return",r);case 4:case"end":return t.stop()}}),t,this)}))),function(t){return o.apply(this,arguments)})},{key:"getNCX",value:function(){var t=this.headers.mobi.indx;if(t<4294967295)return it(t,this.loadRecord.bind(this))}},{key:"getMetadata",value:function(){var t,e,n,r=this.headers,i=r.mobi,o=r.exth;return{identifier:i.uid.toString(),title:T((null==o?void 0:o.title)||this.decode(i.title)),author:null==o||null===(t=o.creator)||void 0===t?void 0:t.map(T),publisher:T(null==o?void 0:o.publisher),language:null!==(e=null==o?void 0:o.language)&&void 0!==e?e:i.language,published:null==o?void 0:o.date,description:T(null==o?void 0:o.description),subject:null==o||null===(n=o.subject)||void 0===n?void 0:n.map(T),rights:T(null==o?void 0:o.rights),contributor:null==o?void 0:o.contributor}}},{key:"getCover",value:(n=(0,f.A)(p().mark((function t(){var e,n,r;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e=this.headers.exth,null==(n=(null==e?void 0:e.coverOffset)<4294967295?null==e?void 0:e.coverOffset:(null==e?void 0:e.thumbnailOffset)<4294967295?null==e?void 0:e.thumbnailOffset:null)){t.next=2;break}return t.next=1,this.loadResource(n);case 1:return r=t.sent,t.abrupt("return",new Blob([r]));case 2:case"end":return t.stop()}}),t,this)}))),function(){return n.apply(this,arguments)})}]);var n,o,s}(ct);function bt(t){var e,n=X(L,t),r=X(C,t);if("MOBI"!==r.magic)throw new Error("Missing MOBI header");var i=r.titleOffset,o=r.titleLength,a=r.localeLanguage,s=r.localeRegion;r.title=t.slice(i,i+o);var u=W[a];r.language=null!==(e=null==u?void 0:u[s>>2])&&void 0!==e?e:null==u?void 0:u[0];var c=64&r.exthFlag?function(t,e){var n=X(R,t),r=n.magic,i=n.count;if("EXTH"!==r)throw new Error("Invalid EXTH header");for(var o=Y(e),a={},s=12,u=0;u<i;u++){var c=G(t.slice(s,s+4)),l=G(t.slice(s+4,s+8));if(c in U){var f,d=(0,h.A)(U[c],3),p=d[0],v=d[1],g=d[2],y=t.slice(s+8,s+l),b="uint"===v?G(y):o.decode(y);g?(null!==(f=a[p])&&void 0!==f||(a[p]=[]),a[p].push(b)):a[p]=b}s+=l}return a}(t.slice(r.length+16),r.encoding):null;return{palmdoc:n,mobi:r,exth:c,kf8:r.version>=8?X(I,t):null}}function mt(){return xt.apply(this,arguments)}function xt(){return(xt=(0,f.A)(p().mark((function t(){var e,n,r,i,o,a,s,u,c,l,f,h,d;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e=this.headers,n=e.palmdoc,r=e.mobi,A(ht,this,Y(r.encoding)),A(dt,this,new TextEncoder),i=n.compression,u=A,c=pt,l=this,1!==i){t.next=1;break}f=function(t){return t},t.next=7;break;case 1:if(2!==i){t.next=2;break}h=tt,t.next=6;break;case 2:if(17480!==i){t.next=4;break}return t.next=3,nt(r,this.loadRecord.bind(this));case 3:d=t.sent,t.next=5;break;case 4:d=null;case 5:h=d;case 6:f=h;case 7:if(u(c,l,f),k(pt,this)){t.next=8;break}throw new Error("Unknown compression type");case 8:o=r.trailingFlags,a=1&o,s=Z(o>>>1),A(vt,this,(function(t){for(var e=0;e<s;e++){var n=J(t);t=t.subarray(0,-n)}if(a){var r=1+(3&t[t.length-1]);t=t.subarray(0,-r)}return t}));case 9:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}var wt=/<\s*(?:mbp:)?pagebreak[^>]*>/gi,kt=/<[^<>]+filepos=['"]{0,1}(\d+)[^<>]*>/gi,At=function(t){for(var e=0;t;){var n=t.parentElement;if(n){var r=n.tagName.toLowerCase();"p"===r?e+=1.5:"blockquote"===r&&(e+=2)}t=n}return e},St=new WeakMap,Et=new WeakMap,_t=new WeakMap,Tt=new WeakMap,Ot=new WeakMap,Mt=new WeakMap,Lt=function(){return(0,c.A)((function t(e){(0,u.A)(this,t),(0,l.A)(this,"parser",new DOMParser),(0,l.A)(this,"serializer",new XMLSerializer),x(this,St,new Map),x(this,Et,new Map),x(this,_t,new Map),x(this,Tt,void 0),x(this,Ot,[]),x(this,Mt,O.HTML),this.mobi=e}),[{key:"init",value:(h=(0,f.A)(p().mark((function t(){var e,n,r,i,o,a,u,c,l,f,h,d,v,g,y,b,m,x=this;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=new Uint8Array,n=0;case 1:if(!(n<this.mobi.headers.palmdoc.numTextRecords)){t.next=4;break}return g=H,y=e,t.next=2,this.mobi.loadText(n);case 2:b=t.sent,e=g(y,b);case 3:n++,t.next=1;break;case 4:return r=Array.from(new Uint8Array(e),(function(t){return String.fromCharCode(t)})).join(""),A(Tt,this,[0].concat(Array.from(r.matchAll(wt),(function(t){return t.index}))).map((function(t,e,n){return r.slice(t,n[e+1])})).map((function(t){return Uint8Array.from(t,(function(t){return t.charCodeAt(0)}))})).map((function(t){return{book:x,raw:t}})).reduce((function(t,e){var n,r=t[t.length-1];return e.start=null!==(n=null==r?void 0:r.end)&&void 0!==n?n:0,e.end=e.start+e.raw.byteLength,t.concat(e)}),[])),this.sections=k(Tt,this).map((function(t,e){return{id:e,load:function(){return x.loadSection(t)},createDocument:function(){return x.createDocument(t)},size:t.end-t.start}})),t.prev=5,t.next=6,this.getGuide();case 6:if(this.landmarks=t.sent,!(o=null===(i=this.landmarks.find((function(t){var e=t.type;return null==e?void 0:e.includes("toc")})))||void 0===i?void 0:i.href)){t.next=8;break}return a=this.resolveHref(o),u=a.index,t.next=7,this.sections[u].createDocument();case 7:c=t.sent,f=0,h=0,d=new Map,v=new Map,this.toc=Array.from(c.querySelectorAll("a[filepos]")).reduce((function(t,e){var n,r,i,o=At(e),a={label:null!==(n=null===(r=e.innerText)||void 0===r?void 0:r.trim())&&void 0!==n?n:"",href:"filepos:".concat(e.getAttribute("filepos"))},s=o>h?f+1:o===h?f:null!==(i=d.get(o))&&void 0!==i?i:Math.max(0,f-1);if(s>f){var u,c;l?(null!==(c=(u=l).subitems)&&void 0!==c||(u.subitems=[]),l.subitems.push(a),v.set(s,l)):t.push(a)}else{var p=v.get(s);p?p.subitems.push(a):t.push(a)}return l=a,f=s,h=o,d.set(o,s),t}),[]);case 8:t.next=10;break;case 9:t.prev=9,m=t.catch(5),console.warn(m);case 10:return A(Ot,this,(0,s.A)(new Set(Array.from(r.matchAll(kt),(function(t){return t[1]})))).map((function(t){return{filepos:t,number:Number(t)}})).sort((function(t,e){return t.number-e.number}))),this.metadata=this.mobi.getMetadata(),this.getCover=this.mobi.getCover.bind(this.mobi),t.abrupt("return",this);case 11:case"end":return t.stop()}}),t,this,[[5,9]])}))),function(){return h.apply(this,arguments)})},{key:"getGuide",value:(a=(0,f.A)(p().mark((function t(){var e;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,this.createDocument(k(Tt,this)[0]);case 1:return e=t.sent,t.abrupt("return",Array.from(e.getElementsByTagName("reference"),(function(t){var e;return{label:t.getAttribute("title"),type:null===(e=t.getAttribute("type"))||void 0===e?void 0:e.split(/\s/),href:"filepos:".concat(t.getAttribute("filepos"))}})));case 2:case"end":return t.stop()}}),t,this)}))),function(){return a.apply(this,arguments)})},{key:"loadResource",value:(o=(0,f.A)(p().mark((function t(e){var n,r;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!k(St,this).has(e)){t.next=1;break}return t.abrupt("return",k(St,this).get(e));case 1:return t.next=2,this.mobi.loadResource(e);case 2:return n=t.sent,r=URL.createObjectURL(new Blob([n])),k(St,this).set(e,r),t.abrupt("return",r);case 3:case"end":return t.stop()}}),t,this)}))),function(t){return o.apply(this,arguments)})},{key:"loadRecindex",value:(i=(0,f.A)(p().mark((function t(e){return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",this.loadResource(Number(e)-1));case 1:case"end":return t.stop()}}),t,this)}))),function(t){return i.apply(this,arguments)})},{key:"replaceResources",value:(r=(0,f.A)(p().mark((function t(e){var n,r,i,o,a,s,u,c,l,f,h,d,v,g,y;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=E(e.querySelectorAll("img[recindex]")),t.prev=1,n.s();case 2:if((r=n.n()).done){t.next=7;break}return i=r.value,o=i.getAttribute("recindex"),t.prev=3,t.next=4,this.loadRecindex(o);case 4:i.src=t.sent,t.next=6;break;case 5:t.prev=5,t.catch(3),console.warn("Failed to load image ".concat(o));case 6:t.next=2;break;case 7:t.next=9;break;case 8:t.prev=8,g=t.catch(1),n.e(g);case 9:return t.prev=9,n.f(),t.finish(9);case 10:a=E(e.querySelectorAll("[mediarecindex]")),t.prev=11,a.s();case 12:if((s=a.n()).done){t.next=19;break}return u=s.value,c=u.getAttribute("mediarecindex"),l=u.getAttribute("recindex"),t.prev=13,t.next=14,this.loadRecindex(c);case 14:if(u.src=t.sent,!l){t.next=16;break}return t.next=15,this.loadRecindex(l);case 15:u.poster=t.sent;case 16:t.next=18;break;case 17:t.prev=17,t.catch(13),console.warn("Failed to load media ".concat(c));case 18:t.next=12;break;case 19:t.next=21;break;case 20:t.prev=20,y=t.catch(11),a.e(y);case 21:return t.prev=21,a.f(),t.finish(21);case 22:f=E(e.querySelectorAll("[filepos]"));try{for(f.s();!(h=f.n()).done;)d=h.value,v=d.getAttribute("filepos"),d.href="filepos:".concat(v)}catch(t){f.e(t)}finally{f.f()}case 23:case"end":return t.stop()}}),t,this,[[1,8,9,10],[3,5],[11,20,21,22],[13,17]])}))),function(t){return r.apply(this,arguments)})},{key:"loadText",value:(n=(0,f.A)(p().mark((function t(e){var n,r,i,o,a=this;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!k(Et,this).has(e)){t.next=1;break}return t.abrupt("return",k(Et,this).get(e));case 1:return n=e.raw,r=k(Ot,this).filter((function(t){var n=t.number;return n>=e.start&&n<e.end})).map((function(t){return g(g({},t),{},{offset:t.number-e.start})})),i=n,r.length&&(i=n.subarray(0,r[0].offset),r.forEach((function(t,e){var o=t.filepos,s=t.offset,u=r[e+1],c=a.mobi.encode('<a id="filepos'.concat(o,'"></a>'));i=q(i,c,n.subarray(s,null==u?void 0:u.offset))}))),o=this.mobi.decode(i).replaceAll(wt,""),k(Et,this).set(e,o),t.abrupt("return",o);case 2:case"end":return t.stop()}}),t,this)}))),function(t){return n.apply(this,arguments)})},{key:"createDocument",value:(e=(0,f.A)(p().mark((function t(e){var n;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,this.loadText(e);case 1:return n=t.sent,t.abrupt("return",this.parser.parseFromString(n,k(Mt,this)));case 2:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"loadSection",value:(t=(0,f.A)(p().mark((function t(e){var n,r,i,o;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!k(_t,this).has(e)){t.next=1;break}return t.abrupt("return",k(_t,this).get(e));case 1:return t.next=2,this.createDocument(e);case 2:return n=t.sent,r=n.createElement("style"),n.head.append(r),r.append(n.createTextNode("blockquote {\n            margin-block-start: 0;\n            margin-block-end: 0;\n            margin-inline-start: 1em;\n            margin-inline-end: 0;\n        }")),t.next=3,this.replaceResources(n);case 3:return i=this.serializer.serializeToString(n),o=URL.createObjectURL(new Blob([i],{type:k(Mt,this)})),k(_t,this).set(e,o),t.abrupt("return",o);case 4:case"end":return t.stop()}}),t,this)}))),function(e){return t.apply(this,arguments)})},{key:"resolveHref",value:function(t){var e=t.match(/filepos:(.*)/)[1],n=Number(e);return{index:k(Tt,this).findIndex((function(t){return t.end>n})),anchor:function(t){return t.getElementById("filepos".concat(e))}}}},{key:"splitTOCHref",value:function(t){var e=t.match(/filepos:(.*)/)[1],n=Number(e);return[k(Tt,this).findIndex((function(t){return t.end>n})),"filepos".concat(e)]}},{key:"getTOCFragment",value:function(t,e){return t.getElementById(e)}},{key:"isExternal",value:function(t){return/^(?!blob|filepos)\w+:/i.test(t)}},{key:"destroy",value:function(){var t,e=E(k(St,this).values());try{for(e.s();!(t=e.n()).done;){var n=t.value;URL.revokeObjectURL(n)}}catch(t){e.e(t)}finally{e.f()}var r,i=E(k(_t,this).values());try{for(i.s();!(r=i.n()).done;){var o=r.value;URL.revokeObjectURL(o)}}catch(t){i.e(t)}finally{i.f()}}}]);var t,e,n,r,i,o,a,h}(),Ct=/kindle:(flow|embed):(\w+)(?:\?mime=(\w+\/[-+.\w]+))?/,It=/kindle:pos:fid:(\w+):off:(\w+)/,Rt=function(t){var e=t.match(Ct).slice(1),n=(0,h.A)(e,3),r=n[0],i=n[1],o=n[2];return{resourceType:r,id:parseInt(i,32),type:o}},jt=function(t){var e=t.match(It).slice(1),n=(0,h.A)(e,2),r=n[0],i=n[1];return{fid:parseInt(r,32),off:parseInt(i,32)}},Pt=function(){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return"kindle:pos:fid:".concat((arguments.length>0&&void 0!==arguments[0]?arguments[0]:0).toString(32).toUpperCase().padStart(4,"0"),":off:").concat(t.toString(32).toUpperCase().padStart(10,"0"))},Ft=function(t){var e=t.match(/\s(id|name|aid)\s*=\s*['"]([^'"]*)['"]/i);if(e){var n=(0,h.A)(e,3),r=n[1],i=n[2];return"[".concat(r,'="').concat(CSS.escape(i),'"]')}},Nt=function(){var t=(0,f.A)(p().mark((function t(e,n,r){var i,o,a,u,c,l,f;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=[],e.replace(n,(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return i.push(e),null})),o=[],a=0,u=i;case 1:if(!(a<u.length)){t.next=4;break}return c=u[a],l=o,t.next=2,r.apply(void 0,(0,s.A)(c));case 2:f=t.sent,l.push.call(l,f);case 3:a++,t.next=1;break;case 4:return t.abrupt("return",e.replace(n,(function(){return o.shift()})));case 5:case"end":return t.stop()}}),t)})));return function(e,n,r){return t.apply(this,arguments)}}(),Dt=function(t){var e,n=E(t);try{for(n.s();!(e=n.n()).done;){var r=e.value;if("page-spread-left"===r||"rendition:page-spread-left"===r)return"left";if("page-spread-right"===r||"rendition:page-spread-right"===r)return"right";if("rendition:page-spread-center"===r)return"center"}}catch(t){n.e(t)}finally{n.f()}},zt=new WeakMap,Bt=new WeakMap,Ut=new WeakMap,Wt=new WeakMap,Ht=new WeakMap,qt=new WeakMap,Vt=new WeakMap,$t=new WeakMap,Gt=new WeakMap,Xt=new WeakMap,Yt=new WeakMap,Kt=new WeakMap,Jt=new WeakSet,Zt=function(){return(0,c.A)((function t(e){(0,u.A)(this,t),m(this,Jt),(0,l.A)(this,"parser",new DOMParser),(0,l.A)(this,"serializer",new XMLSerializer),x(this,zt,new Map),x(this,Bt,new Map),x(this,Ut,new Map),x(this,Wt,{}),x(this,Ht,void 0),x(this,qt,void 0),x(this,Vt,new Uint8Array),x(this,$t,new Uint8Array),x(this,Gt,-1),x(this,Xt,-1),x(this,Yt,O.XHTML),x(this,Kt,new Map),this.mobi=e}),[{key:"init",value:(g=(0,f.A)(p().mark((function t(){var e,n,r,i,o,a,s,u,c,l,f,d,v,g,y,b,m,x,w,S,_,M,L,C,I,R,j,P=this;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=this.mobi.loadRecord.bind(this.mobi),i=this.mobi.headers.kf8,t.prev=1,t.next=2,r(i.fdst);case 2:if(o=t.sent,"FDST"===(a=X(D,o)).magic){t.next=3;break}throw new Error("Missing FDST record");case 3:s=Array.from({length:a.numEntries},(function(t,e){return 12+8*e})).map((function(t){return[G(o.slice(t,t+4)),G(o.slice(t+4,t+8))]})),k(Wt,this).fdstTable=s,A(qt,this,s[s.length-1][1]),t.next=5;break;case 4:t.prev=4,t.catch(1);case 5:return t.next=6,rt(i.skel,r);case 6:return u=t.sent.table.map((function(t,e){var n=t.name,r=t.tagMap;return{index:e,name:n,numFrag:r[1][0],offset:r[6][0],length:r[6][1]}})),t.next=7,rt(i.frag,r);case 7:return c=t.sent,l=c.table.map((function(t){var e=t.name,n=t.tagMap;return{insertOffset:parseInt(e),selector:c.cncx[n[2][0]],index:n[4][0],offset:n[6][0],length:n[6][1]}})),k(Wt,this).skelTable=u,k(Wt,this).fragTable=l,A(Ht,this,u.reduce((function(t,e){var n,r,i=t[t.length-1],o=null!==(n=null==i?void 0:i.fragEnd)&&void 0!==n?n:0,a=o+e.numFrag,s=l.slice(o,a),u=e.length+s.map((function(t){return t.length})).reduce((function(t,e){return t+e})),c=(null!==(r=null==i?void 0:i.totalLength)&&void 0!==r?r:0)+u;return t.concat({skel:e,frags:s,fragEnd:a,length:u,totalLength:c})}),[])),t.next=8,this.getResourcesByMagic(["RESC","PAGE"]);case 8:if(f=t.sent,d=new Map,!f.RESC){t.next=10;break}return t.next=9,this.mobi.loadRecord(f.RESC);case 9:v=t.sent,g=this.mobi.decode(v.slice(16)).replace(/\0/g,""),y=g.search(/\?>/),b="<package>".concat(g.slice(y),"</package>"),m=this.parser.parseFromString(b,O.XML),x=E(m.querySelectorAll("spine > itemref"));try{for(x.s();!(w=x.n()).done;)M=w.value,L=parseInt(M.getAttribute("skelid")),d.set(L,Dt(null!==(S=null===(_=M.getAttribute("properties"))||void 0===_?void 0:_.split(" "))&&void 0!==S?S:[]))}catch(t){x.e(t)}finally{x.f()}case 10:return this.sections=k(Ht,this).map((function(t,e){return t.frags.length?{id:e,load:function(){return P.loadSection(t)},createDocument:function(){return P.createDocument(t)},size:t.length,pageSpread:d.get(e)}:{linear:"no"}})),t.prev=11,t.next=12,this.mobi.getNCX();case 12:return C=t.sent,I=function(t){var e=t.label,n=t.pos,r=t.children,i=(0,h.A)(n,2),o=i[0],a=i[1],s=Pt(o,a),u=k(Bt,P).get(o);return u?u.push(a):k(Bt,P).set(o,[a]),{label:T(e),href:s,subitems:null==r?void 0:r.map(I)}},this.toc=null==C?void 0:C.map(I),t.next=13,this.getGuide();case 13:this.landmarks=t.sent,t.next=15;break;case 14:t.prev=14,j=t.catch(11),console.warn(j);case 15:return R=this.mobi.headers.exth,this.dir=R.pageProgressionDirection,this.rendition={layout:"true"===R.fixedLayout?"pre-paginated":"reflowable",viewport:Object.fromEntries(null!==(e=null===(n=R.originalResolution)||void 0===n||null===(n=n.split("x"))||void 0===n||null===(n=n.slice(0,2))||void 0===n?void 0:n.map((function(t,e){return[e?"height":"width",t]})))&&void 0!==e?e:[])},this.metadata=this.mobi.getMetadata(),this.getCover=this.mobi.getCover.bind(this.mobi),t.abrupt("return",this);case 16:case"end":return t.stop()}}),t,this,[[1,4],[11,14]])}))),function(){return g.apply(this,arguments)})},{key:"getResourcesByMagic",value:(v=(0,f.A)(p().mark((function t(e){var n,r,i,o,a,s=this;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n={},r=this.mobi.headers.kf8.resourceStart,i=this.mobi.pdb.numRecords,o=p().mark((function t(){var r,i;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=1,s.mobi.loadMagic(a);case 1:r=t.sent,(i=e.find((function(t){return t===r})))&&(n[i]=a),t.next=3;break;case 2:t.prev=2,t.catch(0);case 3:case"end":return t.stop()}}),t,null,[[0,2]])})),a=r;case 1:if(!(a<i)){t.next=3;break}return t.delegateYield(o(),"t0",2);case 2:a++,t.next=1;break;case 3:return t.abrupt("return",n);case 4:case"end":return t.stop()}}),t,this)}))),function(t){return v.apply(this,arguments)})},{key:"getGuide",value:(d=(0,f.A)(p().mark((function t(){var e,n,r,i,o;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!((e=this.mobi.headers.kf8.guide)<4294967295)){t.next=2;break}return n=this.mobi.loadRecord.bind(this.mobi),t.next=1,rt(e,n);case 1:return r=t.sent,i=r.table,o=r.cncx,t.abrupt("return",i.map((function(t){var e,n,r,i,a=t.name,s=t.tagMap;return{label:null!==(e=o[s[1][0]])&&void 0!==e?e:"",type:null==a?void 0:a.split(/\s/),href:Pt(null!==(n=null===(r=s[6])||void 0===r?void 0:r[0])&&void 0!==n?n:null===(i=s[3])||void 0===i?void 0:i[0])}})));case 2:case"end":return t.stop()}}),t,this)}))),function(){return d.apply(this,arguments)})},{key:"loadResourceBlob",value:(a=(0,f.A)(p().mark((function t(e){var n,r,i,o,a,s,u,c,l,f;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=Rt(e),i=r.resourceType,o=r.id,a=r.type,"flow"!==i){t.next=2;break}return t.next=1,this.loadFlow(o);case 1:l=t.sent,t.next=4;break;case 2:return t.next=3,this.mobi.loadResource(o-1);case 3:l=t.sent;case 4:if(s=l,![O.XHTML,O.HTML,O.CSS,O.SVG].includes(a)){t.next=6;break}return t.next=5,this.replaceResources(this.mobi.decode(s));case 5:f=t.sent,t.next=7;break;case 6:f=s;case 7:return u=f,c=a===O.SVG?this.parser.parseFromString(u,a):null,t.abrupt("return",[new Blob([u],{type:a}),null!=c&&null!==(n=c.getElementsByTagNameNS("http://www.w3.org/2000/svg","image"))&&void 0!==n&&n.length?c.documentElement:null]);case 8:case"end":return t.stop()}}),t,this)}))),function(t){return a.apply(this,arguments)})},{key:"loadResource",value:(o=(0,f.A)(p().mark((function t(e){var n,r,i,o,a;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!k(zt,this).has(e)){t.next=1;break}return t.abrupt("return",k(zt,this).get(e));case 1:return t.next=2,this.loadResourceBlob(e);case 2:return n=t.sent,r=(0,h.A)(n,2),i=r[0],o=r[1],a=o?e:URL.createObjectURL(i),o&&k(Kt,this).set(a,o),k(zt,this).set(e,a),t.abrupt("return",a);case 3:case"end":return t.stop()}}),t,this)}))),function(t){return o.apply(this,arguments)})},{key:"replaceResources",value:function(t){var e=new RegExp(Ct,"g");return Nt(t,e,this.loadResource.bind(this))}},{key:"loadRaw",value:(i=(0,f.A)(p().mark((function t(e,n){var r,i,o,a,s,u,c,l,f;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=n-k(Vt,this).length,i=null==k(qt,this)?1/0:k(qt,this)-k($t,this).length-e,!(r<0||r<i)){t.next=4;break}case 1:if(!(k(Vt,this).length<n)){t.next=3;break}return a=A(Gt,this,(o=k(Gt,this),++o)),t.next=2,this.mobi.loadText(a);case 2:s=t.sent,A(Vt,this,H(k(Vt,this),s)),t.next=1;break;case 3:return t.abrupt("return",k(Vt,this).slice(e,n));case 4:if(!(k(qt,this)-k($t,this).length>e)){t.next=6;break}return c=this.mobi.headers.palmdoc.numTextRecords-1-A(Xt,this,(u=k(Xt,this),++u)),t.next=5,this.mobi.loadText(c);case 5:l=t.sent,A($t,this,H(l,k($t,this))),t.next=4;break;case 6:return f=k(qt,this)-k($t,this).length,t.abrupt("return",k($t,this).slice(e-f,n-f));case 7:case"end":return t.stop()}}),t,this)}))),function(t,e){return i.apply(this,arguments)})},{key:"loadFlow",value:function(t){if(t<4294967295)return this.loadRaw.apply(this,(0,s.A)(k(Wt,this).fdstTable[t]))}},{key:"loadText",value:(r=(0,f.A)(p().mark((function t(e){var n,r,i,o,a,s,u,c,l,f,h,d,v,g,y,b,m;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.skel,r=e.frags,i=e.length,t.next=1,this.loadRaw(n.offset,n.offset+i);case 1:o=t.sent,a=o.slice(0,n.length),s=E(r);try{for(s.s();!(u=s.n()).done;)if(c=u.value,l=c.insertOffset-n.offset,f=n.length+c.offset,h=o.slice(f,f+c.length),a=q(a.slice(0,l),h,a.slice(l)),d=k(Bt,this).get(c.index)){v=E(d);try{for(v.s();!(g=v.n()).done;)y=g.value,b=this.mobi.decode(h).slice(y),m=Ft(b),S(Jt,this,Qt).call(this,c.index,y,m)}catch(t){v.e(t)}finally{v.f()}}}catch(t){s.e(t)}finally{s.f()}return t.abrupt("return",this.mobi.decode(a));case 2:case"end":return t.stop()}}),t,this)}))),function(t){return r.apply(this,arguments)})},{key:"createDocument",value:(n=(0,f.A)(p().mark((function t(e){var n;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,this.loadText(e);case 1:return n=t.sent,t.abrupt("return",this.parser.parseFromString(n,k(Yt,this)));case 2:case"end":return t.stop()}}),t,this)}))),function(t){return n.apply(this,arguments)})},{key:"loadSection",value:(e=(0,f.A)(p().mark((function t(e){var n,r,i,o,a,s,u,c,l,f,d,v;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!k(zt,this).has(e)){t.next=1;break}return t.abrupt("return",k(zt,this).get(e));case 1:return t.next=2,this.loadText(e);case 2:return r=t.sent,t.next=3,this.replaceResources(r);case 3:i=t.sent,!(o=this.parser.parseFromString(i,k(Yt,this))).querySelector("parsererror")&&null!==(n=o.documentElement)&&void 0!==n&&n.namespaceURI||(A(Yt,this,O.HTML),o=this.parser.parseFromString(i,k(Yt,this))),a=E(k(Kt,this));try{for(a.s();!(s=a.n()).done;){u=(0,h.A)(s.value,2),c=u[0],l=u[1],f=E(o.querySelectorAll('img[src="'.concat(c,'"]')));try{for(f.s();!(d=f.n()).done;)d.value.replaceWith(l)}catch(t){f.e(t)}finally{f.f()}}}catch(t){a.e(t)}finally{a.f()}return v=URL.createObjectURL(new Blob([this.serializer.serializeToString(o)],{type:k(Yt,this)})),k(zt,this).set(e,v),t.abrupt("return",v);case 4:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"getIndexByFID",value:function(t){return k(Ht,this).findIndex((function(e){return e.frags.some((function(e){return e.index===t}))}))}},{key:"resolveHref",value:(t=(0,f.A)(p().mark((function t(e){var n,r,i,o,a,s,u,c,l,f,h,d,v,g,y;return p().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=jt(e),i=r.fid,o=r.off,!((a=this.getIndexByFID(i))<0)){t.next=1;break}return t.abrupt("return");case 1:if(!(s=null===(n=k(Ut,this).get(i))||void 0===n?void 0:n.get(o))){t.next=2;break}return t.abrupt("return",{index:a,anchor:function(t){return t.querySelector(s)}});case 2:return u=k(Ht,this)[a],c=u.skel,l=u.frags,f=l.find((function(t){return t.index===i})),h=c.offset+c.length+f.offset,t.next=3,this.loadRaw(h,h+f.length);case 3:return d=t.sent,v=this.mobi.decode(d).slice(o),g=Ft(v),S(Jt,this,Qt).call(this,i,o,g),y=function(t){return t.querySelector(g)},t.abrupt("return",{index:a,anchor:y});case 4:case"end":return t.stop()}}),t,this)}))),function(e){return t.apply(this,arguments)})},{key:"splitTOCHref",value:function(t){var e=jt(t);return[this.getIndexByFID(e.fid),e]}},{key:"getTOCFragment",value:function(t,e){var n,r=e.fid,i=e.off,o=null===(n=k(Ut,this).get(r))||void 0===n?void 0:n.get(i);return t.querySelector(o)}},{key:"isExternal",value:function(t){return/^(?!blob|kindle)\w+:/i.test(t)}},{key:"destroy",value:function(){var t,e=E(k(zt,this).values());try{for(e.s();!(t=e.n()).done;){var n=t.value;URL.revokeObjectURL(n)}}catch(t){e.e(t)}finally{e.f()}}}]);var t,e,n,r,i,o,a,d,v,g}();function Qt(t,e,n){var r=k(Ut,this).get(t);if(r)r.set(e,n);else{var i=new Map;k(Ut,this).set(t,i),i.set(e,n)}}},4901:function(t){"use strict";var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},4913:function(t,e,n){"use strict";var r=n(3724),i=n(5917),o=n(8686),a=n(8551),s=n(6969),u=TypeError,c=Object.defineProperty,l=Object.getOwnPropertyDescriptor,f="enumerable",h="configurable",d="writable";e.f=r?o?function(t,e,n){if(a(t),e=s(e),a(n),"function"==typeof t&&"prototype"===e&&"value"in n&&d in n&&!n[d]){var r=l(t,e);r&&r[d]&&(t[e]=n.value,n={configurable:h in n?n[h]:r[h],enumerable:f in n?n[f]:r[f],writable:!1})}return c(t,e,n)}:c:function(t,e,n){if(a(t),e=s(e),a(n),i)try{return c(t,e,n)}catch(t){}if("get"in n||"set"in n)throw new u("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},5031:function(t,e,n){"use strict";var r=n(7751),i=n(9504),o=n(8480),a=n(3717),s=n(8551),u=i([].concat);t.exports=r("Reflect","ownKeys")||function(t){var e=o.f(s(t)),n=a.f;return n?u(e,n(t)):e}},5044:function(t,e,n){"use strict";var r=n(4644),i=n(6754),o=n(5854),a=n(6955),s=n(9565),u=n(9504),c=n(9039),l=r.aTypedArray,f=r.exportTypedArrayMethod,h=u("".slice);f("fill",(function(t){var e=arguments.length;l(this);var n="Big"===h(a(this),0,3)?o(t):+t;return s(i,this,n,e>1?arguments[1]:void 0,e>2?arguments[2]:void 0)}),c((function(){var t=0;return new Int8Array(2).fill({valueOf:function(){return t++}}),1!==t})))},5070:function(t,e,n){"use strict";var r=n(6518),i=n(3250);r({target:"Math",stat:!0,forced:i!==Math.expm1},{expm1:i})},5172:function(t){t.exports=function(t,e){this.v=t,this.k=e},t.exports.__esModule=!0,t.exports.default=t.exports},5213:function(t,e,n){"use strict";var r=n(4576),i=n(9039),o=r.RegExp,a=!i((function(){var t=!0;try{o(".","d")}catch(e){t=!1}var e={},n="",r=t?"dgimsy":"gimsy",i=function(t,r){Object.defineProperty(e,t,{get:function(){return n+=r,!0}})},a={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var s in t&&(a.hasIndices="d"),a)i(s,a[s]);return Object.getOwnPropertyDescriptor(o.prototype,"flags").get.call(e)!==r||n!==r}));t.exports={correct:a}},5240:function(t,e,n){"use strict";n(6468)("WeakSet",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),n(4006))},5276:function(t,e,n){"use strict";var r=n(6518),i=n(7476),o=n(9617).indexOf,a=n(4598),s=i([].indexOf),u=!!s&&1/s([1],1,-0)<0;r({target:"Array",proto:!0,forced:u||!a("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return u?s(this,t,e)||0:o(this,t,e)}})},5359:function(t){"use strict";var e=Math.log;t.exports=Math.log1p||function(t){var n=+t;return n>-1e-8&&n<1e-8?n-n*n/2:e(1+n)}},5370:function(t,e,n){"use strict";var r=n(6198);t.exports=function(t,e,n){for(var i=0,o=arguments.length>2?n:r(e),a=new t(o);o>i;)a[i]=e[i++];return a}},5374:function(t,e,n){"use strict";n(6518)({target:"Number",stat:!0,nonConfigurable:!0,nonWritable:!0},{EPSILON:Math.pow(2,-52)})},5376:function(t,e,n){"use strict";n(6518)({target:"Math",stat:!0},{log10:n(9340)})},5397:function(t,e,n){"use strict";var r=n(7055),i=n(7750);t.exports=function(t){return r(i(t))}},5440:function(t,e,n){"use strict";var r=n(8745),i=n(9565),o=n(9504),a=n(9228),s=n(9039),u=n(8551),c=n(4901),l=n(34),f=n(1291),h=n(8014),d=n(655),p=n(7750),v=n(7829),g=n(5966),y=n(2478),b=n(1034),m=n(6682),x=n(8227)("replace"),w=Math.max,k=Math.min,A=o([].concat),S=o([].push),E=o("".indexOf),_=o("".slice),T="$0"==="a".replace(/./,"$0"),O=!!/./[x]&&""===/./[x]("a","$0");a("replace",(function(t,e,n){var o=O?"$":"$0";return[function(t,n){var r=p(this),o=l(t)?g(t,x):void 0;return o?i(o,t,r,n):i(e,d(r),t,n)},function(t,i){var a=u(this),s=d(t);if("string"==typeof i&&-1===E(i,o)&&-1===E(i,"$<")){var l=n(e,a,s,i);if(l.done)return l.value}var p=c(i);p||(i=d(i));var g,x=d(b(a)),T=-1!==E(x,"g");T&&(g=-1!==E(x,"u"),a.lastIndex=0);for(var O,M=[];null!==(O=m(a,s))&&(S(M,O),T);)""===d(O[0])&&(a.lastIndex=v(s,h(a.lastIndex),g));for(var L,C="",I=0,R=0;R<M.length;R++){for(var j,P=d((O=M[R])[0]),F=w(k(f(O.index),s.length),0),N=[],D=1;D<O.length;D++)S(N,void 0===(L=O[D])?L:String(L));var z=O.groups;if(p){var B=A([P],N,F,s);void 0!==z&&S(B,z),j=d(r(i,void 0,B))}else j=y(P,s,F,N,z,i);F>=I&&(C+=_(s,I,F)+j,I=F+P.length)}return C+_(s,I)}]}),!!s((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!T||O)},5457:function(t,e,n){"use strict";n.a(t,(async function(t,e){try{var r=n(545),i=n(296),o=n(467),a=n(4467),s=n(3029),u=n(2901),c=n(6822),l=n(3954),f=n(5501),h=n(8614),d=n(6218),p=n(600),v=n(4756),g=n.n(v),y=n(2248),b=n(2419),m=n(9557),x=n(9570);function F(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=N(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function N(t,e){if(t){if("string"==typeof t)return D(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?D(t,e):void 0}}function D(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function z(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function B(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?z(Object(n),!0).forEach((function(e){(0,a.A)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):z(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function U(t,e,n){return e=(0,l.A)(e),(0,c.A)(t,W()?Reflect.construct(e,n||[],(0,l.A)(t).constructor):e.apply(t,n))}function W(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(W=function(){return!!t})()}function H(t,e,n){q(t,e),e.set(t,n)}function q(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function V(t,e,n){return t.set(G(t,e),n),n}function $(t,e){return t.get(G(t,e))}function G(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}function X(t){var e,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(e=t[n]))return e.call(t);if(r&&null!=(e=t[r]))return new Y(e.call(t));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Y(t){function e(t){if(Object(t)!==t)return Promise.reject(new TypeError(t+" is not an object."));var e=t.done;return Promise.resolve(t.value).then((function(t){return{value:t,done:e}}))}return Y=function(t){this.s=t,this.n=t.next},Y.prototype={s:null,n:null,next:function(){return e(this.n.apply(this.s,arguments))},return:function(t){var n=this.s.return;return void 0===n?Promise.resolve({value:t,done:!0}):e(n.apply(this.s,arguments))},throw:function(t){var n=this.s.return;return void 0===n?Promise.reject(t):e(n.apply(this.s,arguments))}},new Y(t)}var w=(await Promise.resolve().then(n.bind(n,7631))).TTS,k="foliate-search:",A=new WeakMap,S=new WeakMap,E=function(t){function e(){var t;(0,s.A)(this,e);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return H(t=U(this,e,[].concat(r)),A,[]),H(t,S,-1),t}return(0,f.A)(e,t),(0,u.A)(e,[{key:"pushState",value:function(t){var e,n=$(A,this)[$(S,this)];n===t||null!=n&&n.fraction&&n.fraction===t.fraction||($(A,this)[V(S,this,(e=$(S,this),++e))]=t,$(A,this).length=$(S,this)+1,this.dispatchEvent(new Event("index-change")),this.dispatchEvent(new CustomEvent("pushstate",{detail:t})))}},{key:"replaceState",value:function(t){var e=$(S,this);$(A,this)[e]=t}},{key:"back",value:function(){var t=$(S,this);if(!(t<=0)){var e={state:$(A,this)[t-1]};V(S,this,t-1),this.dispatchEvent(new CustomEvent("popstate",{detail:e})),this.dispatchEvent(new Event("index-change"))}}},{key:"forward",value:function(){var t=$(S,this);if(!(t>=$(A,this).length-1)){var e={state:$(A,this)[t+1]};V(S,this,t+1),this.dispatchEvent(new CustomEvent("popstate",{detail:e})),this.dispatchEvent(new Event("index-change"))}}},{key:"canGoBack",get:function(){return $(S,this)>0}},{key:"canGoForward",get:function(){return $(S,this)<$(A,this).length-1}},{key:"clear",value:function(){V(A,this,[]),V(S,this,-1)}}])}((0,h.A)(EventTarget)),_=function(t){if(!t)return{};try{var e,n,r,i,o=null!==(e=Intl.getCanonicalLocales(t)[0])&&void 0!==e?e:"en",a=new Intl.Locale(o);return{canonical:o,locale:a,isCJK:["zh","ja","kr"].includes(a.language),direction:null===(n=null!==(r=null===(i=a.getTextInfo)||void 0===i?void 0:i.call(a))&&void 0!==r?r:a.textInfo)||void 0===n?void 0:n.direction}}catch(t){return console.warn(t),{}}},T=new WeakMap,O=new WeakMap,M=new WeakMap,L=new WeakMap,C=new WeakMap,I=new WeakMap,R=new WeakMap,j=new WeakSet,P=function(t){function e(){var t,n,r;return(0,s.A)(this,e),q(n=t=U(this,e),r=j),r.add(n),H(t,T,t.attachShadow({mode:"open"})),H(t,O,void 0),H(t,M,void 0),H(t,L,void 0),H(t,C,new Map),H(t,I,void 0),(0,a.A)(t,"isFixedLayout",!1),(0,a.A)(t,"lastLocation",void 0),(0,a.A)(t,"history",new E),H(t,R,null),(0,a.A)(t,"oldValue",null),t.history.addEventListener("popstate",(function(e){var n=e.detail,r=t.resolveNavigation(n.state);t.renderer.goTo(r)})),t}return(0,f.A)(e,t),(0,u.A)(e,[{key:"open",value:(D=(0,o.A)(g().mark((function t(e){var r,i,o,a,s,u,c,l,f,h,d=this;return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.book=e,this.language=_(null===(r=e.metadata)||void 0===r?void 0:r.language),!e.splitTOCHref||!e.getTOCFragment){t.next=2;break}return s=e.sections.map((function(t){return t.id})),V(O,this,new b.r(e.sections,1500,1600)),u=e.splitTOCHref.bind(e),c=e.getTOCFragment.bind(e),V(M,this,new b.k),t.next=1,$(M,this).init({toc:null!==(o=e.toc)&&void 0!==o?o:[],ids:s,splitHref:u,getFragment:c});case 1:return V(L,this,new b.k),t.next=2,$(L,this).init({toc:null!==(a=e.pageList)&&void 0!==a?a:[],ids:s,splitHref:u,getFragment:c});case 2:if(this.isFixedLayout="pre-paginated"===(null===(i=this.book.rendition)||void 0===i?void 0:i.layout),!this.isFixedLayout){t.next=4;break}return t.next=3,Promise.resolve().then(n.bind(n,823));case 3:this.renderer=document.createElement("foliate-fxl"),t.next=6;break;case 4:return t.next=5,Promise.resolve().then(n.bind(n,7541));case 5:this.renderer=document.createElement("foliate-paginator");case 6:this.renderer.setAttribute("exportparts","head,foot,filter"),this.renderer.addEventListener("load",(function(t){return G(j,d,Z).call(d,t.detail)})),this.renderer.addEventListener("relocate",(function(t){return G(j,d,J).call(d,t.detail)})),this.renderer.addEventListener("create-overlayer",(function(t){return t.detail.attach(G(j,d,rt).call(d,t.detail))})),this.renderer.open(e),$(T,this).append(this.renderer),e.sections.some((function(t){return t.mediaOverlay}))&&((l=e.media).activeClass||(l.activeClass="-epub-media-overlay-active"),f=e.media.activeClass,this.mediaOverlay=e.getMediaOverlay(),this.mediaOverlay.addEventListener("highlight",(function(t){var e=d.resolveNavigation(t.detail.text);d.renderer.goTo(e).then((function(){var t=d.renderer.getContents().find((function(t){return t.index=e.index})).doc,n=e.anchor(t);n.classList.add(f),h=new WeakRef(n)}))})),this.mediaOverlay.addEventListener("unhighlight",(function(){var t;null===(t=h)||void 0===t||null===(t=t.deref())||void 0===t||null===(t=t.classList)||void 0===t||t.remove(f)})));case 7:case"end":return t.stop()}}),t,this)}))),function(t){return D.apply(this,arguments)})},{key:"close",value:function(){var t,e;null===(t=this.renderer)||void 0===t||t.destroy(),null===(e=this.renderer)||void 0===e||e.remove(),V(O,this,null),V(M,this,null),V(L,this,null),V(C,this,new Map),this.lastLocation=null,this.history.clear(),this.tts=null,this.mediaOverlay=null}},{key:"goToTextStart",value:function(){var t,e;return this.goTo(null!==(t=null===(e=this.book.landmarks)||void 0===e||null===(e=e.find((function(t){return t.type.includes("bodymatter")||t.type.includes("text")})))||void 0===e?void 0:e.href)&&void 0!==t?t:this.book.sections.findIndex((function(t){return"no"!==t.linear})))}},{key:"init",value:(N=(0,o.A)(g().mark((function t(e){var n,r,i;return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.lastLocation,r=e.showTextStart,!(i=n?this.resolveNavigation(n):null)){t.next=2;break}return t.next=1,this.renderer.goTo(i);case 1:this.history.pushState(n),t.next=5;break;case 2:if(!r){t.next=4;break}return t.next=3,this.goToTextStart();case 3:t.next=5;break;case 4:return this.history.pushState(0),t.next=5,this.next();case 5:case"end":return t.stop()}}),t,this)}))),function(t){return N.apply(this,arguments)})},{key:"addAnnotation",value:(P=(0,o.A)(g().mark((function t(e,n){var r,i,o,a,s,u,c,l,f,h,d,p,v,y,b,x,w,A,S,E;return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(o=e.value).startsWith(k)){t.next=4;break}return a=o.replace(k,""),t.next=1,this.resolveNavigation(a);case 1:if(s=t.sent,u=s.index,c=s.anchor,!(l=G(j,this,nt).call(this,u))){t.next=3;break}if(f=l.overlayer,h=l.doc,!n){t.next=2;break}return f.remove(o),t.abrupt("return");case 2:d=h?c(h):c,f.add(o,d,m.u.outline,{color:"#39c5bbaa"});case 3:return t.abrupt("return");case 4:return t.next=5,this.resolveNavigation(o);case 5:return p=t.sent,v=p.index,y=p.anchor,(b=G(j,this,nt).call(this,v))&&(x=b.overlayer,w=b.doc,x.remove(o),n||(A=w?y(w):y,S=function(t,e){return x.add(o,A,t,e)},G(j,this,K).call(this,"draw-annotation",{draw:S,annotation:e,doc:w,range:A}))),E=null!==(r=null===(i=$(M,this).getProgress(v))||void 0===i?void 0:i.label)&&void 0!==r?r:"",t.abrupt("return",{index:v,label:E});case 6:case"end":return t.stop()}}),t,this)}))),function(t,e){return P.apply(this,arguments)})},{key:"deleteAnnotation",value:function(t){return this.addAnnotation(t,!0)}},{key:"showAnnotation",value:(S=(0,o.A)(g().mark((function t(e){var n,r,i,o,a,s,u;return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.value,t.next=1,this.goTo(n);case 1:(r=t.sent)&&(i=r.index,o=r.anchor,a=G(j,this,nt).call(this,i),s=a.doc,u=o(s),G(j,this,K).call(this,"show-annotation",{value:n,index:i,range:u}));case 2:case"end":return t.stop()}}),t,this)}))),function(t){return S.apply(this,arguments)})},{key:"getCFI",value:function(t,e){var n,r=null!==(n=this.book.sections[t].cfi)&&void 0!==n?n:y.EL.fromIndex(t);return e?y.RK(r,y.yT(e)):r}},{key:"resolveCFI",value:function(t){if(this.book.resolveCFI)return this.book.resolveCFI(t);var e,n=y.qg(t);return{index:y.EL.toIndex((null!==(e=n.parent)&&void 0!==e?e:n).shift()),anchor:function(t){return y.Xp(t,n)}}}},{key:"resolveNavigation",value:function(t){try{if("number"==typeof t)return{index:t};if("number"==typeof t.fraction){var e=$(O,this).getSection(t.fraction),n=(0,i.A)(e,2);return{index:n[0],anchor:n[1]}}return y.Lu.test(t)?this.resolveCFI(t):this.book.resolveHref(t)}catch(e){console.error(e),console.error("Could not resolve target ".concat(t))}}},{key:"goTo",value:(A=(0,o.A)(g().mark((function t(e){var n,r;return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=this.resolveNavigation(e),t.prev=1,t.next=2,this.renderer.goTo(n);case 2:return this.history.pushState(e),t.abrupt("return",n);case 3:t.prev=3,r=t.catch(1),console.error(r),console.error("Could not go to ".concat(e));case 4:case"end":return t.stop()}}),t,this,[[1,3]])}))),function(t){return A.apply(this,arguments)})},{key:"goToFraction",value:(v=(0,o.A)(g().mark((function t(e){var n,r,o,a;return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=$(O,this).getSection(e),r=(0,i.A)(n,2),o=r[0],a=r[1],t.next=1,this.renderer.goTo({index:o,anchor:a});case 1:this.history.pushState({fraction:e});case 2:case"end":return t.stop()}}),t,this)}))),function(t){return v.apply(this,arguments)})},{key:"select",value:(h=(0,o.A)(g().mark((function t(e){var n,r;return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=1,this.resolveNavigation(e);case 1:return n=t.sent,t.next=2,this.renderer.goTo(B(B({},n),{},{select:!0}));case 2:this.history.pushState(e),t.next=4;break;case 3:t.prev=3,r=t.catch(0),console.error(r),console.error("Could not go to ".concat(e));case 4:case"end":return t.stop()}}),t,this,[[0,3]])}))),function(t){return h.apply(this,arguments)})},{key:"deselect",value:function(){var t,e=F(this.renderer.getContents());try{for(e.s();!(t=e.n()).done;)t.value.doc.defaultView.getSelection().removeAllRanges()}catch(t){e.e(t)}finally{e.f()}}},{key:"getSectionFractions",value:function(){var t,e,n,r,i=null!==(t=null===(e=$(M,this))||void 0===e?void 0:e.ids)&&void 0!==t?t:[];return(null!==(n=null===(r=$(O,this))||void 0===r?void 0:r.sectionFractions)&&void 0!==n?n:[]).map((function(t,e){var n;return{fraction:t,href:null!==(n=i[e])&&void 0!==n?n:"",index:e}}))}},{key:"getProgressOf",value:function(t,e){var n,r;return{tocItem:null===(n=$(M,this))||void 0===n?void 0:n.getProgress(t,e),pageItem:null===(r=$(L,this))||void 0===r?void 0:r.getProgress(t,e)}}},{key:"getTOCItemOf",value:(l=(0,o.A)(g().mark((function t(e){var n,r,i,o,a,s,u,c;return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=1,this.resolveNavigation(e);case 1:return n=t.sent,r=n.index,i=n.anchor,t.next=2,this.book.sections[r].createDocument();case 2:return o=t.sent,a=i(o),s=a instanceof Range,u=s?a:o.createRange(),s||u.selectNodeContents(a),t.abrupt("return",$(M,this).getProgress(r,u));case 3:t.prev=3,c=t.catch(0),console.error(c),console.error("Could not get ".concat(e));case 4:case"end":return t.stop()}}),t,this,[[0,3]])}))),function(t){return l.apply(this,arguments)})},{key:"prev",value:(c=(0,o.A)(g().mark((function t(e){return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,this.renderer.prev(e);case 1:case"end":return t.stop()}}),t,this)}))),function(t){return c.apply(this,arguments)})},{key:"next",value:(r=(0,o.A)(g().mark((function t(e){return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,this.renderer.next(e);case 1:case"end":return t.stop()}}),t,this)}))),function(t){return r.apply(this,arguments)})},{key:"goLeft",value:function(){return"rtl"===this.book.dir?this.next():this.prev()}},{key:"goRight",value:function(){return"rtl"===this.book.dir?this.prev():this.next()}},{key:"search",value:function(t){var e=this;return(0,p.A)(g().mark((function r(){var i,o,a,s,u,c,l,f,h,p,v,y,b,m,w,A,S,E,_,T,O;return g().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return console.log("search",t),e.clearSearch(),r.next=1,(0,d.A)(Promise.resolve().then(n.bind(n,9072)));case 1:i=r.sent,o=i.searchMatcher,a=t.query,s=t.index,u=o(x.u,B({defaultLocale:e.language},t)),c=null!=s?G(j,e,it).call(e,u,a,s):G(j,e,ot).call(e,u,a),l=[],$(C,e).set(s,l),f=!1,h=!1,r.prev=2,v=X(c);case 3:return r.next=4,(0,d.A)(v.next());case 4:if(!(f=!(y=r.sent).done)){r.next=8;break}if(!(b=y.value).subitems){r.next=6;break}A=b.subitems.map((function(t){var e=t.cfi;return{value:k+e}})),$(C,e).set(b.index,A),S=F(A);try{for(S.s();!(E=S.n()).done;)_=E.value,e.addAnnotation(_)}catch(t){S.e(t)}finally{S.f()}return r.next=5,{label:null!==(m=null===(w=$(M,e).getProgress(b.index))||void 0===w?void 0:w.label)&&void 0!==m?m:"",subitems:b.subitems};case 5:r.next=7;break;case 6:return b.cfi&&(T={value:k+b.cfi},l.push(T),e.addAnnotation(T)),r.next=7,b;case 7:f=!1,r.next=3;break;case 8:r.next=10;break;case 9:r.prev=9,O=r.catch(2),h=!0,p=O;case 10:if(r.prev=10,r.prev=11,!f||null==v.return){r.next=12;break}return r.next=12,(0,d.A)(v.return());case 12:if(r.prev=12,!h){r.next=13;break}throw p;case 13:return r.finish(12);case 14:return r.finish(10);case 15:return r.next=16,"done";case 16:case"end":return r.stop()}}),r,null,[[2,9,10,15],[11,,12,14]])})))()}},{key:"clearSearch",value:function(){var t,e=F($(C,this).values());try{for(e.s();!(t=e.n()).done;){var n,r=F(t.value);try{for(r.s();!(n=r.n()).done;){var i=n.value;this.deleteAnnotation(i)}}catch(t){r.e(t)}finally{r.f()}}}catch(t){e.e(t)}finally{e.f()}$(C,this).clear()}},{key:"initTTS",value:function(t){var e,n=this;if(t)return null===(e=G(j,this,nt).call(this,$(I,this)))||void 0===e?void 0:e.overlayer.remove(this.oldValue);var r=this.renderer.getContents()[0].doc;this.tts&&this.tts.doc===r||(this.tts=new w(r,x.u,(function(t){var e=G(j,n,nt).call(n,$(I,n));if(e){var r=e.overlayer;n.oldValue&&r.remove(n.oldValue);var i=n.getCFI($(I,n),t);r.add(i,t,m.u.squiggly,{color:"#39c5bb"}),n.oldValue=i}n.renderer.scrollToAnchor(t)})))}},{key:"startMediaOverlay",value:function(){var t=this.renderer.getContents()[0].index;return this.mediaOverlay.start(t)}}]);var r,c,l,h,v,A,S,P,N,D}((0,h.A)(HTMLElement));function K(t,e,n){return this.dispatchEvent(new CustomEvent(t,{detail:e,cancelable:n}))}function J(t){var e,n,r,i,o,a=t.reason,s=t.range,u=t.index,c=t.fraction,l=t.size;V(I,this,u);var f=null!==(e=null===(n=$(O,this))||void 0===n?void 0:n.getProgress(u,c,l))&&void 0!==e?e:{},h=null===(r=$(M,this))||void 0===r?void 0:r.getProgress(u,s),d=null===(i=$(L,this))||void 0===i?void 0:i.getProgress(u,s),p=this.getCFI(u,s),v=this.renderer.pages?this.renderer.pages-2:f.section.total,g={current:null!==(o=this.renderer.page)&&void 0!==o?o:f.section.current,total:v};this.lastLocation=B(B({},f),{},{tocItem:h,pageItem:d,cfi:p,range:s,chapterLocation:g}),"snap"!==a&&"page"!==a&&"scroll"!==a||this.history.replaceState(p),!p||$(R,this)&&p===$(R,this)||(V(R,this,p),G(j,this,K).call(this,"relocate",this.lastLocation))}function Z(t){var e,n,r,i,o=t.doc,a=t.index;(e=o.documentElement).lang||(e.lang=null!==(n=this.language.canonical)&&void 0!==n?n:""),this.language.isCJK||(r=o.documentElement).dir||(r.dir=null!==(i=this.language.direction)&&void 0!==i?i:""),G(j,this,Q).call(this,o,a),G(j,this,et).call(this,o),G(j,this,tt).call(this,o),G(j,this,K).call(this,"load",{doc:o,index:a})}function Q(t,e){var n,r=this,i=this.book,o=i.sections[e],a=F(t.querySelectorAll("a[href]"));try{var s=function(){var t=n.value;t.addEventListener("click",(function(e){var n,a,s;e.preventDefault(),e.stopPropagation();var u=t.getAttribute("href"),c=null!==(n=null==o||null===(a=o.resolveHref)||void 0===a?void 0:a.call(o,u))&&void 0!==n?n:u;null!=i&&null!==(s=i.isExternal)&&void 0!==s&&s.call(i,c)?Promise.resolve(G(j,r,K).call(r,"external-link",{a:t,href:c},!0)).then((function(t){return t?globalThis.open(c,"_blank"):null})).catch((function(t){return console.error(t)})):Promise.resolve(G(j,r,K).call(r,"link",{a:t,href:c},!0)).then((function(t){return t?r.goTo(c):null})).catch((function(t){return console.error(t)}))}))};for(a.s();!(n=a.n()).done;)s()}catch(t){a.e(t)}finally{a.f()}}function tt(t){var e,n=this,r=F(t.querySelectorAll("img"));try{var i=function(){var t=e.value;if(t.closest("a[href]"))return 1;t.addEventListener("click",(function(e){e.preventDefault(),e.stopPropagation(),G(j,n,K).call(n,"click-image",{img:t})}))};for(r.s();!(e=r.n()).done;)i()}catch(t){r.e(t)}finally{r.f()}}function et(t){var e=this;t.addEventListener("click",(function(n){if(window.isFootNoteOpen())window.closeFootNote();else if("Range"!==t.getSelection().type){var r=t.position,i=t.scale,o=n.clientX,a=n.clientY;if(r){o*=i,a*=i;var s=t.documentElement.getBoundingClientRect().width*i;return"right"===r&&2.2*s<window.innerWidth&&(o+=.5*window.innerWidth),void G(j,e,K).call(e,"click-view",{x:o,y:a})}e.renderer.vertical?"scrollLeft"==e.renderer.scrollProp?o=e.renderer.size-(e.renderer.viewSize-e.renderer.start-o):a-=e.renderer.start-e.renderer.size:"scrollLeft"==e.renderer.scrollProp?o-=e.renderer.start-e.renderer.size:a-=e.renderer.start,G(j,e,K).call(e,"click-view",{x:o,y:a})}})),this.renderer.addEventListener("click",(function(t){for(var n=t.clientX,i=t.clientY;n>window.innerWidth;)window.innerWidth,(0,r.A)("clientX");G(j,e,K).call(e,"click-view",{x:n,y:i})}))}function nt(t){return this.renderer.getContents().find((function(e){return e.index===t&&e.overlayer}))}function rt(t){var e=this,n=t.doc,r=t.index,o=new m.u;n.addEventListener("click",(function(t){var n=o.hitTest(t),a=(0,i.A)(n,2),s=a[0],u=a[1];s&&!s.startsWith(k)&&(t.preventDefault(),t.stopPropagation(),G(j,e,K).call(e,"show-annotation",{value:s,index:r,range:u}))}),!0);var a=$(C,this).get(r);if(a){var s,u=F(a);try{for(u.s();!(s=u.n()).done;){var c=s.value;this.addAnnotation(c)}}catch(t){u.e(t)}finally{u.f()}}return G(j,this,K).call(this,"create-overlay",{index:r}),o}function it(t,e,n){var r=this;return(0,p.A)(g().mark((function i(){var o,a,s,u,c,l,f;return g().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=1,(0,d.A)(r.book.sections[n].createDocument());case 1:o=i.sent,a=F(t(o,e)),i.prev=2,a.s();case 3:if((s=a.n()).done){i.next=5;break}return u=s.value,c=u.range,l=u.excerpt,i.next=4,{cfi:r.getCFI(n,c),excerpt:l};case 4:i.next=3;break;case 5:i.next=7;break;case 6:i.prev=6,f=i.catch(2),a.e(f);case 7:return i.prev=7,a.f(),i.finish(7);case 8:case"end":return i.stop()}}),i,null,[[2,6,7,8]])})))()}function ot(t,e){var n=this;return(0,p.A)(g().mark((function r(){var o,a,s,u,c;return g().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:o=n.book.sections,a=F(o.entries()),r.prev=1,u=g().mark((function r(){var a,u,c,l,f,h;return g().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(a=(0,i.A)(s.value,2),u=a[0],c=a[1].createDocument){r.next=1;break}return r.abrupt("return",1);case 1:return r.next=2,(0,d.A)(c());case 2:return l=r.sent,f=Array.from(t(l,e),(function(t){var e=t.range,r=t.excerpt;return{cfi:n.getCFI(u,e),excerpt:r}})),h=(u+1)/o.length,r.next=3,{progress:h};case 3:if(!f.length){r.next=4;break}return r.next=4,{index:u,subitems:f};case 4:case"end":return r.stop()}}),r)})),a.s();case 2:if((s=a.n()).done){r.next=5;break}return r.delegateYield(u(),"t0",3);case 3:if(!r.t0){r.next=4;break}return r.abrupt("continue",4);case 4:r.next=2;break;case 5:r.next=7;break;case 6:r.prev=6,c=r.catch(1),a.e(c);case 7:return r.prev=7,a.f(),r.finish(7);case 8:case"end":return r.stop()}}),r,null,[[1,6,7,8]])})))()}customElements.define("foliate-view",P),e()}catch(at){e(at)}}),1)},5458:function(t,e,n){"use strict";n.d(e,{A:function(){return o}});var r=n(3145),i=n(7800);function o(t){return function(t){if(Array.isArray(t))return(0,r.A)(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||(0,i.A)(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},5477:function(t,e,n){"use strict";n(5823)("Int32",(function(t){return function(e,n,r){return t(this,e,n,r)}}))},5501:function(t,e,n){"use strict";n.d(e,{A:function(){return i}});var r=n(3662);function i(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&(0,r.A)(t,e)}},5506:function(t,e,n){"use strict";var r=n(6518),i=n(2357).entries;r({target:"Object",stat:!0},{entries:function(t){return i(t)}})},5546:function(t){function e(n,r,i,o){var a=Object.defineProperty;try{a({},"",{})}catch(n){a=0}t.exports=e=function(t,n,r,i){if(n)a?a(t,n,{value:r,enumerable:!i,configurable:!i,writable:!i}):t[n]=r;else{var o=function(n,r){e(t,n,(function(t){return this._invoke(n,r,t)}))};o("next",0),o("throw",1),o("return",2)}},t.exports.__esModule=!0,t.exports.default=t.exports,e(n,r,i,o)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},5548:function(t,e,n){"use strict";var r=n(3517),i=n(6823),o=TypeError;t.exports=function(t){if(r(t))return t;throw new o(i(t)+" is not a constructor")}},5610:function(t,e,n){"use strict";var r=n(1291),i=Math.max,o=Math.min;t.exports=function(t,e){var n=r(t);return n<0?i(n+e,0):o(n,e)}},5617:function(t,e,n){"use strict";var r=n(3164);t.exports=Math.fround||function(t){return r(t,1.1920928955078125e-7,34028234663852886e22,11754943508222875e-54)}},5652:function(t,e,n){"use strict";var r=n(9039);t.exports=r((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}}))},5700:function(t,e,n){"use strict";var r=n(511),i=n(8242);r("toPrimitive"),i()},5701:function(t,e,n){"use strict";var r=n(6518),i=n(533).end;r({target:"String",proto:!0,forced:n(3063)},{padEnd:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},5745:function(t,e,n){"use strict";var r=n(7629);t.exports=function(t,e){return r[t]||(r[t]=e||{})}},5746:function(t,e,n){"use strict";var r=n(9565),i=n(9228),o=n(8551),a=n(34),s=n(7750),u=n(3470),c=n(655),l=n(5966),f=n(6682);i("search",(function(t,e,n){return[function(e){var n=s(this),i=a(e)?l(e,t):void 0;return i?r(i,e,n):new RegExp(e)[t](c(n))},function(t){var r=o(this),i=c(t),a=n(e,r,i);if(a.done)return a.value;var s=r.lastIndex;u(s,0)||(r.lastIndex=0);var l=f(r,i);return u(r.lastIndex,s)||(r.lastIndex=s),null===l?-1:l.index}]}))},5749:function(t,e,n){"use strict";var r=n(788),i=TypeError;t.exports=function(t){if(r(t))throw new i("The method doesn't accept regular expressions");return t}},5806:function(t,e,n){"use strict";n(7764);var r,i=n(6518),o=n(3724),a=n(7416),s=n(4576),u=n(6080),c=n(9504),l=n(6840),f=n(2106),h=n(679),d=n(9297),p=n(4213),v=n(7916),g=n(7680),y=n(8183).codeAt,b=n(6098),m=n(655),x=n(687),w=n(2812),k=n(8406),A=n(1181),S=A.set,E=A.getterFor("URL"),_=k.URLSearchParams,T=k.getState,O=s.URL,M=s.TypeError,L=s.parseInt,C=Math.floor,I=Math.pow,R=c("".charAt),j=c(/./.exec),P=c([].join),F=c(1.1.toString),N=c([].pop),D=c([].push),z=c("".replace),B=c([].shift),U=c("".split),W=c("".slice),H=c("".toLowerCase),q=c([].unshift),V="Invalid scheme",$="Invalid host",G="Invalid port",X=/[a-z]/i,Y=/[\d+-.a-z]/i,K=/\d/,J=/^0x/i,Z=/^[0-7]+$/,Q=/^\d+$/,tt=/^[\da-f]+$/i,et=/[\0\t\n\r #%/:<>?@[\\\]^|]/,nt=/[\0\t\n\r #/:<>?@[\\\]^|]/,rt=/^[\u0000-\u0020]+/,it=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,ot=/[\t\n\r]/g,at=function(t){var e,n,r,i;if("number"==typeof t){for(e=[],n=0;n<4;n++)q(e,t%256),t=C(t/256);return P(e,".")}if("object"==typeof t){for(e="",r=function(t){for(var e=null,n=1,r=null,i=0,o=0;o<8;o++)0!==t[o]?(i>n&&(e=r,n=i),r=null,i=0):(null===r&&(r=o),++i);return i>n?r:e}(t),n=0;n<8;n++)i&&0===t[n]||(i&&(i=!1),r===n?(e+=n?":":"::",i=!0):(e+=F(t[n],16),n<7&&(e+=":")));return"["+e+"]"}return t},st={},ut=p({},st,{" ":1,'"':1,"<":1,">":1,"`":1}),ct=p({},ut,{"#":1,"?":1,"{":1,"}":1}),lt=p({},ct,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),ft=function(t,e){var n=y(t,0);return n>32&&n<127&&!d(e,t)?t:encodeURIComponent(t)},ht={ftp:21,file:null,http:80,https:443,ws:80,wss:443},dt=function(t,e){var n;return 2===t.length&&j(X,R(t,0))&&(":"===(n=R(t,1))||!e&&"|"===n)},pt=function(t){var e;return t.length>1&&dt(W(t,0,2))&&(2===t.length||"/"===(e=R(t,2))||"\\"===e||"?"===e||"#"===e)},vt=function(t){return"."===t||"%2e"===H(t)},gt={},yt={},bt={},mt={},xt={},wt={},kt={},At={},St={},Et={},_t={},Tt={},Ot={},Mt={},Lt={},Ct={},It={},Rt={},jt={},Pt={},Ft={},Nt=function(t,e,n){var r,i,o,a=m(t);if(e){if(i=this.parse(a))throw new M(i);this.searchParams=null}else{if(void 0!==n&&(r=new Nt(n,!0)),i=this.parse(a,null,r))throw new M(i);(o=T(new _)).bindURL(this),this.searchParams=o}};Nt.prototype={type:"URL",parse:function(t,e,n){var i,o,a,s,u,c=this,l=e||gt,f=0,h="",p=!1,y=!1,b=!1;for(t=m(t),e||(c.scheme="",c.username="",c.password="",c.host=null,c.port=null,c.path=[],c.query=null,c.fragment=null,c.cannotBeABaseURL=!1,t=z(t,rt,""),t=z(t,it,"$1")),t=z(t,ot,""),i=v(t);f<=i.length;){switch(o=i[f],l){case gt:if(!o||!j(X,o)){if(e)return V;l=bt;continue}h+=H(o),l=yt;break;case yt:if(o&&(j(Y,o)||"+"===o||"-"===o||"."===o))h+=H(o);else{if(":"!==o){if(e)return V;h="",l=bt,f=0;continue}if(e&&(c.isSpecial()!==d(ht,h)||"file"===h&&(c.includesCredentials()||null!==c.port)||"file"===c.scheme&&!c.host))return;if(c.scheme=h,e)return void(c.isSpecial()&&ht[c.scheme]===c.port&&(c.port=null));h="","file"===c.scheme?l=Mt:c.isSpecial()&&n&&n.scheme===c.scheme?l=mt:c.isSpecial()?l=At:"/"===i[f+1]?(l=xt,f++):(c.cannotBeABaseURL=!0,D(c.path,""),l=jt)}break;case bt:if(!n||n.cannotBeABaseURL&&"#"!==o)return V;if(n.cannotBeABaseURL&&"#"===o){c.scheme=n.scheme,c.path=g(n.path),c.query=n.query,c.fragment="",c.cannotBeABaseURL=!0,l=Ft;break}l="file"===n.scheme?Mt:wt;continue;case mt:if("/"!==o||"/"!==i[f+1]){l=wt;continue}l=St,f++;break;case xt:if("/"===o){l=Et;break}l=Rt;continue;case wt:if(c.scheme=n.scheme,o===r)c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=g(n.path),c.query=n.query;else if("/"===o||"\\"===o&&c.isSpecial())l=kt;else if("?"===o)c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=g(n.path),c.query="",l=Pt;else{if("#"!==o){c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=g(n.path),c.path.length--,l=Rt;continue}c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,c.path=g(n.path),c.query=n.query,c.fragment="",l=Ft}break;case kt:if(!c.isSpecial()||"/"!==o&&"\\"!==o){if("/"!==o){c.username=n.username,c.password=n.password,c.host=n.host,c.port=n.port,l=Rt;continue}l=Et}else l=St;break;case At:if(l=St,"/"!==o||"/"!==R(h,f+1))continue;f++;break;case St:if("/"!==o&&"\\"!==o){l=Et;continue}break;case Et:if("@"===o){p&&(h="%40"+h),p=!0,a=v(h);for(var x=0;x<a.length;x++){var w=a[x];if(":"!==w||b){var k=ft(w,lt);b?c.password+=k:c.username+=k}else b=!0}h=""}else if(o===r||"/"===o||"?"===o||"#"===o||"\\"===o&&c.isSpecial()){if(p&&""===h)return"Invalid authority";f-=v(h).length+1,h="",l=_t}else h+=o;break;case _t:case Tt:if(e&&"file"===c.scheme){l=Ct;continue}if(":"!==o||y){if(o===r||"/"===o||"?"===o||"#"===o||"\\"===o&&c.isSpecial()){if(c.isSpecial()&&""===h)return $;if(e&&""===h&&(c.includesCredentials()||null!==c.port))return;if(s=c.parseHost(h))return s;if(h="",l=It,e)return;continue}"["===o?y=!0:"]"===o&&(y=!1),h+=o}else{if(""===h)return $;if(s=c.parseHost(h))return s;if(h="",l=Ot,e===Tt)return}break;case Ot:if(!j(K,o)){if(o===r||"/"===o||"?"===o||"#"===o||"\\"===o&&c.isSpecial()||e){if(""!==h){var A=L(h,10);if(A>65535)return G;c.port=c.isSpecial()&&A===ht[c.scheme]?null:A,h=""}if(e)return;l=It;continue}return G}h+=o;break;case Mt:if(c.scheme="file","/"===o||"\\"===o)l=Lt;else{if(!n||"file"!==n.scheme){l=Rt;continue}switch(o){case r:c.host=n.host,c.path=g(n.path),c.query=n.query;break;case"?":c.host=n.host,c.path=g(n.path),c.query="",l=Pt;break;case"#":c.host=n.host,c.path=g(n.path),c.query=n.query,c.fragment="",l=Ft;break;default:pt(P(g(i,f),""))||(c.host=n.host,c.path=g(n.path),c.shortenPath()),l=Rt;continue}}break;case Lt:if("/"===o||"\\"===o){l=Ct;break}n&&"file"===n.scheme&&!pt(P(g(i,f),""))&&(dt(n.path[0],!0)?D(c.path,n.path[0]):c.host=n.host),l=Rt;continue;case Ct:if(o===r||"/"===o||"\\"===o||"?"===o||"#"===o){if(!e&&dt(h))l=Rt;else if(""===h){if(c.host="",e)return;l=It}else{if(s=c.parseHost(h))return s;if("localhost"===c.host&&(c.host=""),e)return;h="",l=It}continue}h+=o;break;case It:if(c.isSpecial()){if(l=Rt,"/"!==o&&"\\"!==o)continue}else if(e||"?"!==o)if(e||"#"!==o){if(o!==r&&(l=Rt,"/"!==o))continue}else c.fragment="",l=Ft;else c.query="",l=Pt;break;case Rt:if(o===r||"/"===o||"\\"===o&&c.isSpecial()||!e&&("?"===o||"#"===o)){if(".."===(u=H(u=h))||"%2e."===u||".%2e"===u||"%2e%2e"===u?(c.shortenPath(),"/"===o||"\\"===o&&c.isSpecial()||D(c.path,"")):vt(h)?"/"===o||"\\"===o&&c.isSpecial()||D(c.path,""):("file"===c.scheme&&!c.path.length&&dt(h)&&(c.host&&(c.host=""),h=R(h,0)+":"),D(c.path,h)),h="","file"===c.scheme&&(o===r||"?"===o||"#"===o))for(;c.path.length>1&&""===c.path[0];)B(c.path);"?"===o?(c.query="",l=Pt):"#"===o&&(c.fragment="",l=Ft)}else h+=ft(o,ct);break;case jt:"?"===o?(c.query="",l=Pt):"#"===o?(c.fragment="",l=Ft):o!==r&&(c.path[0]+=ft(o,st));break;case Pt:e||"#"!==o?o!==r&&("'"===o&&c.isSpecial()?c.query+="%27":c.query+="#"===o?"%23":ft(o,st)):(c.fragment="",l=Ft);break;case Ft:o!==r&&(c.fragment+=ft(o,ut))}f++}},parseHost:function(t){var e,n,r;if("["===R(t,0)){if("]"!==R(t,t.length-1))return $;if(e=function(t){var e,n,r,i,o,a,s,u=[0,0,0,0,0,0,0,0],c=0,l=null,f=0,h=function(){return R(t,f)};if(":"===h()){if(":"!==R(t,1))return;f+=2,l=++c}for(;h();){if(8===c)return;if(":"!==h()){for(e=n=0;n<4&&j(tt,h());)e=16*e+L(h(),16),f++,n++;if("."===h()){if(0===n)return;if(f-=n,c>6)return;for(r=0;h();){if(i=null,r>0){if(!("."===h()&&r<4))return;f++}if(!j(K,h()))return;for(;j(K,h());){if(o=L(h(),10),null===i)i=o;else{if(0===i)return;i=10*i+o}if(i>255)return;f++}u[c]=256*u[c]+i,2!=++r&&4!==r||c++}if(4!==r)return;break}if(":"===h()){if(f++,!h())return}else if(h())return;u[c++]=e}else{if(null!==l)return;f++,l=++c}}if(null!==l)for(a=c-l,c=7;0!==c&&a>0;)s=u[c],u[c--]=u[l+a-1],u[l+--a]=s;else if(8!==c)return;return u}(W(t,1,-1)),!e)return $;this.host=e}else if(this.isSpecial()){if(t=b(t),j(et,t))return $;if(e=function(t){var e,n,r,i,o,a,s,u=U(t,".");if(u.length&&""===u[u.length-1]&&u.length--,(e=u.length)>4)return t;for(n=[],r=0;r<e;r++){if(""===(i=u[r]))return t;if(o=10,i.length>1&&"0"===R(i,0)&&(o=j(J,i)?16:8,i=W(i,8===o?1:2)),""===i)a=0;else{if(!j(10===o?Q:8===o?Z:tt,i))return t;a=L(i,o)}D(n,a)}for(r=0;r<e;r++)if(a=n[r],r===e-1){if(a>=I(256,5-e))return null}else if(a>255)return null;for(s=N(n),r=0;r<n.length;r++)s+=n[r]*I(256,3-r);return s}(t),null===e)return $;this.host=e}else{if(j(nt,t))return $;for(e="",n=v(t),r=0;r<n.length;r++)e+=ft(n[r],st);this.host=e}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"===this.scheme},includesCredentials:function(){return""!==this.username||""!==this.password},isSpecial:function(){return d(ht,this.scheme)},shortenPath:function(){var t=this.path,e=t.length;!e||"file"===this.scheme&&1===e&&dt(t[0],!0)||t.length--},serialize:function(){var t=this,e=t.scheme,n=t.username,r=t.password,i=t.host,o=t.port,a=t.path,s=t.query,u=t.fragment,c=e+":";return null!==i?(c+="//",t.includesCredentials()&&(c+=n+(r?":"+r:"")+"@"),c+=at(i),null!==o&&(c+=":"+o)):"file"===e&&(c+="//"),c+=t.cannotBeABaseURL?a[0]:a.length?"/"+P(a,"/"):"",null!==s&&(c+="?"+s),null!==u&&(c+="#"+u),c},setHref:function(t){var e=this.parse(t);if(e)throw new M(e);this.searchParams.update()},getOrigin:function(){var t=this.scheme,e=this.port;if("blob"===t)try{return new Dt(t.path[0]).origin}catch(t){return"null"}return"file"!==t&&this.isSpecial()?t+"://"+at(this.host)+(null!==e?":"+e:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(t){this.parse(m(t)+":",gt)},getUsername:function(){return this.username},setUsername:function(t){var e=v(m(t));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var n=0;n<e.length;n++)this.username+=ft(e[n],lt)}},getPassword:function(){return this.password},setPassword:function(t){var e=v(m(t));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var n=0;n<e.length;n++)this.password+=ft(e[n],lt)}},getHost:function(){var t=this.host,e=this.port;return null===t?"":null===e?at(t):at(t)+":"+e},setHost:function(t){this.cannotBeABaseURL||this.parse(t,_t)},getHostname:function(){var t=this.host;return null===t?"":at(t)},setHostname:function(t){this.cannotBeABaseURL||this.parse(t,Tt)},getPort:function(){var t=this.port;return null===t?"":m(t)},setPort:function(t){this.cannotHaveUsernamePasswordPort()||(""===(t=m(t))?this.port=null:this.parse(t,Ot))},getPathname:function(){var t=this.path;return this.cannotBeABaseURL?t[0]:t.length?"/"+P(t,"/"):""},setPathname:function(t){this.cannotBeABaseURL||(this.path=[],this.parse(t,It))},getSearch:function(){var t=this.query;return t?"?"+t:""},setSearch:function(t){""===(t=m(t))?this.query=null:("?"===R(t,0)&&(t=W(t,1)),this.query="",this.parse(t,Pt)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var t=this.fragment;return t?"#"+t:""},setHash:function(t){""!==(t=m(t))?("#"===R(t,0)&&(t=W(t,1)),this.fragment="",this.parse(t,Ft)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var Dt=function(t){var e=h(this,zt),n=w(arguments.length,1)>1?arguments[1]:void 0,r=S(e,new Nt(t,!1,n));o||(e.href=r.serialize(),e.origin=r.getOrigin(),e.protocol=r.getProtocol(),e.username=r.getUsername(),e.password=r.getPassword(),e.host=r.getHost(),e.hostname=r.getHostname(),e.port=r.getPort(),e.pathname=r.getPathname(),e.search=r.getSearch(),e.searchParams=r.getSearchParams(),e.hash=r.getHash())},zt=Dt.prototype,Bt=function(t,e){return{get:function(){return E(this)[t]()},set:e&&function(t){return E(this)[e](t)},configurable:!0,enumerable:!0}};if(o&&(f(zt,"href",Bt("serialize","setHref")),f(zt,"origin",Bt("getOrigin")),f(zt,"protocol",Bt("getProtocol","setProtocol")),f(zt,"username",Bt("getUsername","setUsername")),f(zt,"password",Bt("getPassword","setPassword")),f(zt,"host",Bt("getHost","setHost")),f(zt,"hostname",Bt("getHostname","setHostname")),f(zt,"port",Bt("getPort","setPort")),f(zt,"pathname",Bt("getPathname","setPathname")),f(zt,"search",Bt("getSearch","setSearch")),f(zt,"searchParams",Bt("getSearchParams")),f(zt,"hash",Bt("getHash","setHash"))),l(zt,"toJSON",(function(){return E(this).serialize()}),{enumerable:!0}),l(zt,"toString",(function(){return E(this).serialize()}),{enumerable:!0}),O){var Ut=O.createObjectURL,Wt=O.revokeObjectURL;Ut&&l(Dt,"createObjectURL",u(Ut,O)),Wt&&l(Dt,"revokeObjectURL",u(Wt,O))}x(Dt,"URL"),i({global:!0,constructor:!0,forced:!a,sham:!o},{URL:Dt})},5823:function(t,e,n){"use strict";var r=n(6518),i=n(4576),o=n(9565),a=n(3724),s=n(2805),u=n(4644),c=n(6346),l=n(679),f=n(6980),h=n(6699),d=n(2087),p=n(8014),v=n(7696),g=n(8229),y=n(8319),b=n(6969),m=n(9297),x=n(6955),w=n(34),k=n(757),A=n(2360),S=n(1625),E=n(2967),_=n(8480).f,T=n(3251),O=n(9213).forEach,M=n(7633),L=n(2106),C=n(4913),I=n(7347),R=n(5370),j=n(1181),P=n(3167),F=j.get,N=j.set,D=j.enforce,z=C.f,B=I.f,U=i.RangeError,W=c.ArrayBuffer,H=W.prototype,q=c.DataView,V=u.NATIVE_ARRAY_BUFFER_VIEWS,$=u.TYPED_ARRAY_TAG,G=u.TypedArray,X=u.TypedArrayPrototype,Y=u.isTypedArray,K="BYTES_PER_ELEMENT",J="Wrong length",Z=function(t,e){L(t,e,{configurable:!0,get:function(){return F(this)[e]}})},Q=function(t){var e;return S(H,t)||"ArrayBuffer"===(e=x(t))||"SharedArrayBuffer"===e},tt=function(t,e){return Y(t)&&!k(e)&&e in t&&d(+e)&&e>=0},et=function(t,e){return e=b(e),tt(t,e)?f(2,t[e]):B(t,e)},nt=function(t,e,n){return e=b(e),!(tt(t,e)&&w(n)&&m(n,"value"))||m(n,"get")||m(n,"set")||n.configurable||m(n,"writable")&&!n.writable||m(n,"enumerable")&&!n.enumerable?z(t,e,n):(t[e]=n.value,t)};a?(V||(I.f=et,C.f=nt,Z(X,"buffer"),Z(X,"byteOffset"),Z(X,"byteLength"),Z(X,"length")),r({target:"Object",stat:!0,forced:!V},{getOwnPropertyDescriptor:et,defineProperty:nt}),t.exports=function(t,e,n){var a=t.match(/\d+/)[0]/8,u=t+(n?"Clamped":"")+"Array",c="get"+t,f="set"+t,d=i[u],b=d,m=b&&b.prototype,x={},k=function(t,e){z(t,e,{get:function(){return function(t,e){var n=F(t);return n.view[c](e*a+n.byteOffset,!0)}(this,e)},set:function(t){return function(t,e,r){var i=F(t);i.view[f](e*a+i.byteOffset,n?y(r):r,!0)}(this,e,t)},enumerable:!0})};V?s&&(b=e((function(t,e,n,r){return l(t,m),P(w(e)?Q(e)?void 0!==r?new d(e,g(n,a),r):void 0!==n?new d(e,g(n,a)):new d(e):Y(e)?R(b,e):o(T,b,e):new d(v(e)),t,b)})),E&&E(b,G),O(_(d),(function(t){t in b||h(b,t,d[t])})),b.prototype=m):(b=e((function(t,e,n,r){l(t,m);var i,s,u,c=0,f=0;if(w(e)){if(!Q(e))return Y(e)?R(b,e):o(T,b,e);i=e,f=g(n,a);var h=e.byteLength;if(void 0===r){if(h%a)throw new U(J);if((s=h-f)<0)throw new U(J)}else if((s=p(r)*a)+f>h)throw new U(J);u=s/a}else u=v(e),i=new W(s=u*a);for(N(t,{buffer:i,byteOffset:f,byteLength:s,length:u,view:new q(i)});c<u;)k(t,c++)})),E&&E(b,G),m=b.prototype=A(X)),m.constructor!==b&&h(m,"constructor",b),D(m).TypedArrayConstructor=b,$&&h(m,$,u);var S=b!==d;x[u]=b,r({global:!0,constructor:!0,forced:S,sham:!V},x),K in b||h(b,K,a),K in m||h(m,K,a),M(u)}):t.exports=function(){}},5843:function(t,e,n){"use strict";var r=n(6518),i=n(2703);r({target:"Number",stat:!0,forced:Number.parseInt!==i},{parseInt:i})},5854:function(t,e,n){"use strict";var r=n(2777),i=TypeError;t.exports=function(t){var e=r(t,"number");if("number"==typeof e)throw new i("Can't convert number to bigint");return BigInt(e)}},5869:function(t,e,n){var r=n(887);t.exports=function(t,e,n,i,o){var a=r(t,e,n,i,o);return a.next().then((function(t){return t.done?t.value:a.next()}))},t.exports.__esModule=!0,t.exports.default=t.exports},5906:function(t,e,n){"use strict";var r=n(6518),i=n(9504),o=n(5397),a=n(8981),s=n(655),u=n(6198),c=i([].push),l=i([].join);r({target:"String",stat:!0},{raw:function(t){var e=o(a(t).raw),n=u(e);if(!n)return"";for(var r=arguments.length,i=[],f=0;;){if(c(i,s(e[f++])),f===n)return l(i,"");f<r&&c(i,s(arguments[f]))}}})},5914:function(t,e,n){"use strict";n(6518)({target:"Math",stat:!0},{sign:n(7782)})},5917:function(t,e,n){"use strict";var r=n(3724),i=n(9039),o=n(4055);t.exports=!r&&!i((function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},5966:function(t,e,n){"use strict";var r=n(9306),i=n(4117);t.exports=function(t,e){var n=t[e];return i(n)?void 0:r(n)}},6033:function(t,e,n){"use strict";n(8523)},6034:function(t,e,n){"use strict";var r=n(6518),i=n(2357).values;r({target:"Object",stat:!0},{values:function(t){return i(t)}})},6043:function(t,e,n){"use strict";var r=n(9306),i=TypeError,o=function(t){var e,n;this.promise=new t((function(t,r){if(void 0!==e||void 0!==n)throw new i("Bad Promise constructor");e=t,n=r})),this.resolve=r(e),this.reject=r(n)};t.exports.f=function(t){return new o(t)}},6072:function(t,e,n){"use strict";var r=n(4644),i=n(926).right,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("reduceRight",(function(t){var e=arguments.length;return i(o(this),t,e,e>1?arguments[1]:void 0)}))},6080:function(t,e,n){"use strict";var r=n(7476),i=n(9306),o=n(616),a=r(r.bind);t.exports=function(t,e){return i(t),void 0===e?t:o?a(t,e):function(){return t.apply(e,arguments)}}},6098:function(t,e,n){"use strict";var r=n(9504),i=2147483647,o=/[^\0-\u007E]/,a=/[.\u3002\uFF0E\uFF61]/g,s="Overflow: input needs wider integers to process",u=RangeError,c=r(a.exec),l=Math.floor,f=String.fromCharCode,h=r("".charCodeAt),d=r([].join),p=r([].push),v=r("".replace),g=r("".split),y=r("".toLowerCase),b=function(t){return t+22+75*(t<26)},m=function(t,e,n){var r=0;for(t=n?l(t/700):t>>1,t+=l(t/e);t>455;)t=l(t/35),r+=36;return l(r+36*t/(t+38))},x=function(t){var e=[];t=function(t){for(var e=[],n=0,r=t.length;n<r;){var i=h(t,n++);if(i>=55296&&i<=56319&&n<r){var o=h(t,n++);56320==(64512&o)?p(e,((1023&i)<<10)+(1023&o)+65536):(p(e,i),n--)}else p(e,i)}return e}(t);var n,r,o=t.length,a=128,c=0,v=72;for(n=0;n<t.length;n++)(r=t[n])<128&&p(e,f(r));var g=e.length,y=g;for(g&&p(e,"-");y<o;){var x=i;for(n=0;n<t.length;n++)(r=t[n])>=a&&r<x&&(x=r);var w=y+1;if(x-a>l((i-c)/w))throw new u(s);for(c+=(x-a)*w,a=x,n=0;n<t.length;n++){if((r=t[n])<a&&++c>i)throw new u(s);if(r===a){for(var k=c,A=36;;){var S=A<=v?1:A>=v+26?26:A-v;if(k<S)break;var E=k-S,_=36-S;p(e,f(b(S+E%_))),k=l(E/_),A+=36}p(e,f(b(k))),v=m(c,w,y===g),c=0,y++}}c++,a++}return d(e,"")};t.exports=function(t){var e,n,r=[],i=g(v(y(t),a,"."),".");for(e=0;e<i.length;e++)n=i[e],p(r,c(o,n)?"xn--"+x(n):n);return d(r,".")}},6099:function(t,e,n){"use strict";var r=n(2140),i=n(6840),o=n(3179);r||i(Object.prototype,"toString",o,{unsafe:!0})},6119:function(t,e,n){"use strict";var r=n(5745),i=n(3392),o=r("keys");t.exports=function(t){return o[t]||(o[t]=i(t))}},6193:function(t,e,n){"use strict";var r=n(4215);t.exports="NODE"===r},6198:function(t,e,n){"use strict";var r=n(8014);t.exports=function(t){return r(t.length)}},6218:function(t,e,n){"use strict";n.d(e,{A:function(){return i}});var r=n(7106);function i(t){return new r.A(t,0)}},6269:function(t){"use strict";t.exports={}},6279:function(t,e,n){"use strict";var r=n(6840);t.exports=function(t,e,n){for(var i in e)r(t,i,e[i],n);return t}},6319:function(t,e,n){"use strict";var r=n(8551),i=n(9539);t.exports=function(t,e,n,o){try{return o?e(r(n)[0],n[1]):e(n)}catch(e){i(t,"throw",e)}}},6346:function(t,e,n){"use strict";var r=n(4576),i=n(9504),o=n(3724),a=n(7811),s=n(350),u=n(6699),c=n(2106),l=n(6279),f=n(9039),h=n(679),d=n(1291),p=n(8014),v=n(7696),g=n(5617),y=n(8490),b=n(2787),m=n(2967),x=n(6754),w=n(7680),k=n(3167),A=n(7740),S=n(687),E=n(1181),_=s.PROPER,T=s.CONFIGURABLE,O="ArrayBuffer",M="DataView",L="prototype",C="Wrong index",I=E.getterFor(O),R=E.getterFor(M),j=E.set,P=r[O],F=P,N=F&&F[L],D=r[M],z=D&&D[L],B=Object.prototype,U=r.Array,W=r.RangeError,H=i(x),q=i([].reverse),V=y.pack,$=y.unpack,G=function(t){return[255&t]},X=function(t){return[255&t,t>>8&255]},Y=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},K=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},J=function(t){return V(g(t),23,4)},Z=function(t){return V(t,52,8)},Q=function(t,e,n){c(t[L],e,{configurable:!0,get:function(){return n(this)[e]}})},tt=function(t,e,n,r){var i=R(t),o=v(n),a=!!r;if(o+e>i.byteLength)throw new W(C);var s=i.bytes,u=o+i.byteOffset,c=w(s,u,u+e);return a?c:q(c)},et=function(t,e,n,r,i,o){var a=R(t),s=v(n),u=r(+i),c=!!o;if(s+e>a.byteLength)throw new W(C);for(var l=a.bytes,f=s+a.byteOffset,h=0;h<e;h++)l[f+h]=u[c?h:e-h-1]};if(a){var nt=_&&P.name!==O;f((function(){P(1)}))&&f((function(){new P(-1)}))&&!f((function(){return new P,new P(1.5),new P(NaN),1!==P.length||nt&&!T}))?nt&&T&&u(P,"name",O):((F=function(t){return h(this,N),k(new P(v(t)),this,F)})[L]=N,N.constructor=F,A(F,P)),m&&b(z)!==B&&m(z,B);var rt=new D(new F(2)),it=i(z.setInt8);rt.setInt8(0,2147483648),rt.setInt8(1,2147483649),!rt.getInt8(0)&&rt.getInt8(1)||l(z,{setInt8:function(t,e){it(this,t,e<<24>>24)},setUint8:function(t,e){it(this,t,e<<24>>24)}},{unsafe:!0})}else N=(F=function(t){h(this,N);var e=v(t);j(this,{type:O,bytes:H(U(e),0),byteLength:e}),o||(this.byteLength=e,this.detached=!1)})[L],z=(D=function(t,e,n){h(this,z),h(t,N);var r=I(t),i=r.byteLength,a=d(e);if(a<0||a>i)throw new W("Wrong offset");if(a+(n=void 0===n?i-a:p(n))>i)throw new W("Wrong length");j(this,{type:M,buffer:t,byteLength:n,byteOffset:a,bytes:r.bytes}),o||(this.buffer=t,this.byteLength=n,this.byteOffset=a)})[L],o&&(Q(F,"byteLength",I),Q(D,"buffer",R),Q(D,"byteLength",R),Q(D,"byteOffset",R)),l(z,{getInt8:function(t){return tt(this,1,t)[0]<<24>>24},getUint8:function(t){return tt(this,1,t)[0]},getInt16:function(t){var e=tt(this,2,t,arguments.length>1&&arguments[1]);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=tt(this,2,t,arguments.length>1&&arguments[1]);return e[1]<<8|e[0]},getInt32:function(t){return K(tt(this,4,t,arguments.length>1&&arguments[1]))},getUint32:function(t){return K(tt(this,4,t,arguments.length>1&&arguments[1]))>>>0},getFloat32:function(t){return $(tt(this,4,t,arguments.length>1&&arguments[1]),23)},getFloat64:function(t){return $(tt(this,8,t,arguments.length>1&&arguments[1]),52)},setInt8:function(t,e){et(this,1,t,G,e)},setUint8:function(t,e){et(this,1,t,G,e)},setInt16:function(t,e){et(this,2,t,X,e,arguments.length>2&&arguments[2])},setUint16:function(t,e){et(this,2,t,X,e,arguments.length>2&&arguments[2])},setInt32:function(t,e){et(this,4,t,Y,e,arguments.length>2&&arguments[2])},setUint32:function(t,e){et(this,4,t,Y,e,arguments.length>2&&arguments[2])},setFloat32:function(t,e){et(this,4,t,J,e,arguments.length>2&&arguments[2])},setFloat64:function(t,e){et(this,8,t,Z,e,arguments.length>2&&arguments[2])}});S(F,O),S(D,M),t.exports={ArrayBuffer:F,DataView:D}},6368:function(t,e,n){"use strict";var r=n(6518),i=n(4576),o=n(9225).clear;r({global:!0,bind:!0,enumerable:!0,forced:i.clearImmediate!==o},{clearImmediate:o})},6389:function(t,e,n){"use strict";var r=n(6518),i=Math.atanh,o=Math.log;r({target:"Math",stat:!0,forced:!(i&&1/i(-0)<0)},{atanh:function(t){var e=+t;return 0===e?e:o((1+e)/(1-e))/2}})},6395:function(t){"use strict";t.exports=!1},6412:function(t,e,n){"use strict";n(511)("asyncIterator")},6449:function(t,e,n){"use strict";var r=n(6518),i=n(259),o=n(8981),a=n(6198),s=n(1291),u=n(1469);r({target:"Array",proto:!0},{flat:function(){var t=arguments.length?arguments[0]:void 0,e=o(this),n=a(e),r=u(e,0);return r.length=i(r,e,e,n,0,void 0===t?1:s(t)),r}})},6468:function(t,e,n){"use strict";var r=n(6518),i=n(4576),o=n(9504),a=n(2796),s=n(6840),u=n(3451),c=n(2652),l=n(679),f=n(4901),h=n(4117),d=n(34),p=n(9039),v=n(4428),g=n(687),y=n(3167);t.exports=function(t,e,n){var b=-1!==t.indexOf("Map"),m=-1!==t.indexOf("Weak"),x=b?"set":"add",w=i[t],k=w&&w.prototype,A=w,S={},E=function(t){var e=o(k[t]);s(k,t,"add"===t?function(t){return e(this,0===t?0:t),this}:"delete"===t?function(t){return!(m&&!d(t))&&e(this,0===t?0:t)}:"get"===t?function(t){return m&&!d(t)?void 0:e(this,0===t?0:t)}:"has"===t?function(t){return!(m&&!d(t))&&e(this,0===t?0:t)}:function(t,n){return e(this,0===t?0:t,n),this})};if(a(t,!f(w)||!(m||k.forEach&&!p((function(){(new w).entries().next()})))))A=n.getConstructor(e,t,b,x),u.enable();else if(a(t,!0)){var _=new A,T=_[x](m?{}:-0,1)!==_,O=p((function(){_.has(1)})),M=v((function(t){new w(t)})),L=!m&&p((function(){for(var t=new w,e=5;e--;)t[x](e,e);return!t.has(-0)}));M||((A=e((function(t,e){l(t,k);var n=y(new w,t,A);return h(e)||c(e,n[x],{that:n,AS_ENTRIES:b}),n}))).prototype=k,k.constructor=A),(O||L)&&(E("delete"),E("has"),b&&E("get")),(L||T)&&E(x),m&&k.clear&&delete k.clear}return S[t]=A,r({global:!0,constructor:!0,forced:A!==w},S),g(A,t),m||n.setStrong(A,t,b),A}},6469:function(t,e,n){"use strict";var r=n(8227),i=n(2360),o=n(4913).f,a=r("unscopables"),s=Array.prototype;void 0===s[a]&&o(s,a,{configurable:!0,value:i(null)}),t.exports=function(t){s[a][t]=!0}},6499:function(t,e,n){"use strict";var r=n(6518),i=n(9565),o=n(9306),a=n(6043),s=n(1103),u=n(2652);r({target:"Promise",stat:!0,forced:n(537)},{all:function(t){var e=this,n=a.f(e),r=n.resolve,c=n.reject,l=s((function(){var n=o(e.resolve),a=[],s=0,l=1;u(t,(function(t){var o=s++,u=!1;l++,i(n,e,t).then((function(t){u||(u=!0,a[o]=t,--l||r(a))}),c)})),--l||r(a)}));return l.error&&c(l.value),n.promise}})},6518:function(t,e,n){"use strict";var r=n(4576),i=n(7347).f,o=n(6699),a=n(6840),s=n(9433),u=n(7740),c=n(2796);t.exports=function(t,e){var n,l,f,h,d,p=t.target,v=t.global,g=t.stat;if(n=v?r:g?r[p]||s(p,{}):r[p]&&r[p].prototype)for(l in e){if(h=e[l],f=t.dontCallGetSet?(d=i(n,l))&&d.value:n[l],!c(v?l:p+(g?".":"#")+l,t.forced)&&void 0!==f){if(typeof h==typeof f)continue;u(h,f)}(t.sham||f&&f.sham)&&o(h,"sham",!0),a(n,l,h,t)}}},6561:function(t,e,n){"use strict";n.a(t,(async function(t,r){try{n.r(e);var i=n(3029),o=n(2901),a=n(4467),s=n(467),u=n(296),c=n(4756),l=n.n(c),f=(n(2675),n(9463),n(6412),n(193),n(2168),n(2259),n(6964),n(3237),n(1833),n(7947),n(1073),n(5700),n(8125),n(326),n(8706),n(6835),n(3771),n(2008),n(113),n(8980),n(6449),n(8350),n(3418),n(4423),n(5276),n(3792),n(8921),n(2062),n(1051),n(2712),n(8863),n(4490),n(4782),n(6910),n(7478),n(4554),n(3514),n(237),n(4743),n(9142),n(1745),n(739),n(9572),n(8957),n(4731),n(6033),n(3153),n(2326),n(6389),n(4444),n(8085),n(7762),n(5070),n(605),n(9469),n(5376),n(6624),n(1367),n(5914),n(8553),n(8690),n(479),n(761),n(2892),n(5374),n(2637),n(9149),n(4601),n(4435),n(7220),n(5843),n(9085),n(7427),n(7945),n(4185),n(7607),n(5506),n(2811),n(3921),n(3851),n(1278),n(1480),n(875),n(4052),n(4003),n(221),n(9432),n(9220),n(7904),n(3967),n(3941),n(287),n(6099),n(6034),n(8459),n(8940),n(3362),n(9391),n(9796),n(825),n(7411),n(1211),n(888),n(9065),n(6565),n(431),n(4634),n(1137),n(985),n(4268),n(4873),n(4864),n(7495),n(9479),n(8781),n(1415),n(3860),n(9449),n(7337),n(1699),n(7764),n(1761),n(5701),n(8156),n(5906),n(2781),n(5440),n(5746),n(744),n(1392),n(2762),n(9202),n(3359),n(4594),n(9833),n(6594),n(2107),n(5477),n(1489),n(2134),n(3690),n(1740),n(1630),n(2170),n(5044),n(1920),n(1694),n(9955),n(3206),n(8345),n(4496),n(6651),n(2887),n(9369),n(6812),n(8995),n(2568),n(1575),n(6072),n(8747),n(8845),n(9423),n(7301),n(373),n(1405),n(3684),n(3772),n(958),n(3500),n(2953),n(9848),n(122),n(3296),n(7208),n(8408),n(5457)),h=n(2407),d=n(9557),p=n(2248),v=t([f]);function bt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function mt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?bt(Object(n),!0).forEach((function(e){(0,a.A)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):bt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function xt(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=wt(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function wt(t,e){if(t){if("string"==typeof t)return kt(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?kt(t,e):void 0}}function kt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function At(t,e,n){St(t,e),e.set(t,n)}function St(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Et(t,e,n){return t.set(Tt(t,e),n),n}function _t(t,e){return t.get(Tt(t,e))}function Tt(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}function Ot(t){var e,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(e=t[n]))return e.call(t);if(r&&null!=(e=t[r]))return new Mt(e.call(t));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Mt(t){function e(t){if(Object(t)!==t)return Promise.reject(new TypeError(t+" is not an object."));var e=t.done;return Promise.resolve(t.value).then((function(t){return{value:t,done:e}}))}return Mt=function(t){this.s=t,this.n=t.next},Mt.prototype={s:null,n:null,next:function(){return e(this.n.apply(this.s,arguments))},return:function(t){var n=this.s.return;return void 0===n?Promise.resolve({value:t,done:!0}):e(n.apply(this.s,arguments))},throw:function(t){var n=this.s.return;return void 0===n?Promise.reject(t):e(n.apply(this.s,arguments))}},new Mt(t)}f=(v.then?(await v)():v)[0],console.log("book.js"),console.log("AnxUA",navigator.userAgent);var g=await Promise.resolve().then(n.bind(n,3234)),y=g.configure,b=g.ZipReader,m=g.BlobReader,x=g.TextWriter,w=g.BlobWriter,k=(await Promise.resolve().then(n.bind(n,1330))).EPUB,A=!1,S=function(t){var e,n,r,i,o,a,s=function(t){var e=t.x,n=t.y;return e>=0&&n>=0&&e<=window.innerWidth&&n<=window.innerHeight},c=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;return{left:n*e.left+t.left,right:n*e.right+t.left,top:r*e.top+t.top,bottom:r*e.bottom+t.top}},l=null!==(e=null===(n=t.getRootNode)||void 0===n?void 0:n.call(t))&&void 0!==e?e:null==t||null===(r=t.endContainer)||void 0===r||null===(i=r.getRootNode)||void 0===i?void 0:i.call(r),f=null==l||null===(o=l.defaultView)||void 0===o?void 0:o.frameElement,h=1,d=1;if(f){var p=getComputedStyle(f).transform.match(/matrix\((.+)\)/);if(p){var v=p[1].split(/\s*,\s*/).map(Number),g=(0,u.A)(v,4);h=g[0],d=g[3]}}var y=null!==(a=null==f?void 0:f.getBoundingClientRect())&&void 0!==a?a:{top:0,left:0},b=Array.from(t.getClientRects()),m=c(y,b[0],h,d),x=c(y,b[b.length-1],h,d),w=window.innerWidth,k=window.innerHeight,A={point:{x:(m.left+m.right)/2/w,y:m.top/k},dir:"up"},S={point:{x:(x.left+x.right)/2/w,y:x.bottom/k},dir:"down"},E=s(A.point),_=s(S.point);return E||_?E?_?A.point.y*k>k-S.point.y*k?A:S:A:S:{point:{x:0,y:0}}},E=function(t){if(null==t||!t.rangeCount)return null;var e=t.getRangeAt(0);return e.collapsed?null:e},_=function(t,e,n){var r=e.getSelection(),i=E(r);if(i){var o=S(i),a=t.getCFI(n,i),s=r.toString();if(!s){var u=i.startContainer.ownerDocument.getSelection();u.removeAllRanges(),u.addRange(i),s=u.toString()}ft({index:n,range:i,lang:"en-US",cfi:a,pos:o,text:s})}},T=function(t,e,n){navigator.platform.includes("Win")||navigator.platform.includes("Mac")||navigator.platform.includes("iPhone")||navigator.platform.includes("iPad")?e.addEventListener("pointerup",(function(){return _(t,e,n)})):e.addEventListener("contextmenu",(function(r){_(t,e,n)})),t.isFixedLayout||(e.addEventListener("selectstart",(function(){var e=t.shadowRoot.querySelector("foliate-paginator").shadowRoot.querySelector("#container");e&&(globalThis.originalScrollLeft=e.scrollLeft)})),e.addEventListener("selectionchange",(function(){if("paginated"===t.renderer.getAttribute("flow")){var n=t.lastLocation;if(n){var r=E(e.getSelection());if(r){globalThis.pageDebounceTimer&&(clearTimeout(globalThis.pageDebounceTimer),globalThis.pageDebounceTimer=null);var i=t.shadowRoot.querySelector("foliate-paginator").shadowRoot.querySelector("#container");if(r.compareBoundaryPoints(Range.END_TO_END,n.range)>=0)globalThis.pageDebounceTimer=setTimeout((0,s.A)(l().mark((function e(){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=1,t.next();case 1:globalThis.originalScrollLeft=i.scrollLeft,globalThis.pageDebounceTimer=null;case 2:case"end":return e.stop()}}),e)}))),1e3);else{var o=function(){var n=E(e.getSelection());n&&t.lastLocation&&t.lastLocation.range&&t.lastLocation.range.startContainer===n.endContainer&&(i.scrollLeft=globalThis.originalScrollLeft)};i.addEventListener("scroll",o),e.addEventListener("pointerup",(function(){i.removeEventListener("scroll",o)}),{once:!0})}}}}})))},O=function(){var t=(0,s.A)(l().mark((function t(e){var n,r,i;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=Uint8Array,t.next=1,e.slice(0,4).arrayBuffer();case 1:return i=t.sent,n=new r(i),t.abrupt("return",80===n[0]&&75===n[1]&&3===n[2]&&4===n[3]);case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),M=function(){var t=(0,s.A)(l().mark((function t(e){var n,r,i;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=Uint8Array,t.next=1,e.slice(0,5).arrayBuffer();case 1:return i=t.sent,n=new r(i),t.abrupt("return",37===n[0]&&80===n[1]&&68===n[2]&&70===n[3]&&45===n[4]);case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),L=function(){var t=(0,s.A)(l().mark((function t(e){var n,r,i,o,a,s,u;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return y({useWebWorkers:!1}),n=new b(new m(e)),t.next=1,n.getEntries();case 1:return r=t.sent,i=new Map(r.map((function(t){return[t.filename,t]}))),o=function(t){return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return i.has(e)?t.apply(void 0,[i.get(e)].concat(r)):null}},a=o((function(t){return t.getData(new x)})),s=o((function(t,e){return t.getData(new w(e))})),u=function(t){var e,n;return null!==(e=null===(n=i.get(t))||void 0===n?void 0:n.uncompressedSize)&&void 0!==e?e:0},t.abrupt("return",{entries:r,loadText:a,loadBlob:s,getSize:u});case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),C=function(){var t=(0,s.A)(l().mark((function t(e){var n,r,i,o,a,s;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.isFile){t.next=1;break}n=e,t.next=4;break;case 1:return r=Promise,i=Array,t.next=2,new Promise((function(t,n){return e.createReader().readEntries((function(e){return t(e)}),(function(t){return n(t)}))}));case 2:return o=t.sent,a=C,s=i.from.call(i,o,a),t.next=3,r.all.call(r,s);case 3:n=t.sent.flat();case 4:return t.abrupt("return",n);case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),I=function(){var t=(0,s.A)(l().mark((function t(e){var n,r,i,o,a,c,f,h,d;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,C(e);case 1:return n=t.sent,t.next=2,Promise.all(n.map((function(t){return new Promise((function(e,n){return t.file((function(n){return e([n,t.fullPath])}),(function(t){return n(t)}))}))})));case 2:return r=t.sent,i=new Map(r.map((function(t){var n=(0,u.A)(t,2),r=n[0];return[n[1].replace(e.fullPath+"/",""),r]}))),o=new TextDecoder,a=function(t){return t?o.decode(t):null},c=function(t){var e,n;return null!==(e=null===(n=i.get(t))||void 0===n?void 0:n.arrayBuffer())&&void 0!==e?e:null},f=function(){var t=(0,s.A)(l().mark((function t(e){var n,r;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=a,t.next=1,c(e);case 1:return r=t.sent,t.abrupt("return",n(r));case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),h=function(t){return i.get(t)},d=function(t){var e,n;return null!==(e=null===(n=i.get(t))||void 0===n?void 0:n.size)&&void 0!==e?e:0},t.abrupt("return",{loadText:f,loadBlob:h,getSize:d});case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),R=function(t){var e=t.name;return"application/x-fictionbook+xml"===t.type||e.endsWith(".fb2")},j=function(t){var e=t.name;return"application/x-zip-compressed-fb2"===t.type||e.endsWith(".fb2.zip")||e.endsWith(".fbz")},P=function(){var t=(0,s.A)(l().mark((function t(e){var r,i,o,a,s,u,c,f,h,d,p,v,g,y,b,m,x,w,S,E,_;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.isDirectory){t.next=4;break}return t.next=1,I(e);case 1:return i=t.sent,t.next=2,Promise.resolve().then(n.bind(n,1330));case 2:return o=t.sent,a=o.EPUB,t.next=3,new a(i).init();case 3:r=t.sent,t.next=28;break;case 4:if(e.size){t.next=5;break}throw new Error("File not found");case 5:return t.next=6,O(e);case 6:if(!t.sent){t.next=16;break}return t.next=7,L(e);case 7:if(s=t.sent,T=void 0,T=(l=e).name,"application/vnd.comicbook+zip"!==l.type&&!T.endsWith(".cbz")){t.next=9;break}return t.next=8,Promise.resolve().then(n.bind(n,6973));case 8:u=t.sent,c=u.makeComicBook,r=c(s,e),t.next=15;break;case 9:if(!j(e)){t.next=13;break}return t.next=10,Promise.resolve().then(n.bind(n,740));case 10:return f=t.sent,h=f.makeFB2,d=s.entries,p=d.find((function(t){return t.filename.endsWith(".fb2")})),t.next=11,s.loadBlob((null!=p?p:d[0]).filename);case 11:return v=t.sent,t.next=12,h(v);case 12:r=t.sent,t.next=15;break;case 13:return t.next=14,new k(s).init();case 14:r=t.sent;case 15:t.next=28;break;case 16:return t.next=17,M(e);case 17:if(!t.sent){t.next=20;break}return A=!0,t.next=18,Promise.resolve().then(n.bind(n,6648));case 18:return g=t.sent,y=g.makePDF,t.next=19,y(e);case 19:r=t.sent,t.next=28;break;case 20:return t.next=21,Promise.resolve().then(n.bind(n,4885));case 21:return b=t.sent,m=b.isMOBI,x=b.MOBI,t.next=22,m(e);case 22:if(!t.sent){t.next=25;break}return t.next=23,Promise.resolve().then(n.bind(n,2725));case 23:return w=t.sent,t.next=24,new x({unzlib:w.unzlibSync}).open(e);case 24:r=t.sent,t.next=28;break;case 25:if(!R(e)){t.next=28;break}return t.next=26,Promise.resolve().then(n.bind(n,740));case 26:return S=t.sent,E=S.makeFB2,t.next=27,E(e);case 27:r=t.sent;case 28:if(r){t.next=29;break}throw new Error("File type not supported");case 29:return _=document.createElement("foliate-view"),document.body.append(_),t.next=30,_.open(r);case 30:return t.abrupt("return",_);case 31:case"end":return t.stop()}var l,T}),t)})));return function(e){return t.apply(this,arguments)}}(),F=function(t){var e=t.fontSize,n=t.fontName,r=t.fontPath,i=t.fontWeight,o=t.letterSpacing,a=t.spacing,s=t.textIndent,u=t.paragraphSpacing,c=t.fontColor,l=(t.backgroundColor,t.justify),f=t.hyphenate,h=t.writingMode,d=t.backgroundImage,p=t.flow,v="book"===n?"":"system"===n?"font-family: system-ui !important;":"font-family: ".concat(n," !important;"),g="auto"===h?"":"writing-mode: ".concat(h," !important;"),y=!d||p||"none"===d?"background: none !important;":"background-image: url('".concat(d,"') !important;\n    background-size: 100% 100% !important;\n    background-repeat: repeat !important;\n    background-attachment: scroll !important; \n    background-position: center center !important;\n    background-clip: content-box !important;");return'\n    @namespace epub "http://www.idpf.org/2007/ops";\n    @font-face {\n      font-family: '.concat(n,";\n      src: url('").concat(r,"');\n      font-display: swap;\n    }\n\n    html {\n        ").concat(g,"\n        color: ").concat(c," !important;\n        ").concat(y,"\n        background-color: transparent !important;\n        letter-spacing: ").concat(o,"px;\n        font-size: ").concat(e,"em;\n    }\n\n    body {\n        background: none !important;\n        background-color: transparent;\n    }\n\n    img {\n        max-width: 100% !important;\n        object-fit: contain !important;\n        break-inside: avoid !important;\n        box-sizing: border-box !important;\n    }\n\n    a:link {\n        color:rgb(167, 96, 52) !important;\n    }\n\n    * {\n        line-height: ").concat(a,"em !important;\n        ").concat(v,"\n    }\n\n    p, li, blockquote, dd, div, font {\n        color: ").concat(c," !important;\n        // line-height: ").concat(a," !important;\n        font-weight: ").concat(i," !important;\n        padding-bottom: ").concat(u,"em !important;\n        text-align: ").concat(l?"justify":"start",";\n        -webkit-hyphens: ").concat(f?"auto":"manual",";\n        hyphens: ").concat(f?"auto":"manual",";\n        -webkit-hyphenate-limit-before: 3;\n        -webkit-hyphenate-limit-after: 2;\n        -webkit-hyphenate-limit-lines: 2;\n        hanging-punctuation: allow-end last;\n        margin-top: 0 !important;\n        margin-bottom: 0 !important;\n        widows: 2;\n    }\n\n    p, li, blockquote, dd, font {\n        text-indent: ").concat(s,"em !important;\n    }\n    \n    p img {\n      margin-left: -").concat(s,'em;\n    }\n        \n    /* prevent the above from overriding the align attribute */\n    [align="left"] { text-align: left; }\n    [align="right"] { text-align: right; }\n    [align="center"] { text-align: center; }\n    [align="justify"] { text-align: justify; }\n\n    pre {\n        white-space: pre-wrap !important;\n    }\n    aside[epub|type~="endnote"],\n    aside[epub|type~="footnote"],\n    aside[epub|type~="note"],\n    aside[epub|type~="rearnote"] {\n        display: none;\n    }\n')},N=function(t){"none"!==yt.convertChineseMode&&function(t,e){console.log("convertChinese",t);var n="皑蔼碍爱翱袄奥坝罢摆败颁办绊帮绑镑谤剥饱宝报鲍辈贝钡狈备惫绷笔毕毙闭边编贬变辩辫鳖瘪濒滨宾摈饼拨钵铂驳卜补参蚕残惭惨灿苍舱仓沧厕侧册测层诧搀掺蝉馋谗缠铲产阐颤场尝长偿肠厂畅钞车彻尘陈衬撑称惩诚骋痴迟驰耻齿炽冲虫宠畴踌筹绸丑橱厨锄雏础储触处传疮闯创锤纯绰辞词赐聪葱囱从丛凑窜错达带贷担单郸掸胆惮诞弹当挡党荡档捣岛祷导盗灯邓敌涤递缔点垫电淀钓调迭谍叠钉顶锭订东动栋冻斗犊独读赌镀锻断缎兑队对吨顿钝夺鹅额讹恶饿儿尔饵贰发罚阀珐矾钒烦范贩饭访纺飞废费纷坟奋愤粪丰枫锋风疯冯缝讽凤肤辐抚辅赋复负讣妇缚该钙盖干赶秆赣冈刚钢纲岗皋镐搁鸽阁铬个给龚宫巩贡钩沟构购够蛊顾剐关观馆惯贯广规硅归龟闺轨诡柜贵刽辊滚锅国过骇韩汉阂鹤贺横轰鸿红后壶护沪户哗华画划话怀坏欢环还缓换唤痪焕涣黄谎挥辉毁贿秽会烩汇讳诲绘荤浑伙获货祸击机积饥讥鸡绩缉极辑级挤几蓟剂济计记际继纪夹荚颊贾钾价驾歼监坚笺间艰缄茧检碱硷拣捡简俭减荐槛鉴践贱见键舰剑饯渐溅涧浆蒋桨奖讲酱胶浇骄娇搅铰矫侥脚饺缴绞轿较秸阶节茎惊经颈静镜径痉竞净纠厩旧驹举据锯惧剧鹃绢杰洁结诫届紧锦仅谨进晋烬尽劲荆觉决诀绝钧军骏开凯颗壳课垦恳抠库裤夸块侩宽矿旷况亏岿窥馈溃扩阔蜡腊莱来赖蓝栏拦篮阑兰澜谰揽览懒缆烂滥捞劳涝乐镭垒类泪篱离里鲤礼丽厉励砾历沥隶俩联莲连镰怜涟帘敛脸链恋炼练粮凉两辆谅疗辽镣猎临邻鳞凛赁龄铃凌灵岭领馏刘龙聋咙笼垄拢陇楼娄搂篓芦卢颅庐炉掳卤虏鲁赂禄录陆驴吕铝侣屡缕虑滤绿峦挛孪滦乱抡轮伦仑沦纶论萝罗逻锣箩骡骆络妈玛码蚂马骂吗买麦卖迈脉瞒馒蛮满谩猫锚铆贸么霉没镁门闷们锰梦谜弥觅绵缅庙灭悯闽鸣铭谬谋亩钠纳难挠脑恼闹馁腻撵捻酿鸟聂啮镊镍柠狞宁拧泞钮纽脓浓农疟诺欧鸥殴呕沤盘庞国爱赔喷鹏骗飘频贫苹凭评泼颇扑铺朴谱脐齐骑岂启气弃讫牵扦钎铅迁签谦钱钳潜浅谴堑枪呛墙蔷强抢锹桥乔侨翘窍窃钦亲轻氢倾顷请庆琼穷趋区躯驱龋颧权劝却鹊让饶扰绕热韧认纫荣绒软锐闰润洒萨鳃赛伞丧骚扫涩杀纱筛晒闪陕赡缮伤赏烧绍赊摄慑设绅审婶肾渗声绳胜圣师狮湿诗尸时蚀实识驶势释饰视试寿兽枢输书赎属术树竖数帅双谁税顺说硕烁丝饲耸怂颂讼诵擞苏诉肃虽绥岁孙损笋缩琐锁獭挞抬摊贪瘫滩坛谭谈叹汤烫涛绦腾誊锑题体屉条贴铁厅听烃铜统头图涂团颓蜕脱鸵驮驼椭洼袜弯湾顽万网韦违围为潍维苇伟伪纬谓卫温闻纹稳问瓮挝蜗涡窝呜钨乌诬无芜吴坞雾务误锡牺袭习铣戏细虾辖峡侠狭厦锨鲜纤咸贤衔闲显险现献县馅羡宪线厢镶乡详响项萧销晓啸蝎协挟携胁谐写泻谢锌衅兴汹锈绣虚嘘须许绪续轩悬选癣绚学勋询寻驯训讯逊压鸦鸭哑亚讶阉烟盐严颜阎艳厌砚彦谚验鸯杨扬疡阳痒养样瑶摇尧遥窑谣药爷页业叶医铱颐遗仪彝蚁艺亿忆义诣议谊译异绎荫阴银饮樱婴鹰应缨莹萤营荧蝇颖哟拥佣痈踊咏涌优忧邮铀犹游诱舆鱼渔娱与屿语吁御狱誉预驭鸳渊辕园员圆缘远愿约跃钥岳粤悦阅云郧匀陨运蕴酝晕韵杂灾载攒暂赞赃脏凿枣灶责择则泽贼赠扎札轧铡闸诈斋债毡盏斩辗崭栈战绽张涨帐账胀赵蛰辙锗这贞针侦诊镇阵挣睁狰帧郑证织职执纸挚掷帜质钟终种肿众诌轴皱昼骤猪诸诛烛瞩嘱贮铸筑驻专砖转赚桩庄装妆壮状锥赘坠缀谆浊兹资渍踪综总纵邹诅组钻致钟么为只凶准启板里雳余链泄",r="皚藹礙愛翺襖奧壩罷擺敗頒辦絆幫綁鎊謗剝飽寶報鮑輩貝鋇狽備憊繃筆畢斃閉邊編貶變辯辮鼈癟瀕濱賓擯餅撥缽鉑駁蔔補參蠶殘慚慘燦蒼艙倉滄廁側冊測層詫攙摻蟬饞讒纏鏟産闡顫場嘗長償腸廠暢鈔車徹塵陳襯撐稱懲誠騁癡遲馳恥齒熾沖蟲寵疇躊籌綢醜櫥廚鋤雛礎儲觸處傳瘡闖創錘純綽辭詞賜聰蔥囪從叢湊竄錯達帶貸擔單鄲撣膽憚誕彈當擋黨蕩檔搗島禱導盜燈鄧敵滌遞締點墊電澱釣調叠諜疊釘頂錠訂東動棟凍鬥犢獨讀賭鍍鍛斷緞兌隊對噸頓鈍奪鵝額訛惡餓兒爾餌貳發罰閥琺礬釩煩範販飯訪紡飛廢費紛墳奮憤糞豐楓鋒風瘋馮縫諷鳳膚輻撫輔賦複負訃婦縛該鈣蓋幹趕稈贛岡剛鋼綱崗臯鎬擱鴿閣鉻個給龔宮鞏貢鈎溝構購夠蠱顧剮關觀館慣貫廣規矽歸龜閨軌詭櫃貴劊輥滾鍋國過駭韓漢閡鶴賀橫轟鴻紅後壺護滬戶嘩華畫劃話懷壞歡環還緩換喚瘓煥渙黃謊揮輝毀賄穢會燴彙諱誨繪葷渾夥獲貨禍擊機積饑譏雞績緝極輯級擠幾薊劑濟計記際繼紀夾莢頰賈鉀價駕殲監堅箋間艱緘繭檢堿鹼揀撿簡儉減薦檻鑒踐賤見鍵艦劍餞漸濺澗漿蔣槳獎講醬膠澆驕嬌攪鉸矯僥腳餃繳絞轎較稭階節莖驚經頸靜鏡徑痙競淨糾廄舊駒舉據鋸懼劇鵑絹傑潔結誡屆緊錦僅謹進晉燼盡勁荊覺決訣絕鈞軍駿開凱顆殼課墾懇摳庫褲誇塊儈寬礦曠況虧巋窺饋潰擴闊蠟臘萊來賴藍欄攔籃闌蘭瀾讕攬覽懶纜爛濫撈勞澇樂鐳壘類淚籬離裏鯉禮麗厲勵礫曆瀝隸倆聯蓮連鐮憐漣簾斂臉鏈戀煉練糧涼兩輛諒療遼鐐獵臨鄰鱗凜賃齡鈴淩靈嶺領餾劉龍聾嚨籠壟攏隴樓婁摟簍蘆盧顱廬爐擄鹵虜魯賂祿錄陸驢呂鋁侶屢縷慮濾綠巒攣孿灤亂掄輪倫侖淪綸論蘿羅邏鑼籮騾駱絡媽瑪碼螞馬罵嗎買麥賣邁脈瞞饅蠻滿謾貓錨鉚貿麽黴沒鎂門悶們錳夢謎彌覓綿緬廟滅憫閩鳴銘謬謀畝鈉納難撓腦惱鬧餒膩攆撚釀鳥聶齧鑷鎳檸獰甯擰濘鈕紐膿濃農瘧諾歐鷗毆嘔漚盤龐國愛賠噴鵬騙飄頻貧蘋憑評潑頗撲鋪樸譜臍齊騎豈啓氣棄訖牽扡釺鉛遷簽謙錢鉗潛淺譴塹槍嗆牆薔強搶鍬橋喬僑翹竅竊欽親輕氫傾頃請慶瓊窮趨區軀驅齲顴權勸卻鵲讓饒擾繞熱韌認紉榮絨軟銳閏潤灑薩鰓賽傘喪騷掃澀殺紗篩曬閃陝贍繕傷賞燒紹賒攝懾設紳審嬸腎滲聲繩勝聖師獅濕詩屍時蝕實識駛勢釋飾視試壽獸樞輸書贖屬術樹豎數帥雙誰稅順說碩爍絲飼聳慫頌訟誦擻蘇訴肅雖綏歲孫損筍縮瑣鎖獺撻擡攤貪癱灘壇譚談歎湯燙濤縧騰謄銻題體屜條貼鐵廳聽烴銅統頭圖塗團頹蛻脫鴕馱駝橢窪襪彎灣頑萬網韋違圍爲濰維葦偉僞緯謂衛溫聞紋穩問甕撾蝸渦窩嗚鎢烏誣無蕪吳塢霧務誤錫犧襲習銑戲細蝦轄峽俠狹廈鍁鮮纖鹹賢銜閑顯險現獻縣餡羨憲線廂鑲鄉詳響項蕭銷曉嘯蠍協挾攜脅諧寫瀉謝鋅釁興洶鏽繡虛噓須許緒續軒懸選癬絢學勳詢尋馴訓訊遜壓鴉鴨啞亞訝閹煙鹽嚴顔閻豔厭硯彥諺驗鴦楊揚瘍陽癢養樣瑤搖堯遙窯謠藥爺頁業葉醫銥頤遺儀彜蟻藝億憶義詣議誼譯異繹蔭陰銀飲櫻嬰鷹應纓瑩螢營熒蠅穎喲擁傭癰踴詠湧優憂郵鈾猶遊誘輿魚漁娛與嶼語籲禦獄譽預馭鴛淵轅園員圓緣遠願約躍鑰嶽粵悅閱雲鄖勻隕運蘊醞暈韻雜災載攢暫贊贓髒鑿棗竈責擇則澤賊贈紮劄軋鍘閘詐齋債氈盞斬輾嶄棧戰綻張漲帳賬脹趙蟄轍鍺這貞針偵診鎮陣掙睜猙幀鄭證織職執紙摯擲幟質鍾終種腫衆謅軸皺晝驟豬諸誅燭矚囑貯鑄築駐專磚轉賺樁莊裝妝壯狀錐贅墜綴諄濁茲資漬蹤綜總縱鄒詛組鑽緻鐘麼為隻兇準啟闆裡靂餘鍊洩",i="s2t"===t?n:r,o="s2t"===t?r:n,a=function(t,e,n){t.nodeType===Node.TEXT_NODE?t.textContent=t.textContent.replace(/[\u4e00-\u9fa5]/g,(function(t){var r;return null!==(r=n[e.indexOf(t)])&&void 0!==r?r:t})):t.childNodes.forEach((function(t){return a(t,e,n)}))};e.body.childNodes.forEach((function(t){a(t,i,o)}))}(yt.convertChineseMode,t),yt.bionicReadingMode},D=document.getElementById("footnote-dialog");D.style.display="none",D.addEventListener("click",(function(){D.style.display="none",rt("onFootnoteClose")})),D.addEventListener("click",(function(t){return t.target===D?D.close():null}));var z=new WeakMap,B=new WeakMap,U=new WeakMap,W=new WeakMap,H=new WeakMap,q=new WeakMap,V=new WeakMap,$=new WeakSet,G=new WeakMap,X=new WeakMap,Y=new WeakMap,K=new WeakMap,J=new WeakMap,Z=new WeakMap,Q=new WeakMap,tt=new WeakMap,et=function(){return(0,o.A)((function t(){var e,n=this;(0,i.A)(this,t),St(this,e=$),e.add(this),(0,a.A)(this,"annotations",new Map),(0,a.A)(this,"annotationsByValue",new Map),At(this,z,new h.F),At(this,B,void 0),At(this,U,void 0),At(this,W,void 0),At(this,H,!1),At(this,q,!1),At(this,V,{exists:!1,cfi:null,id:null}),At(this,G,(function(){Et(W,n,[]);for(var t=document.createTreeWalker(_t(B,n).body,NodeFilter.SHOW_TEXT,null,!1);t.nextNode();)_t(W,n).push(t.currentNode.textContent)})),At(this,X,(function(){for(var t,e=document.createTreeWalker(_t(B,n).body,NodeFilter.SHOW_TEXT,null,!1),r=0;t=e.nextNode();)t.textContent=_t(W,n)[r++]})),(0,a.A)(this,"readingFeatures",(function(){_t(X,n).call(n),N(_t(B,n))})),(0,a.A)(this,"getChapterContent",(function(){return _t(B,n).body.textContent})),(0,a.A)(this,"getPreviousContent",(function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2e3,r=null===(t=n.view.lastLocation)||void 0===t||null===(t=t.range)||void 0===t||null===(t=t.endContainer)||void 0===t?void 0:t.parentElement;if(!r)return"";for(var i="";i.length<e&&r;)i=r.textContent+i,r=r.previousSibling;return i})),(0,a.A)(this,"getSelection",(function(){var t=_t(B,n).getSelection();return E(t)})),At(this,Y,(function(){return"scrollTop"===n.view.renderer.scrollProp})),At(this,K,(function(t){t.detail,_t(Y,n).call(n)||(Et(H,n,!!document.getElementById("bookmark-icon")),Et(q,n,!1))})),At(this,J,(function(t){var e=t.detail;if(!_t(Y,n).call(n)){var r=n.view.shadowRoot.children[0];if("vertical"===e.touchState.direction){var i=e.touchState.delta.y;i>0?(r.style.transform="translateY(".concat(Math.sqrt(50*i),"px)"),_t(Q,n).call(n,i)):i<-60&&(_t(q,n)||(Et(q,n,!0),window.pullUp()))}}})),At(this,Z,(function(t){var e=t.detail;if(!_t(Y,n).call(n)){var r=n.view.shadowRoot.children[0];if("vertical"===e.touchState.direction){var i=e.touchState.delta.y;i<-60||(i>60?_t(H,n)?(_t(tt,n).call(n),n.handleBookmark(!0)):(_t(Q,n).call(n,i),n.handleBookmark(!1)):_t(tt,n).call(n)),r.style.transition="transform 0.3s ease-out",r.style.transform="translateY(0px)",setTimeout((function(){r.style.transition=""}),300)}}})),At(this,Q,(function(t){var e=document.getElementById("bookmark-icon");e||((e=document.createElement("div")).id="bookmark-icon",e.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8 24"><g data-name="Layer 2"><g data-name="bookmark"><rect width="8" height="24" opacity="0"/><path d="M2 21a1 1 0 0 1-.49-.13A1 1 0 0 1 1 20V5.33A2.28 2.28 0 0 1 3.2 3h1.6A2.28 2.28 0 0 1 7 5.33V20a1 1 0 0 1-.5.86 1 1 0 0 1-1 0L4 19.07l-1.5 1.79A1 1 0 0 1 2 21z" fill="#215a8f"/></g></g></svg>',e.style.cssText="\n        height: 80px;\n        width: 26px;\n        position: fixed;\n        top: -16px;\n        right: 20px;\n        font-size: 24px;\n        opacity: 0;\n        transition: opacity 0.2s ease;\n        z-index: 1000;\n        pointer-events: none;\n      ",document.body.appendChild(e));var n=Math.min(t/60,1);e.style.opacity=n})),At(this,tt,(function(){var t=document.getElementById("bookmark-icon");t&&(t.style.transition="opacity 0.3s ease-out",t.style.opacity="0",setTimeout((function(){t&&t.parentNode&&t.parentNode.removeChild(t)}),300))})),(0,a.A)(this,"handleBookmark",(function(t){var e,r,i=t?_t(V,n).cfi:null===(e=n.view.lastLocation)||void 0===e?void 0:e.cfi,o=null!==(r=n.view.lastLocation.range.startContainer.data)&&void 0!==r?r:n.view.lastLocation.range.startContainer.innerText;(o=o.trim()).length>200&&(o=o.slice(0,200)+"...");var a=n.view.lastLocation.fraction;rt("handleBookmark",{remove:t,detail:{cfi:i,content:o,percentage:a}})})),_t(z,this).addEventListener("before-render",(function(t){var e=t.detail.view;n.setView(e),function(t){clearSelection(),D.querySelector("main").replaceChildren(t),t.addEventListener("load",(function(e){var n=e.detail,r=n.doc,i=n.index;globalThis.footnoteSelection=function(){return _(t,r,i)},T(t,r,i),N(r),setTimeout((function(){var t=document.getElementById("footnote-dialog");document.querySelector("#footnote-dialog > main > foliate-view").shadowRoot.querySelector("foliate-paginator").shadowRoot.querySelector("#container > div > iframe"),t.style.display="block"}),0)}));var e=t.renderer;e.setAttribute("flow","scrolled"),e.setAttribute("gap","5%"),e.setAttribute("top-margin","0px"),e.setAttribute("bottom-margin","0px");var n={fontSize:gt.fontSize,fontName:gt.fontName,fontPath:gt.fontPath,letterSpacing:gt.letterSpacing,spacing:gt.spacing,textIndent:gt.textIndent,fontColor:gt.fontColor,backgroundColor:"transparent",justify:!0,hyphenate:!0};e.setStyles(F(n)),D.style.backgroundColor=gt.backgroundColor.slice(0,7)+"33"}(e)})),_t(z,this).addEventListener("render",(function(t){t.detail.view,D.showModal()})),Et(W,this,null)}),[{key:"open",value:(t=(0,s.A)(l().mark((function t(e,n){return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,P(e,n);case 1:if(this.view=t.sent,!dt){t.next=2;break}return t.abrupt("return");case 2:return this.view.addEventListener("load",Tt($,this,It).bind(this)),this.view.addEventListener("relocate",Tt($,this,Rt).bind(this)),this.view.addEventListener("click-view",Tt($,this,jt).bind(this)),this.view.addEventListener("doctouchstart",_t(K,this).bind(this)),this.view.addEventListener("doctouchmove",_t(J,this).bind(this)),this.view.addEventListener("doctouchend",_t(Z,this).bind(this)),it(),n||this.view.renderer.next(),this.setView(this.view),t.next=3,this.view.init({lastLocation:n});case 3:document.documentElement.style.backgroundColor="grey";case 4:case"end":return t.stop()}}),t,this)}))),function(e,n){return t.apply(this,arguments)})},{key:"setView",value:function(t){var e=this;t.addEventListener("create-overlay",(function(t){var n=t.detail.index,r=e.annotations.get(n);if(r){var i,o=xt(r);try{for(o.s();!(i=o.n()).done;){var a=i.value;e.view.addAnnotation(a)}}catch(t){o.e(t)}finally{o.f()}}})),t.addEventListener("draw-annotation",(function(t){var e=t.detail,n=e.draw,r=e.annotation,i=r.color,o=r.type;"highlight"===o?n(d.u.highlight,{color:i}):"underline"===o&&n(d.u.underline,{color:i})})),t.addEventListener("show-annotation",(function(t){var n=e.annotationsByValue.get(t.detail.value),r=S(t.detail.range);at({annotation:n,pos:r})})),t.addEventListener("external-link",(function(t){t.preventDefault(),ut(t.detail)})),t.addEventListener("link",(function(t){var n;return null===(n=_t(z,e).handle(e.view.book,t))||void 0===n?void 0:n.catch((function(n){console.warn(n),e.view.goTo(t.detail.href)}))})),t.history.addEventListener("pushstate",(function(e){rt("onPushState",{canGoBack:t.history.canGoBack,canGoForward:t.history.canGoForward})})),t.addEventListener("click-image",function(){var t=(0,s.A)(l().mark((function t(e){var n,r,i;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return console.log("click-image",e.detail.img.src),n=e.detail.img.src,t.next=1,fetch(n).then((function(t){return t.blob()}));case 1:return r=t.sent,t.next=2,new Promise((function(t,e){var n=new FileReader;n.onloadend=function(){return t(n.result)},n.onerror=e,n.readAsDataURL(r)}));case 2:i=t.sent,rt("onImageClick",i);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}},{key:"renderAnnotation",value:function(t){var e,n,r=xt(null!==(e=null!=t?t:allAnnotations)&&void 0!==e?e:[]);try{for(r.s();!(n=r.n()).done;){var i=n.value,o=i.value,a=i.type,s=i.color,u=i.note,c={id:i.id,value:o,type:a,color:s,note:u};this.addAnnotation(c)}}catch(t){r.e(t)}finally{r.f()}}},{key:"showContextMenu",value:function(){return _(this.view,_t(B,this),_t(U,this))}},{key:"addAnnotation",value:function(t){var e=t.value,n=(e.split("/")[2].split("!")[0]-2)/2,r=this.annotations.get(n);r?r.push(t):this.annotations.set(n,[t]),this.annotationsByValue.set(e,t),"bookmark"===t.type?Tt($,this,Ct).call(this,t)&&(_t(Q,this).call(this,60),Et(V,this,{exists:!0,cfi:t.value,id:t.id})):this.view.addAnnotation(t)}},{key:"removeAnnotation",value:function(t){var e=this.annotationsByValue.get(t);if(e){var n=e.value,r=(n.split("/")[2].split("!")[0]-2)/2,i=this.annotations.get(r);if(i){var o=i.findIndex((function(t){return t.id===e.id}));-1!==o&&i.splice(o,1)}this.annotationsByValue.delete(n),this.view.addAnnotation(e,!0),"bookmark"===e.type&&Tt($,this,Ct).call(this,e)&&(_t(tt,this).call(this),this.handleBookmark(!0),Et(V,this,{exists:!1,cfi:null,id:null}))}}},{key:"index",get:function(){return _t(U,this)}},{key:"toc",get:function(){var t,e,n,r,i,o=this.view.getSectionFractions(),a=null!==(t=null===(e=this.view.lastLocation)||void 0===e||null===(e=e.tocItem)||void 0===e?void 0:e.href.split("#")[0])&&void 0!==t?t:"Not Found",s=o.findIndex((function(t){return t.href===a}));-1===s&&(s=0);var u=(null===(n=o[s])||void 0===n?void 0:n.fraction)||0,c=(null===(r=o[s+1])||void 0===r?void 0:r.fraction)||1,l=((null===(i=this.view.lastLocation)||void 0===i?void 0:i.chapterLocation.total)||1)/(c-u),f=function(t){t=t.split("#")[0];var e=o.find((function(e){return e.href===t}));return e?e.fraction:0},h=function(t,e){return(null==t?void 0:t.map((function(t){return{label:t.label,href:t.href,id:t.id,level:e,startPercentage:f(t.href),startPage:Math.ceil(f(t.href)*l),subitems:h(t.subitems,e+1)}})))||[]};return h(this.view.book.toc,1)}}]);var t}();function Lt(){var t=_t(U,this),e=this.annotations.get(t),n=!1,r=null;if(e){var i,o=xt(e);try{for(o.s();!(i=o.n()).done;){var a=i.value;if("bookmark"===a.type&&(n=!!Tt($,this,Ct).call(this,a)||n)){r=a,_t(Q,this).call(this,60);break}}}catch(t){o.e(t)}finally{o.f()}}Et(V,this,{exists:n,cfi:n?r.value:null,id:n?r.id:null}),n||_t(tt,this).call(this)}function Ct(t){var e,n=null===(e=this.view.lastLocation)||void 0===e?void 0:e.cfi,r=(0,p.ap)(n),i=(0,p.ap)(n,!0),o=t.value,a=(0,p.ap)(o);if((0,p.UD)(r,a)<=0&&(0,p.UD)(i,a)>0)return!0}function It(t){var e=t.detail,n=e.doc,r=e.index;Et(B,this,n),Et(U,this,r),T(this.view,n,r),_t(G,this).call(this),this.readingFeatures(yt)}function Rt(t){var e=t.detail,n=e.cfi,r=e.fraction,i=e.location,o=e.tocItem,a=e.pageItem,s=e.chapterLocation,u=a?"Page ".concat(a.label):"Loc ".concat(i.current);Tt($,this,Lt).call(this),ot({cfi:n,fraction:r,loc:u,tocItem:o,pageItem:a,location:i,chapterLocation:s,bookmark:_t(V,this)})}function jt(t){var e=t.detail,n=e.x,r=e.y,i=n/window.innerWidth,o=r/window.innerHeight;st(i,o)}var nt=function(){var t=(0,s.A)(l().mark((function t(e,n){var r;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=new et,globalThis.reader=r,t.next=1,r.open(e,n);case 1:dt?lt():(rt("onLoadEnd"),ct(),rt("renderAnnotations"));case 2:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),rt=function(t,e){window.flutter_inappwebview.callHandler(t,e)},it=function(){var t,e,n={scroll:!1,animated:!0};switch(gt.pageTurnStyle){case"slide":n.scroll=!1,n.animated=!0;break;case"scroll":n.scroll=!0,n.animated=!0;break;case"noAnimation":n.scroll=!1,n.animated=!1}reader.view.renderer.setAttribute("flow",n.scroll?"scrolled":"paginated"),reader.view.renderer.setAttribute("top-margin","".concat(gt.topMargin,"px")),reader.view.renderer.setAttribute("bottom-margin","".concat(gt.bottomMargin,"px")),reader.view.renderer.setAttribute("gap","".concat(gt.sideMargin,"%")),reader.view.renderer.setAttribute("background-color",gt.backgroundColor),reader.view.renderer.setAttribute("max-column-count",gt.maxColumnCount),reader.view.renderer.setAttribute("bgimg-url",gt.backgroundImage),n.animated?reader.view.renderer.setAttribute("animated","true"):reader.view.renderer.removeAttribute("animated");var r={fontSize:gt.fontSize,fontName:gt.fontName,fontPath:gt.fontPath,fontWeight:gt.fontWeight,letterSpacing:gt.letterSpacing,spacing:gt.spacing,paragraphSpacing:gt.paragraphSpacing,textIndent:gt.textIndent,fontColor:gt.fontColor,backgroundColor:gt.backgroundColor,justify:gt.justify,hyphenate:gt.hyphenate,writingMode:gt.writingMode,backgroundImage:gt.backgroundImage,flow:n.scroll};null===(t=(e=reader.view.renderer).setStyles)||void 0===t||t.call(e,F(r))},ot=function(t){var e,n,r=null===(e=t.tocItem)||void 0===e?void 0:e.label,i=null===(n=t.tocItem)||void 0===n?void 0:n.href,o=t.chapterLocation.total,a=t.chapterLocation.current,s=t.location.total,u=t.location.current,c=t.cfi,l=t.fraction;rt("onRelocated",{chapterTitle:r,chapterHref:i,chapterTotalPages:o,chapterCurrentPage:a,bookTotalPages:s,bookCurrentPage:u,cfi:c,percentage:l,bookmark:t.bookmark})},at=function(t){return rt("onAnnotationClick",t)},st=function(t,e){return rt("onClick",{x:t,y:e})},ut=function(t){return console.log(t)},ct=function(){return rt("onSetToc",reader.toc)},lt=function(){var t=(0,s.A)(l().mark((function t(){var e,n;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,reader.view.book.getCover();case 1:(e=t.sent)?((n=new FileReader).readAsDataURL(e),n.onloadend=function(){rt("onMetadata",mt(mt({},reader.view.book.metadata),{},{cover:n.result}))}):rt("onMetadata",mt(mt({},reader.view.book.metadata),{},{cover:null}));case 2:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();window.refreshToc=function(){return ct()},window.changeStyle=function(t){gt=mt(mt({},gt),t),console.log("changeStyle",JSON.stringify(gt)),it()},window.goToHref=function(t){return reader.view.goTo(t)},window.goToCfi=function(t){return reader.view.goTo(t)},window.goToPercent=function(t){return reader.view.goToFraction(t)},window.nextPage=function(){return reader.view.next()},window.prevPage=function(){return reader.view.prev()},window.setScroll=function(){gt.scroll=!0,gt.animated=!0,it()},window.setPaginated=function(){gt.scroll=!1,gt.animated=!0,it()},window.setNoAnimation=function(){gt.scroll=!1,gt.animated=!1,it()};var ft=function(t){D.open||A?rt("onSelectionEnd",mt(mt({},t),{},{footnote:!0})):rt("onSelectionEnd",mt(mt({},t),{},{footnote:!1}))};window.showContextMenu=function(){D.open?footnoteSelection():reader.showContextMenu()},window.getSelection=function(){return reader.getSelection()},window.clearSelection=function(){return reader.view.deselect()},window.addAnnotation=function(t){return reader.addAnnotation(t)},window.addBookmarkHere=function(){return reader.handleBookmark(!1)},window.removeAnnotation=function(t){return reader.removeAnnotation(t)},window.prevSection=function(){return reader.view.renderer.prevSection()},window.nextSection=function(){return reader.view.renderer.nextSection()},window.initTts=function(){return reader.view.initTTS()},window.ttsStop=function(){return reader.view.initTTS(!0)},window.ttsHere=function(){return initTts(),reader.view.tts.from(reader.view.lastLocation.range)},window.ttsNextSection=(0,s.A)(l().mark((function t(){return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,nextSection();case 1:return initTts(),t.abrupt("return",ttsNext());case 2:case"end":return t.stop()}}),t)}))),window.ttsPrevSection=function(){var t=(0,s.A)(l().mark((function t(e){return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,prevSection();case 1:return initTts(),t.abrupt("return",e?reader.view.tts.end():ttsNext());case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),window.ttsNext=(0,s.A)(l().mark((function t(){var e;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(e=reader.view.tts.next(!0))){t.next=1;break}return t.abrupt("return",e);case 1:return t.next=2,ttsNextSection();case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),window.ttsPrev=function(){return reader.view.tts.prev(!0)||ttsPrevSection(!0)},window.ttsPrepare=function(){return reader.view.tts.prepare()},window.clearSearch=function(){return reader.view.clearSearch()},window.search=function(){var t=(0,s.A)(l().mark((function t(e,n){var r,i,o,a,s,u,c,f,h;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(null==n&&(n={scope:"book",matchCase:!1,matchDiacritics:!1,matchWholeWords:!1}),r=e.trim()){t.next=1;break}return t.abrupt("return");case 1:i="section"===n.scope?reader.index:null,o=!1,a=!1,t.prev=2,u=Ot(reader.view.search(mt(mt({},n),{},{query:r,index:i})));case 3:return t.next=4,u.next();case 4:if(!(o=!(c=t.sent).done)){t.next=6;break}f=c.value,rt("onSearch","done"===f?{process:1}:"progress"in f?{process:f.progress}:f);case 5:o=!1,t.next=3;break;case 6:t.next=8;break;case 7:t.prev=7,h=t.catch(2),a=!0,s=h;case 8:if(t.prev=8,t.prev=9,!o||null==u.return){t.next=10;break}return t.next=10,u.return();case 10:if(t.prev=10,!a){t.next=11;break}throw s;case 11:return t.finish(10);case 12:return t.finish(8);case 13:case"end":return t.stop()}}),t,null,[[2,7,8,13],[9,,10,12]])})));return function(e,n){return t.apply(this,arguments)}}(),window.back=function(){return reader.view.history.back()},window.forward=function(){return reader.view.history.forward()},window.renderAnnotations=function(t){return reader.renderAnnotation(t)},window.theChapterContent=function(){return reader.getChapterContent()},window.previousContent=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2e3;return reader.getPreviousContent(t)},window.isFootNoteOpen=function(){return D.getAttribute("style").includes("display: block")},window.closeFootNote=function(){D.style.display="none",rt("onFootnoteClose")},window.readingFeatures=function(t){yt=mt(mt({},yt),t),reader.readingFeatures()},window.pullUp=function(){rt("onPullUp")};var ht=new URLSearchParams(window.location.search),dt=JSON.parse(ht.get("importing")),pt=JSON.parse(ht.get("url")),vt=JSON.parse(ht.get("initialCfi")),gt=JSON.parse(ht.get("style")),yt=JSON.parse(ht.get("readingRules"));fetch(pt).then((function(t){return t.blob()})).then((function(t){return nt(new File([t],new URL(pt,window.location.origin).pathname),vt)})).catch((function(t){return console.error(t)})),r()}catch(Pt){r(Pt)}}),1)},6565:function(t,e,n){"use strict";var r=n(6518),i=n(8551),o=n(2787);r({target:"Reflect",stat:!0,sham:!n(2211)},{getPrototypeOf:function(t){return o(i(t))}})},6575:function(t,e,n){"use strict";var r=n(9297);t.exports=function(t){return void 0!==t&&(r(t,"value")||r(t,"writable"))}},6594:function(t,e,n){"use strict";n(5823)("Int8",(function(t){return function(e,n,r){return t(this,e,n,r)}}))},6624:function(t,e,n){"use strict";n(6518)({target:"Math",stat:!0},{log1p:n(5359)})},6648:function(t,e,n){"use strict";n.r(e),n.d(e,{makePDF:function(){return u}});var r=n(467),i=n(4756),o=n.n(i),a=function(){var t=(0,r.A)(o().mark((function t(e,n){var r,i,a,s,u,c,l,f,h,d,p,v,g,y,b,m,x,w,k;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.getViewport({scale:1}),i=r.width/r.height,a=innerWidth/innerHeight,s=devicePixelRatio*(a/i),u=e.getViewport({scale:s}),(c=document.createElement("canvas")).height=u.height,c.width=u.width,l=c.getContext("2d"),t.next=1,e.render({canvasContext:l,viewport:u}).promise;case 1:return t.next=2,new Promise((function(t){return c.toBlob(t)}));case 2:if(f=t.sent,!n){t.next=3;break}return t.abrupt("return",f);case 3:return(h=document.createElement("div")).classList.add("textLayer"),g=pdfjsLib,t.next=4,e.getTextContent();case 4:return y=t.sent,b={textContentSource:y,container:h,viewport:u},t.next=5,g.renderTextLayer.call(g,b).promise;case 5:return(d=document.createElement("div")).classList.add("annotationLayer"),m=new pdfjsLib.AnnotationLayer({page:e,viewport:u,div:d}),t.next=6,e.getAnnotations();case 6:return x=t.sent,w={getDestinationHash:function(t){return JSON.stringify(t)},addLinkAttributes:function(t,e){return t.href=e}},k={annotations:x,linkService:w},t.next=7,m.render.call(m,k);case 7:return p=URL.createObjectURL(f),v=URL.createObjectURL(new Blob(['\n        <!DOCTYPE html>\n        <meta charset="utf-8">\n        <style>\n        :root {\n            --scale-factor: '.concat(s,";\n        }\n        html, body {\n            margin: 0;\n            padding: 0;\n        }\n        ").concat('\n/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the "License");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an "AS IS" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n:root {\n  --highlight-bg-color: rgb(180 0 170);\n  --highlight-selected-bg-color: rgb(0 100 0);\n}\n\n@media screen and (forced-colors: active) {\n  :root {\n    --highlight-bg-color: Highlight;\n    --highlight-selected-bg-color: ButtonText;\n  }\n}\n\n.textLayer {\n  position: absolute;\n  text-align: initial;\n  inset: 0;\n  overflow: hidden;\n  opacity: 0.25;\n  line-height: 1;\n  text-size-adjust: none;\n  forced-color-adjust: none;\n  transform-origin: 0 0;\n  z-index: 2;\n}\n\n.textLayer :is(span, br) {\n  color: transparent;\n  position: absolute;\n  white-space: pre;\n  cursor: text;\n  transform-origin: 0% 0%;\n}\n\n/* Only necessary in Google Chrome, see issue 14205, and most unfortunately\n * the problem doesn\'t show up in "text" reference tests. */\n/*#if !MOZCENTRAL*/\n.textLayer span.markedContent {\n  top: 0;\n  height: 0;\n}\n/*#endif*/\n\n.textLayer .highlight {\n  margin: -1px;\n  padding: 1px;\n  background-color: var(--highlight-bg-color);\n  border-radius: 4px;\n}\n\n.textLayer .highlight.appended {\n  position: initial;\n}\n\n.textLayer .highlight.begin {\n  border-radius: 4px 0 0 4px;\n}\n\n.textLayer .highlight.end {\n  border-radius: 0 4px 4px 0;\n}\n\n.textLayer .highlight.middle {\n  border-radius: 0;\n}\n\n.textLayer .highlight.selected {\n  background-color: var(--highlight-selected-bg-color);\n}\n\n.textLayer ::selection {\n  /*#if !MOZCENTRAL*/\n  background: blue;\n  /*#endif*/\n  background: AccentColor; /* stylelint-disable-line declaration-block-no-duplicate-properties */\n}\n\n/* Avoids https://github.com/mozilla/pdf.js/issues/13840 in Chrome */\n/*#if !MOZCENTRAL*/\n.textLayer br::selection {\n  background: transparent;\n}\n/*#endif*/\n\n.textLayer .endOfContent {\n  display: block;\n  position: absolute;\n  inset: 100% 0 0;\n  z-index: -1;\n  cursor: default;\n  user-select: none;\n}\n\n.textLayer .endOfContent.active {\n  top: 0;\n}\n',"\n        ").concat('\n/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the "License");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an "AS IS" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n:root {\n  --annotation-unfocused-field-background: url("data:image/svg+xml;charset=UTF-8,<svg width=\'1px\' height=\'1px\' xmlns=\'http://www.w3.org/2000/svg\'><rect width=\'100%\' height=\'100%\' style=\'fill:rgba(0, 54, 255, 0.13);\'/></svg>");\n  --input-focus-border-color: Highlight;\n  --input-focus-outline: 1px solid Canvas;\n  --input-unfocused-border-color: transparent;\n  --input-disabled-border-color: transparent;\n  --input-hover-border-color: black;\n  --link-outline: none;\n}\n\n@media screen and (forced-colors: active) {\n  :root {\n    --input-focus-border-color: CanvasText;\n    --input-unfocused-border-color: ActiveText;\n    --input-disabled-border-color: GrayText;\n    --input-hover-border-color: Highlight;\n    --link-outline: 1.5px solid LinkText;\n    --hcm-highligh-filter: invert(100%);\n  }\n  .annotationLayer .textWidgetAnnotation :is(input, textarea):required,\n  .annotationLayer .choiceWidgetAnnotation select:required,\n  .annotationLayer\n    .buttonWidgetAnnotation:is(.checkBox, .radioButton)\n    input:required {\n    outline: 1.5px solid selectedItem;\n  }\n\n  .annotationLayer .linkAnnotation:hover {\n    backdrop-filter: var(--hcm-highligh-filter);\n  }\n\n  .annotationLayer .linkAnnotation > a:hover {\n    opacity: 0 !important;\n    background: none !important;\n    box-shadow: none;\n  }\n\n  .annotationLayer .popupAnnotation .popup {\n    outline: calc(1.5px * var(--scale-factor)) solid CanvasText !important;\n    background-color: ButtonFace !important;\n    color: ButtonText !important;\n  }\n\n  .annotationLayer .highlightArea:hover::after {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    backdrop-filter: var(--hcm-highligh-filter);\n    content: "";\n    pointer-events: none;\n  }\n\n  .annotationLayer .popupAnnotation.focused .popup {\n    outline: calc(3px * var(--scale-factor)) solid Highlight !important;\n  }\n}\n\n.annotationLayer {\n  position: absolute;\n  top: 0;\n  left: 0;\n  pointer-events: none;\n  transform-origin: 0 0;\n  z-index: 3;\n}\n\n.annotationLayer[data-main-rotation="90"] .norotate {\n  transform: rotate(270deg) translateX(-100%);\n}\n.annotationLayer[data-main-rotation="180"] .norotate {\n  transform: rotate(180deg) translate(-100%, -100%);\n}\n.annotationLayer[data-main-rotation="270"] .norotate {\n  transform: rotate(90deg) translateY(-100%);\n}\n\n.annotationLayer canvas {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n}\n\n.annotationLayer section {\n  position: absolute;\n  text-align: initial;\n  pointer-events: auto;\n  box-sizing: border-box;\n  transform-origin: 0 0;\n}\n\n.annotationLayer .linkAnnotation {\n  outline: var(--link-outline);\n}\n\n.annotationLayer :is(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a {\n  position: absolute;\n  font-size: 1em;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.annotationLayer\n  :is(.linkAnnotation, .buttonWidgetAnnotation.pushButton):not(.hasBorder)\n  > a:hover {\n  opacity: 0.2;\n  background-color: rgb(255 255 0);\n  box-shadow: 0 2px 10px rgb(255 255 0);\n}\n\n.annotationLayer .linkAnnotation.hasBorder:hover {\n  background-color: rgb(255 255 0 / 0.2);\n}\n\n.annotationLayer .hasBorder {\n  background-size: 100% 100%;\n}\n\n.annotationLayer .textAnnotation img {\n  position: absolute;\n  cursor: pointer;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n}\n\n.annotationLayer .textWidgetAnnotation :is(input, textarea),\n.annotationLayer .choiceWidgetAnnotation select,\n.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input {\n  background-image: var(--annotation-unfocused-field-background);\n  border: 2px solid var(--input-unfocused-border-color);\n  box-sizing: border-box;\n  font: calc(9px * var(--scale-factor)) sans-serif;\n  height: 100%;\n  margin: 0;\n  vertical-align: top;\n  width: 100%;\n}\n\n.annotationLayer .textWidgetAnnotation :is(input, textarea):required,\n.annotationLayer .choiceWidgetAnnotation select:required,\n.annotationLayer\n  .buttonWidgetAnnotation:is(.checkBox, .radioButton)\n  input:required {\n  outline: 1.5px solid red;\n}\n\n.annotationLayer .choiceWidgetAnnotation select option {\n  padding: 0;\n}\n\n.annotationLayer .buttonWidgetAnnotation.radioButton input {\n  border-radius: 50%;\n}\n\n.annotationLayer .textWidgetAnnotation textarea {\n  resize: none;\n}\n\n.annotationLayer .textWidgetAnnotation :is(input, textarea)[disabled],\n.annotationLayer .choiceWidgetAnnotation select[disabled],\n.annotationLayer\n  .buttonWidgetAnnotation:is(.checkBox, .radioButton)\n  input[disabled] {\n  background: none;\n  border: 2px solid var(--input-disabled-border-color);\n  cursor: not-allowed;\n}\n\n.annotationLayer .textWidgetAnnotation :is(input, textarea):hover,\n.annotationLayer .choiceWidgetAnnotation select:hover,\n.annotationLayer\n  .buttonWidgetAnnotation:is(.checkBox, .radioButton)\n  input:hover {\n  border: 2px solid var(--input-hover-border-color);\n}\n.annotationLayer .textWidgetAnnotation :is(input, textarea):hover,\n.annotationLayer .choiceWidgetAnnotation select:hover,\n.annotationLayer .buttonWidgetAnnotation.checkBox input:hover {\n  border-radius: 2px;\n}\n\n.annotationLayer .textWidgetAnnotation :is(input, textarea):focus,\n.annotationLayer .choiceWidgetAnnotation select:focus {\n  background: none;\n  border: 2px solid var(--input-focus-border-color);\n  border-radius: 2px;\n  outline: var(--input-focus-outline);\n}\n\n.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) :focus {\n  background-image: none;\n  background-color: transparent;\n}\n\n.annotationLayer .buttonWidgetAnnotation.checkBox :focus {\n  border: 2px solid var(--input-focus-border-color);\n  border-radius: 2px;\n  outline: var(--input-focus-outline);\n}\n\n.annotationLayer .buttonWidgetAnnotation.radioButton :focus {\n  border: 2px solid var(--input-focus-border-color);\n  outline: var(--input-focus-outline);\n}\n\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before,\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after,\n.annotationLayer .buttonWidgetAnnotation.radioButton input:checked::before {\n  background-color: CanvasText;\n  content: "";\n  display: block;\n  position: absolute;\n}\n\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before,\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after {\n  height: 80%;\n  left: 45%;\n  width: 1px;\n}\n\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before {\n  transform: rotate(45deg);\n}\n\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after {\n  transform: rotate(-45deg);\n}\n\n.annotationLayer .buttonWidgetAnnotation.radioButton input:checked::before {\n  border-radius: 50%;\n  height: 50%;\n  left: 30%;\n  top: 20%;\n  width: 50%;\n}\n\n.annotationLayer .textWidgetAnnotation input.comb {\n  font-family: monospace;\n  padding-left: 2px;\n  padding-right: 0;\n}\n\n.annotationLayer .textWidgetAnnotation input.comb:focus {\n  /*\n   * Letter spacing is placed on the right side of each character. Hence, the\n   * letter spacing of the last character may be placed outside the visible\n   * area, causing horizontal scrolling. We avoid this by extending the width\n   * when the element has focus and revert this when it loses focus.\n   */\n  width: 103%;\n}\n\n.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input {\n  appearance: none;\n}\n\n.annotationLayer .fileAttachmentAnnotation .popupTriggerArea {\n  height: 100%;\n  width: 100%;\n}\n\n.annotationLayer .popupAnnotation {\n  position: absolute;\n  font-size: calc(9px * var(--scale-factor));\n  pointer-events: none;\n  width: max-content;\n  max-width: 45%;\n  height: auto;\n}\n\n.annotationLayer .popup {\n  background-color: rgb(255 255 153);\n  box-shadow: 0 calc(2px * var(--scale-factor)) calc(5px * var(--scale-factor))\n    rgb(136 136 136);\n  border-radius: calc(2px * var(--scale-factor));\n  outline: 1.5px solid rgb(255 255 74);\n  padding: calc(6px * var(--scale-factor));\n  cursor: pointer;\n  font: message-box;\n  white-space: normal;\n  word-wrap: break-word;\n  pointer-events: auto;\n}\n\n.annotationLayer .popupAnnotation.focused .popup {\n  outline-width: 3px;\n}\n\n.annotationLayer .popup * {\n  font-size: calc(9px * var(--scale-factor));\n}\n\n.annotationLayer .popup > .header {\n  display: inline-block;\n}\n\n.annotationLayer .popup > .header h1 {\n  display: inline;\n}\n\n.annotationLayer .popup > .header .popupDate {\n  display: inline-block;\n  margin-left: calc(5px * var(--scale-factor));\n  width: fit-content;\n}\n\n.annotationLayer .popupContent {\n  border-top: 1px solid rgb(51 51 51);\n  margin-top: calc(2px * var(--scale-factor));\n  padding-top: calc(2px * var(--scale-factor));\n}\n\n.annotationLayer .richText > * {\n  white-space: pre-wrap;\n  font-size: calc(9px * var(--scale-factor));\n}\n\n.annotationLayer .popupTriggerArea {\n  cursor: pointer;\n}\n\n.annotationLayer section svg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n}\n\n.annotationLayer .annotationTextContent {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  opacity: 0;\n  color: transparent;\n  user-select: none;\n  pointer-events: none;\n}\n\n.annotationLayer .annotationTextContent span {\n  width: 100%;\n  display: inline-block;\n}\n\n.annotationLayer svg.quadrilateralsContainer {\n  contain: strict;\n  width: 0;\n  height: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: -1;\n}\n','\n        </style>\n        <img src="').concat(p,'">\n        ').concat(h.outerHTML,"\n        ").concat(d.outerHTML,"\n    ")],{type:"text/html"})),t.abrupt("return",v);case 8:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),s=function(t){return{label:t.title,href:JSON.stringify(t.dest),subitems:t.items.length?t.items.map(s):null}},u=function(){var t=(0,r.A)(o().mark((function t(e){var n,i,u,c,l,f,h,d,p,v,g,y;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return d=Uint8Array,t.next=1,e.arrayBuffer();case 1:return p=t.sent,i=new d(p),t.next=2,pdfjsLib.getDocument({data:i}).promise;case 2:return u=t.sent,c={rendition:{layout:"pre-paginated"}},t.next=3,u.getMetadata();case 3:if(g=n=t.sent,v=null===g){t.next=4;break}v=void 0===n;case 4:if(!v){t.next=5;break}y=void 0,t.next=6;break;case 5:y=n.info;case 6:return l=y,c.metadata={title:null==l?void 0:l.Title,author:null==l?void 0:l.Author},t.next=7,u.getOutline();case 7:return f=t.sent,c.toc=null==f?void 0:f.map(s),h=new Map,c.sections=Array.from({length:u.numPages}).map((function(t,e){return{id:e,load:(n=(0,r.A)(o().mark((function t(){var n,r,i,s;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(n=h.get(e))){t.next=1;break}return t.abrupt("return",n);case 1:return i=a,t.next=2,u.getPage(e+1);case 2:return s=t.sent,t.next=3,i(s);case 3:return r=t.sent,h.set(e,r),t.abrupt("return",r);case 4:case"end":return t.stop()}}),t)}))),function(){return n.apply(this,arguments)}),size:1e3};var n})),c.sections[0].pageSpread="right",c.isExternal=function(t){return/^\w+:/i.test(t)},c.resolveHref=function(){var t=(0,r.A)(o().mark((function t(e){var n,r,i,a;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("string"!=typeof(n=JSON.parse(e))){t.next=2;break}return t.next=1,u.getDestination(n);case 1:a=t.sent,t.next=3;break;case 2:a=n;case 3:return r=a,t.next=4,u.getPageIndex(r[0]);case 4:return i=t.sent,t.abrupt("return",{index:i});case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),c.splitTOCHref=function(){var t=(0,r.A)(o().mark((function t(e){var n,r,i,a;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("string"!=typeof(n=JSON.parse(e))){t.next=2;break}return t.next=1,u.getDestination(n);case 1:a=t.sent,t.next=3;break;case 2:a=n;case 3:return r=a,t.next=4,u.getPageIndex(r[0]);case 4:return i=t.sent,t.abrupt("return",[i,null]);case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),c.getTOCFragment=function(t){return t.documentElement},c.getCover=(0,r.A)(o().mark((function t(){var e,n;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=a,t.next=1,u.getPage(1);case 1:return n=t.sent,t.abrupt("return",e(n,!0));case 2:case"end":return t.stop()}}),t)}))),t.abrupt("return",c);case 8:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()},6651:function(t,e,n){"use strict";var r=n(4644),i=n(9617).indexOf,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("indexOf",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},6682:function(t,e,n){"use strict";var r=n(9565),i=n(8551),o=n(4901),a=n(2195),s=n(7323),u=TypeError;t.exports=function(t,e){var n=t.exec;if(o(n)){var c=r(n,t,e);return null!==c&&i(c),c}if("RegExp"===a(t))return r(s,t,e);throw new u("RegExp#exec called on incompatible receiver")}},6699:function(t,e,n){"use strict";var r=n(3724),i=n(4913),o=n(6980);t.exports=r?function(t,e,n){return i.f(t,e,o(1,n))}:function(t,e,n){return t[e]=n,t}},6706:function(t,e,n){"use strict";var r=n(9504),i=n(9306);t.exports=function(t,e,n){try{return r(i(Object.getOwnPropertyDescriptor(t,e)[n]))}catch(t){}}},6754:function(t,e,n){"use strict";var r=n(8981),i=n(5610),o=n(6198);t.exports=function(t){for(var e=r(this),n=o(e),a=arguments.length,s=i(a>1?arguments[1]:void 0,n),u=a>2?arguments[2]:void 0,c=void 0===u?n:i(u,n);c>s;)e[s++]=t;return e}},6761:function(t,e,n){"use strict";var r=n(6518),i=n(4576),o=n(9565),a=n(9504),s=n(6395),u=n(3724),c=n(4495),l=n(9039),f=n(9297),h=n(1625),d=n(8551),p=n(5397),v=n(6969),g=n(655),y=n(6980),b=n(2360),m=n(1072),x=n(8480),w=n(298),k=n(3717),A=n(7347),S=n(4913),E=n(6801),_=n(8773),T=n(6840),O=n(2106),M=n(5745),L=n(6119),C=n(421),I=n(3392),R=n(8227),j=n(1951),P=n(511),F=n(8242),N=n(687),D=n(1181),z=n(9213).forEach,B=L("hidden"),U="Symbol",W="prototype",H=D.set,q=D.getterFor(U),V=Object[W],$=i.Symbol,G=$&&$[W],X=i.RangeError,Y=i.TypeError,K=i.QObject,J=A.f,Z=S.f,Q=w.f,tt=_.f,et=a([].push),nt=M("symbols"),rt=M("op-symbols"),it=M("wks"),ot=!K||!K[W]||!K[W].findChild,at=function(t,e,n){var r=J(V,e);r&&delete V[e],Z(t,e,n),r&&t!==V&&Z(V,e,r)},st=u&&l((function(){return 7!==b(Z({},"a",{get:function(){return Z(this,"a",{value:7}).a}})).a}))?at:Z,ut=function(t,e){var n=nt[t]=b(G);return H(n,{type:U,tag:t,description:e}),u||(n.description=e),n},ct=function(t,e,n){t===V&&ct(rt,e,n),d(t);var r=v(e);return d(n),f(nt,r)?(n.enumerable?(f(t,B)&&t[B][r]&&(t[B][r]=!1),n=b(n,{enumerable:y(0,!1)})):(f(t,B)||Z(t,B,y(1,b(null))),t[B][r]=!0),st(t,r,n)):Z(t,r,n)},lt=function(t,e){d(t);var n=p(e),r=m(n).concat(pt(n));return z(r,(function(e){u&&!o(ft,n,e)||ct(t,e,n[e])})),t},ft=function(t){var e=v(t),n=o(tt,this,e);return!(this===V&&f(nt,e)&&!f(rt,e))&&(!(n||!f(this,e)||!f(nt,e)||f(this,B)&&this[B][e])||n)},ht=function(t,e){var n=p(t),r=v(e);if(n!==V||!f(nt,r)||f(rt,r)){var i=J(n,r);return!i||!f(nt,r)||f(n,B)&&n[B][r]||(i.enumerable=!0),i}},dt=function(t){var e=Q(p(t)),n=[];return z(e,(function(t){f(nt,t)||f(C,t)||et(n,t)})),n},pt=function(t){var e=t===V,n=Q(e?rt:p(t)),r=[];return z(n,(function(t){!f(nt,t)||e&&!f(V,t)||et(r,nt[t])})),r};c||($=function(){if(h(G,this))throw new Y("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?g(arguments[0]):void 0,e=I(t),n=function(t){var r=void 0===this?i:this;r===V&&o(n,rt,t),f(r,B)&&f(r[B],e)&&(r[B][e]=!1);var a=y(1,t);try{st(r,e,a)}catch(t){if(!(t instanceof X))throw t;at(r,e,a)}};return u&&ot&&st(V,e,{configurable:!0,set:n}),ut(e,t)},T(G=$[W],"toString",(function(){return q(this).tag})),T($,"withoutSetter",(function(t){return ut(I(t),t)})),_.f=ft,S.f=ct,E.f=lt,A.f=ht,x.f=w.f=dt,k.f=pt,j.f=function(t){return ut(R(t),t)},u&&(O(G,"description",{configurable:!0,get:function(){return q(this).description}}),s||T(V,"propertyIsEnumerable",ft,{unsafe:!0}))),r({global:!0,constructor:!0,wrap:!0,forced:!c,sham:!c},{Symbol:$}),z(m(it),(function(t){P(t)})),r({target:U,stat:!0,forced:!c},{useSetter:function(){ot=!0},useSimple:function(){ot=!1}}),r({target:"Object",stat:!0,forced:!c,sham:!u},{create:function(t,e){return void 0===e?b(t):lt(b(t),e)},defineProperty:ct,defineProperties:lt,getOwnPropertyDescriptor:ht}),r({target:"Object",stat:!0,forced:!c},{getOwnPropertyNames:dt}),F(),N($,U),C[B]=!0},6801:function(t,e,n){"use strict";var r=n(3724),i=n(8686),o=n(4913),a=n(8551),s=n(5397),u=n(1072);e.f=r&&!i?Object.defineProperties:function(t,e){a(t);for(var n,r=s(e),i=u(e),c=i.length,l=0;c>l;)o.f(t,n=i[l++],r[n]);return t}},6812:function(t,e,n){"use strict";var r=n(4644),i=n(8745),o=n(8379),a=r.aTypedArray;(0,r.exportTypedArrayMethod)("lastIndexOf",(function(t){var e=arguments.length;return i(o,a(this),e>1?[t,arguments[1]]:[t])}))},6822:function(t,e,n){"use strict";n.d(e,{A:function(){return o}});var r=n(2284),i=n(9417);function o(t,e){if(e&&("object"==(0,r.A)(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return(0,i.A)(t)}},6823:function(t){"use strict";var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},6835:function(t,e,n){"use strict";var r=n(6518),i=n(7029),o=n(6469);r({target:"Array",proto:!0},{copyWithin:i}),o("copyWithin")},6837:function(t){"use strict";var e=TypeError;t.exports=function(t){if(t>9007199254740991)throw e("Maximum allowed index exceeded");return t}},6840:function(t,e,n){"use strict";var r=n(4901),i=n(4913),o=n(283),a=n(9433);t.exports=function(t,e,n,s){s||(s={});var u=s.enumerable,c=void 0!==s.name?s.name:e;if(r(n)&&o(n,c,s),s.global)u?t[e]=n:a(e,n);else{try{s.unsafe?t[e]&&(u=!0):delete t[e]}catch(t){}u?t[e]=n:i.f(t,e,{value:n,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return t}},6910:function(t,e,n){"use strict";var r=n(6518),i=n(9504),o=n(9306),a=n(8981),s=n(6198),u=n(4606),c=n(655),l=n(9039),f=n(4488),h=n(4598),d=n(3709),p=n(3763),v=n(9519),g=n(3607),y=[],b=i(y.sort),m=i(y.push),x=l((function(){y.sort(void 0)})),w=l((function(){y.sort(null)})),k=h("sort"),A=!l((function(){if(v)return v<70;if(!(d&&d>3)){if(p)return!0;if(g)return g<603;var t,e,n,r,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(r=0;r<47;r++)y.push({k:e+r,v:n})}for(y.sort((function(t,e){return e.v-t.v})),r=0;r<y.length;r++)e=y[r].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}}));r({target:"Array",proto:!0,forced:x||!w||!k||!A},{sort:function(t){void 0!==t&&o(t);var e=a(this);if(A)return void 0===t?b(e):b(e,t);var n,r,i=[],l=s(e);for(r=0;r<l;r++)r in e&&m(i,e[r]);for(f(i,function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:c(e)>c(n)?1:-1}}(t)),n=s(i),r=0;r<n;)e[r]=i[r++];for(;r<l;)u(e,r++);return e}})},6933:function(t,e,n){"use strict";var r=n(9504),i=n(4376),o=n(4901),a=n(2195),s=n(655),u=r([].push);t.exports=function(t){if(o(t))return t;if(i(t)){for(var e=t.length,n=[],r=0;r<e;r++){var c=t[r];"string"==typeof c?u(n,c):"number"!=typeof c&&"Number"!==a(c)&&"String"!==a(c)||u(n,s(c))}var l=n.length,f=!0;return function(t,e){if(f)return f=!1,e;if(i(this))return e;for(var r=0;r<l;r++)if(n[r]===t)return e}}}},6938:function(t,e,n){"use strict";var r=n(2360),i=n(2106),o=n(6279),a=n(6080),s=n(679),u=n(4117),c=n(2652),l=n(1088),f=n(2529),h=n(7633),d=n(3724),p=n(3451).fastKey,v=n(1181),g=v.set,y=v.getterFor;t.exports={getConstructor:function(t,e,n,l){var f=t((function(t,i){s(t,h),g(t,{type:e,index:r(null),first:null,last:null,size:0}),d||(t.size=0),u(i)||c(i,t[l],{that:t,AS_ENTRIES:n})})),h=f.prototype,v=y(e),b=function(t,e,n){var r,i,o=v(t),a=m(t,e);return a?a.value=n:(o.last=a={index:i=p(e,!0),key:e,value:n,previous:r=o.last,next:null,removed:!1},o.first||(o.first=a),r&&(r.next=a),d?o.size++:t.size++,"F"!==i&&(o.index[i]=a)),t},m=function(t,e){var n,r=v(t),i=p(e);if("F"!==i)return r.index[i];for(n=r.first;n;n=n.next)if(n.key===e)return n};return o(h,{clear:function(){for(var t=v(this),e=t.first;e;)e.removed=!0,e.previous&&(e.previous=e.previous.next=null),e=e.next;t.first=t.last=null,t.index=r(null),d?t.size=0:this.size=0},delete:function(t){var e=this,n=v(e),r=m(e,t);if(r){var i=r.next,o=r.previous;delete n.index[r.index],r.removed=!0,o&&(o.next=i),i&&(i.previous=o),n.first===r&&(n.first=i),n.last===r&&(n.last=o),d?n.size--:e.size--}return!!r},forEach:function(t){for(var e,n=v(this),r=a(t,arguments.length>1?arguments[1]:void 0);e=e?e.next:n.first;)for(r(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!m(this,t)}}),o(h,n?{get:function(t){var e=m(this,t);return e&&e.value},set:function(t,e){return b(this,0===t?0:t,e)}}:{add:function(t){return b(this,t=0===t?0:t,t)}}),d&&i(h,"size",{configurable:!0,get:function(){return v(this).size}}),f},setStrong:function(t,e,n){var r=e+" Iterator",i=y(e),o=y(r);l(t,e,(function(t,e){g(this,{type:r,target:t,state:i(t),kind:e,last:null})}),(function(){for(var t=o(this),e=t.kind,n=t.last;n&&n.removed;)n=n.previous;return t.target&&(t.last=n=n?n.next:t.state.first)?f("keys"===e?n.key:"values"===e?n.value:[n.key,n.value],!1):(t.target=null,f(void 0,!0))}),n?"entries":"values",!n,!0),h(e)}}},6955:function(t,e,n){"use strict";var r=n(2140),i=n(4901),o=n(2195),a=n(8227)("toStringTag"),s=Object,u="Arguments"===o(function(){return arguments}());t.exports=r?o:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=s(t),a))?n:u?o(e):"Object"===(r=o(e))&&i(e.callee)?"Arguments":r}},6964:function(t,e,n){"use strict";n(511)("match")},6969:function(t,e,n){"use strict";var r=n(2777),i=n(757);t.exports=function(t){var e=r(t,"string");return i(e)?e:e+""}},6973:function(t,e,n){"use strict";n.r(e),n.d(e,{makeComicBook:function(){return u}});var r=n(467),i=n(4756),o=n.n(i);function a(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return s(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,u=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){u=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw o}}}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var u=function(t,e){var n=t.entries,i=t.loadBlob,s=t.getSize,u=new Map,c=new Map,l=function(){var t=(0,r.A)(o().mark((function t(e){var n,r,a,s;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!u.has(e)){t.next=1;break}return t.abrupt("return",u.get(e));case 1:return a=URL,t.next=2,i(e);case 2:return s=t.sent,n=a.createObjectURL.call(a,s),r=URL.createObjectURL(new Blob(['<img src="'.concat(n,'">')],{type:"text/html"})),c.set(e,[n,r]),u.set(e,r),t.abrupt("return",r);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),f=[".jpg",".jpeg",".png",".gif",".bmp",".webp",".svg",".jxl",".avif"],h=n.map((function(t){return t.filename})).filter((function(t){return f.some((function(e){return t.endsWith(e)}))})).sort();if(!h.length)throw new Error("No supported image files in archive");var d={getCover:function(){return i(h[0])}};return d.metadata={title:e.name},d.sections=h.map((function(t){return{id:t,load:function(){return l(t)},unload:function(){return function(t){var e,n;null===(e=c.get(t))||void 0===e||null===(n=e.forEach)||void 0===n||n.call(e,(function(t){return URL.revokeObjectURL(t)})),c.delete(t),u.delete(t)}(t)},size:s(t)}})),d.toc=h.map((function(t){return{label:t,href:t}})),d.rendition={layout:"pre-paginated"},d.resolveHref=function(t){return{index:d.sections.findIndex((function(e){return e.id===t}))}},d.splitTOCHref=function(t){return[t,null]},d.getTOCFragment=function(t){return t.documentElement},d.destroy=function(){var t,e=a(c.values());try{for(e.s();!(t=e.n()).done;){var n,r=a(t.value);try{for(r.s();!(n=r.n()).done;){var i=n.value;URL.revokeObjectURL(i)}}catch(t){r.e(t)}finally{r.f()}}}catch(t){e.e(t)}finally{e.f()}},d}},6980:function(t){"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},6993:function(t,e,n){var r=n(5546);function i(){var e,n,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.toStringTag||"@@toStringTag";function u(t,i,o,a){var s=i&&i.prototype instanceof l?i:l,u=Object.create(s.prototype);return r(u,"_invoke",function(t,r,i){var o,a,s,u=0,l=i||[],f=!1,h={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return o=t,a=0,s=e,h.n=n,c}};function d(t,r){for(a=t,s=r,n=0;!f&&u&&!i&&n<l.length;n++){var i,o=l[n],d=h.p,p=o[2];t>3?(i=p===r)&&(s=o[(a=o[4])?5:(a=3,3)],o[4]=o[5]=e):o[0]<=d&&((i=t<2&&d<o[1])?(a=0,h.v=r,h.n=o[1]):d<p&&(i=t<3||o[0]>r||r>p)&&(o[4]=t,o[5]=r,h.n=p,a=0))}if(i||t>1)return c;throw f=!0,r}return function(i,l,p){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&d(l,p),a=l,s=p;(n=a<2?e:s)||!f;){o||(a?a<3?(a>1&&(h.n=-1),d(a,s)):h.n=s:h.v=s);try{if(u=2,o){if(a||(i="next"),n=o[i]){if(!(n=n.call(o,s)))throw TypeError("iterator result is not an object");if(!n.done)return n;s=n.value,a<2&&(a=0)}else 1===a&&(n=o.return)&&n.call(o),a<2&&(s=TypeError("The iterator does not provide a '"+i+"' method"),a=1);o=e}else if((n=(f=h.n<0)?s:t.call(r,h))!==c)break}catch(t){o=e,a=1,s=t}finally{u=1}}return{value:n,done:f}}}(t,o,a),!0),u}var c={};function l(){}function f(){}function h(){}n=Object.getPrototypeOf;var d=[][a]?n(n([][a]())):(r(n={},a,(function(){return this})),n),p=h.prototype=l.prototype=Object.create(d);function v(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,r(t,s,"GeneratorFunction")),t.prototype=Object.create(p),t}return f.prototype=h,r(p,"constructor",h),r(h,"constructor",f),f.displayName="GeneratorFunction",r(h,s,"GeneratorFunction"),r(p),r(p,s,"Generator"),r(p,a,(function(){return this})),r(p,"toString",(function(){return"[object Generator]"})),(t.exports=i=function(){return{w:u,m:v}},t.exports.__esModule=!0,t.exports.default=t.exports)()}t.exports=i,t.exports.__esModule=!0,t.exports.default=t.exports},7029:function(t,e,n){"use strict";var r=n(8981),i=n(5610),o=n(6198),a=n(4606),s=Math.min;t.exports=[].copyWithin||function(t,e){var n=r(this),u=o(n),c=i(t,u),l=i(e,u),f=arguments.length>2?arguments[2]:void 0,h=s((void 0===f?u:i(f,u))-l,u-c),d=1;for(l<c&&c<l+h&&(d=-1,l+=h-1,c+=h-1);h-- >0;)l in n?n[c]=n[l]:a(n,c),c+=d,l+=d;return n}},7040:function(t,e,n){"use strict";var r=n(4495);t.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},7055:function(t,e,n){"use strict";var r=n(9504),i=n(9039),o=n(2195),a=Object,s=r("".split);t.exports=i((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===o(t)?s(t,""):a(t)}:a},7106:function(t,e,n){"use strict";function r(t,e){this.v=t,this.k=e}n.d(e,{A:function(){return r}})},7208:function(t,e,n){"use strict";var r=n(6518),i=n(9565);r({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return i(URL.prototype.toString,this)}})},7220:function(t,e,n){"use strict";var r=n(6518),i=n(3904);r({target:"Number",stat:!0,forced:Number.parseFloat!==i},{parseFloat:i})},7301:function(t,e,n){"use strict";var r=n(4644),i=n(9213).some,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("some",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},7323:function(t,e,n){"use strict";var r,i,o=n(9565),a=n(9504),s=n(655),u=n(7979),c=n(8429),l=n(5745),f=n(2360),h=n(1181).get,d=n(3635),p=n(8814),v=l("native-string-replace",String.prototype.replace),g=RegExp.prototype.exec,y=g,b=a("".charAt),m=a("".indexOf),x=a("".replace),w=a("".slice),k=(i=/b*/g,o(g,r=/a/,"a"),o(g,i,"a"),0!==r.lastIndex||0!==i.lastIndex),A=c.BROKEN_CARET,S=void 0!==/()??/.exec("")[1];(k||S||A||d||p)&&(y=function(t){var e,n,r,i,a,c,l,d=this,p=h(d),E=s(t),_=p.raw;if(_)return _.lastIndex=d.lastIndex,e=o(y,_,E),d.lastIndex=_.lastIndex,e;var T=p.groups,O=A&&d.sticky,M=o(u,d),L=d.source,C=0,I=E;if(O&&(M=x(M,"y",""),-1===m(M,"g")&&(M+="g"),I=w(E,d.lastIndex),d.lastIndex>0&&(!d.multiline||d.multiline&&"\n"!==b(E,d.lastIndex-1))&&(L="(?: "+L+")",I=" "+I,C++),n=new RegExp("^(?:"+L+")",M)),S&&(n=new RegExp("^"+L+"$(?!\\s)",M)),k&&(r=d.lastIndex),i=o(g,O?n:d,I),O?i?(i.input=w(i.input,C),i[0]=w(i[0],C),i.index=d.lastIndex,d.lastIndex+=i[0].length):d.lastIndex=0:k&&i&&(d.lastIndex=d.global?i.index+i[0].length:r),S&&i&&i.length>1&&o(v,i[0],n,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(i[a]=void 0)})),i&&T)for(i.groups=c=f(null),a=0;a<T.length;a++)c[(l=T[a])[0]]=i[l[1]];return i}),t.exports=y},7337:function(t,e,n){"use strict";var r=n(6518),i=n(9504),o=n(5610),a=RangeError,s=String.fromCharCode,u=String.fromCodePoint,c=i([].join);r({target:"String",stat:!0,arity:1,forced:!!u&&1!==u.length},{fromCodePoint:function(t){for(var e,n=[],r=arguments.length,i=0;r>i;){if(e=+arguments[i++],o(e,1114111)!==e)throw new a(e+" is not a valid code point");n[i]=e<65536?s(e):s(55296+((e-=65536)>>10),e%1024+56320)}return c(n,"")}})},7347:function(t,e,n){"use strict";var r=n(3724),i=n(9565),o=n(8773),a=n(6980),s=n(5397),u=n(6969),c=n(9297),l=n(5917),f=Object.getOwnPropertyDescriptor;e.f=r?f:function(t,e){if(t=s(t),e=u(e),l)try{return f(t,e)}catch(t){}if(c(t,e))return a(!i(o.f,t,e),t[e])}},7400:function(t){"use strict";t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},7411:function(t,e,n){"use strict";var r=n(6518),i=n(3724),o=n(8551),a=n(6969),s=n(4913);r({target:"Reflect",stat:!0,forced:n(9039)((function(){Reflect.defineProperty(s.f({},1,{value:1}),1,{value:2})})),sham:!i},{defineProperty:function(t,e,n){o(t);var r=a(e);o(n);try{return s.f(t,r,n),!0}catch(t){return!1}}})},7416:function(t,e,n){"use strict";var r=n(9039),i=n(8227),o=n(3724),a=n(6395),s=i("iterator");t.exports=!r((function(){var t=new URL("b?a=1&b=2&c=3","https://a"),e=t.searchParams,n=new URLSearchParams("a=1&a=2&b=3"),r="";return t.pathname="c%20d",e.forEach((function(t,n){e.delete("b"),r+=n+t})),n.delete("a",2),n.delete("b",void 0),a&&(!t.toJSON||!n.has("a",1)||n.has("a",2)||!n.has("a",void 0)||n.has("b"))||!e.size&&(a||!o)||!e.sort||"https://a/c%20d?a=1&c=3"!==t.href||"3"!==e.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!e[s]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==r||"x"!==new URL("https://x",void 0).host}))},7427:function(t,e,n){"use strict";var r=n(6518),i=n(3724),o=n(2551),a=n(9306),s=n(8981),u=n(4913);i&&r({target:"Object",proto:!0,forced:o},{__defineGetter__:function(t,e){u.f(s(this),t,{get:a(e),enumerable:!0,configurable:!0})}})},7433:function(t,e,n){"use strict";var r=n(4376),i=n(3517),o=n(34),a=n(8227)("species"),s=Array;t.exports=function(t){var e;return r(t)&&(e=t.constructor,(i(e)&&(e===s||r(e.prototype))||o(e)&&null===(e=e[a]))&&(e=void 0)),void 0===e?s:e}},7452:function(t){"use strict";t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},7476:function(t,e,n){"use strict";var r=n(2195),i=n(9504);t.exports=function(t){if("Function"===r(t))return i(t)}},7478:function(t,e,n){"use strict";n(7633)("Array")},7495:function(t,e,n){"use strict";var r=n(6518),i=n(7323);r({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},7541:function(t,e,n){"use strict";n.r(e),n.d(e,{Paginator:function(){return wt}});var r=n(6822),i=n(3954),o=n(5501),a=n(8614),s=n(4467),u=n(467),c=n(3029),l=n(2901),f=n(296),h=n(4756),d=n.n(h);function p(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(p=function(){return!!t})()}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function g(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function y(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?g(Object(n),!0).forEach((function(e){(0,s.A)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function b(t,e,n){m(t,e),e.set(t,n)}function m(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function x(t,e,n){return t.set(k(t,e),n),n}function w(t,e){return t.get(k(t,e))}function k(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}var A=function(t){return new Promise((function(e){return setTimeout(e,t)}))},S=function(t,e,n,r,i){return new Promise((function(o){var a,s=function(u){null!=a||(a=u);var c,l,f=Math.min(1,(u-a)/n);i((c=t,l=e,r(f)*(l-c)+c)),f<1?requestAnimationFrame(s):o()};requestAnimationFrame(s)}))},E=function(t){if(null==t||!t.collapsed)return t;var e=t.endOffset,n=t.endContainer;if(1===n.nodeType)return n;if(e+1<n.length)t.setEnd(n,e+1);else{if(!(e>1))return n.parentNode;t.setStart(n,e-1)}return t},_=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n,i=t.createRange();return i.setStart(e,n),i.setEnd(e,r),i},T=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:e.nodeValue.length;if(i-r==1)return n(_(t,e,r),_(t,e,i))<0?r:i;var o=Math.floor(r+(i-r)/2),a=n(_(t,e,r,o),_(t,e,o,i));return a<0?T(t,e,n,r,o):a>0?T(t,e,n,o,i):o},O=NodeFilter,M=O.SHOW_ELEMENT,L=O.SHOW_TEXT,C=O.SHOW_CDATA_SECTION,I=O.FILTER_ACCEPT,R=O.FILTER_REJECT,j=O.FILTER_SKIP,P=M|L|C,F=function(t,e,n,r){var i,o;if(t){for(var a=t.createTreeWalker(t.body,P,{acceptNode:function(i){var o,a=null===(o=i.localName)||void 0===o?void 0:o.toLowerCase();if("script"===a||"style"===a)return R;if(1===i.nodeType){var s=r(i.getBoundingClientRect()),u=s.left,c=s.right;if(c<e||u>n)return R;if(u>=e&&c<=n)return I}else{var l;if(null===(l=i.nodeValue)||void 0===l||!l.trim())return R;var f=t.createRange();f.selectNodeContents(i);var h=r(f.getBoundingClientRect()),d=h.left;if(h.right>=e&&d<=n)return I}return j}}),s=[],u=a.nextNode();u;u=a.nextNode())s.push(u);var c=null!==(i=s[0])&&void 0!==i?i:t.body,l=null!==(o=s[s.length-1])&&void 0!==o?o:c,f=1===c.nodeType?0:T(t,c,(function(t,n){var i=r(t.getBoundingClientRect()),o=r(n.getBoundingClientRect());return i.right<e&&o.left>e?0:o.left>e?-1:1})),h=1===l.nodeType?0:T(t,l,(function(t,e){var i=r(t.getBoundingClientRect()),o=r(e.getBoundingClientRect());return i.right<n&&o.left>n?0:o.left>n?-1:1})),d=t.createRange();return d.setStart(c,f),d.setEnd(l,h),d}},N=function(t){var e=t.defaultView.getComputedStyle(t.body),n=e.writingMode,r=e.direction;return{vertical:"vertical-rl"===n||"vertical-lr"===n,rtl:"rtl"===t.body.dir||"rtl"===r||"rtl"===t.documentElement.dir}},D=function(t){return"none"===t?"none":"url(".concat(t,") repeat scroll 50% 50% / 100% 100%")},z=function(t,e){return Array.from({length:t},(function(){var t=document.createElement("div"),n=document.createElement("div");return t.append(n),n.setAttribute("part",e),t}))},B=function(t,e){for(var n=t.style,r=0,i=Object.entries(e);r<i.length;r++){var o=(0,f.A)(i[r],2),a=o[0],s=o[1];n.setProperty(a,s,"important")}},U=new WeakMap,W=new WeakMap,H=new WeakMap,q=new WeakMap,V=new WeakMap,$=new WeakMap,G=new WeakMap,X=new WeakMap,Y=new WeakMap,K=new WeakMap,J=function(){return(0,l.A)((function t(e){var n=this,r=e.container,i=e.onExpand;(0,c.A)(this,t),b(this,U,new ResizeObserver((function(){return n.expand()}))),b(this,W,document.createElement("div")),b(this,H,document.createElement("iframe")),b(this,q,document.createRange()),b(this,V,void 0),b(this,$,!1),b(this,G,!1),b(this,X,!0),b(this,Y,void 0),b(this,K,{}),this.container=r,this.onExpand=i,w(H,this).setAttribute("part","filter"),w(W,this).append(w(H,this)),Object.assign(w(W,this).style,{boxSizing:"content-box",position:"relative",overflow:"hidden",flex:"0 0 auto",width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center"}),Object.assign(w(H,this).style,{overflow:"hidden",border:"0",display:"none",width:"100%",height:"100%"}),w(H,this).setAttribute("sandbox","allow-same-origin allow-scripts"),w(H,this).setAttribute("scrolling","no")}),[{key:"element",get:function(){return w(W,this)}},{key:"document",get:function(){return w(H,this).contentDocument}},{key:"load",value:(t=(0,u.A)(d().mark((function t(e,n,r){var i=this;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("string"==typeof e){t.next=1;break}throw new Error("".concat(e," is not string"));case 1:return t.abrupt("return",new Promise((function(t){w(H,i).addEventListener("load",(function(){var e=i.document;null==n||n(e),w(H,i).style.display="block";var o=N(e),a=o.vertical,s=o.rtl;w(H,i).style.display="none",x($,i,a),x(G,i,s),w(q,i).selectNodeContents(e.body);var u=null==r?void 0:r({vertical:a,rtl:s});w(H,i).style.display="block",i.render(u),w(U,i).observe(e.body),e.fonts.ready.then((function(){return i.expand()})),t()}),{once:!0}),w(H,i).src=e})));case 2:case"end":return t.stop()}}),t)}))),function(e,n,r){return t.apply(this,arguments)})},{key:"render",value:function(t){t&&(x(X,this,"scrolled"!==t.flow),x(K,this,t),w(X,this)?this.columnize(t):this.scrolled(t))}},{key:"scrolled",value:function(t){var e=t.gap,n=t.columnWidth,r=w($,this),i=this.document;i&&(B(i.documentElement,{"box-sizing":"border-box",padding:r?"".concat(e,"px 0"):"0 ".concat(e,"px"),"column-width":"auto",height:"auto",width:"auto"}),B(i.body,(0,s.A)((0,s.A)({},r?"max-height":"max-width","".concat(n,"px")),"margin","auto")),this.setImageSize(),this.expand())}},{key:"columnize",value:function(t){var e=t.width,n=t.height,r=t.gap,i=t.columnWidth,o=t.topMargin,a=t.bottomMargin,s=w($,this);x(Y,this,s?n:e);var u=this.document,c="".concat(r/2,"px ").concat(o,"px ").concat(r/2,"px ").concat(a,"px"),l="".concat(o,"px ").concat(r/2,"px ").concat(a,"px ").concat(r/2,"px");B(u.documentElement,y(y({"box-sizing":"border-box","column-width":"".concat(Math.trunc(i),"px"),"column-gap":"".concat(r,"px"),"column-fill":"auto"},s?{width:"".concat(e,"px")}:{height:"".concat(n,"px")}),{},{padding:s?c:l,overflow:"hidden","overflow-wrap":"break-word",position:"static",border:"0",margin:"0","max-height":"none","max-width":"none","min-height":"none","min-width":"none","-webkit-line-box-contain":"block glyphs replaced"})),B(u.body,{"max-height":"none","max-width":"none",margin:"0"}),this.setImageSize(),this.expand()}},{key:"setImageSize",value:function(){var t,e=w(K,this),n=e.width,r=e.height,i=e.margin,o=w($,this),a=this.document,s=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return v(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?v(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}(a.body.querySelectorAll("img, svg, video"));try{for(s.s();!(t=s.n()).done;){var u=t.value,c=a.defaultView.getComputedStyle(u),l=c.maxHeight,f=c.maxWidth;B(u,{"max-height":o?"none"!==l&&"0px"!==l?l:"100%":"".concat(r-2*i,"px"),"max-width":o?"".concat(n-2*i,"px"):"none"!==f&&"0px"!==f?f:"100%","object-fit":"contain","page-break-inside":"avoid","break-inside":"avoid","box-sizing":"border-box"})}}catch(t){s.e(t)}finally{s.f()}}},{key:"expand",value:function(){var t=this.document.documentElement;if(w(X,this)){var e=w($,this)?"height":"width",n=w($,this)?"width":"height";w(q,this).selectNodeContents(this.document.body);var r=w(q,this).getBoundingClientRect(),i=t.getBoundingClientRect(),o=(w($,this)?0:w(G,this)?i.right-r.right:r.left-i.left)+r[e],a=Math.ceil(o/w(Y,this))*w(Y,this);w(W,this).style.padding="0",w(H,this).style[e]="".concat(a,"px"),w(W,this).style[e]="".concat(a+2*w(Y,this),"px"),w(H,this).style[n]="100%",w(W,this).style[n]="100%",t.style[e]="".concat(w(Y,this),"px"),w(V,this)&&(w(V,this).element.style.margin="0",w(V,this).element.style.left=w($,this)?"0":"".concat(w(Y,this),"px"),w(V,this).element.style.top=w($,this)?"".concat(w(Y,this),"px"):"0",w(V,this).element.style[e]="".concat(a,"px"),w(V,this).redraw())}else{var s=w($,this)?"width":"height",u=w($,this)?"height":"width",c=t.getBoundingClientRect()[s],l=w(K,this).margin,f=w($,this)?"0 ".concat(l,"px"):"".concat(l,"px 0");w(W,this).style.padding=f,w(H,this).style[s]="".concat(c,"px"),w(W,this).style[s]="".concat(c,"px"),w(H,this).style[u]="100%",w(W,this).style[u]="100%",w(V,this)&&(w(V,this).element.style.margin=f,w(V,this).element.style.left="0",w(V,this).element.style.top="0",w(V,this).element.style[s]="".concat(c,"px"),w(V,this).redraw())}this.onExpand()}},{key:"overlayer",get:function(){return w(V,this)},set:function(t){x(V,this,t),w(W,this).append(t.element)}},{key:"destroy",value:function(){this.document&&w(U,this).unobserve(this.document.body)}}]);var t}(),Z=new WeakMap,Q=new WeakMap,tt=new WeakMap,et=new WeakMap,nt=new WeakMap,rt=new WeakMap,it=new WeakMap,ot=new WeakMap,at=new WeakMap,st=new WeakMap,ut=new WeakMap,ct=new WeakMap,lt=new WeakMap,ft=new WeakMap,ht=new WeakMap,dt=new WeakMap,pt=new WeakMap,vt=new WeakMap,gt=new WeakMap,yt=new WeakMap,bt=new WeakMap,mt=new WeakMap,xt=new WeakSet,wt=function(t){function e(){var t,n,o,a,s,u;(0,c.A)(this,e),n=this,o=e,o=(0,i.A)(o),function(t,e){m(t,e),e.add(t)}(t=(0,r.A)(n,p()?Reflect.construct(o,[],(0,i.A)(n).constructor):o.apply(n,a)),xt),b(t,Z,t.attachShadow({mode:"open"})),b(t,Q,new ResizeObserver((function(){return t.render()}))),b(t,tt,void 0),b(t,et,void 0),b(t,nt,void 0),b(t,rt,void 0),b(t,it,!1),b(t,ot,!1),b(t,at,0),b(t,st,-1),b(t,ut,0),b(t,ct,!1),b(t,lt,!1),b(t,ft,void 0),b(t,ht,new WeakMap),b(t,dt,matchMedia("(prefers-color-scheme: dark)")),b(t,pt,void 0),b(t,vt,void 0),b(t,gt,void 0),b(t,yt,void 0),b(t,bt,!1),b(t,mt,!1),w(Z,t).innerHTML='<style>\n        :host {\n            display: block;\n            container-type: size;\n        }\n        :host, #top {\n            box-sizing: border-box;\n            position: relative;\n            overflow: hidden;\n            width: 100%;\n            height: 100%;\n        }\n        #top {\n            height: 100%;\n            // --_gap: 7%;\n            background-color: var(--_background-color);\n            --_max-inline-size: 720px;\n            --_max-block-size: 1440px;\n            --_max-column-count: 2;\n            --_max-column-count-portrait: 1;\n            --_max-column-count-spread: var(--_max-column-count);\n            --_half-gap: calc(var(--_gap) / 2);\n            --_max-width: calc(var(--_max-inline-size) * var(--_max-column-count-spread));\n            --_max-height: var(--_max-block-size);\n            display: grid;\n            grid-template-columns:\n                minmax(var(--_half-gap), 1fr)\n                var(--_half-gap)\n                minmax(0, calc(var(--_max-width) - var(--_gap)))\n                var(--_half-gap)\n                minmax(var(--_half-gap), 1fr);\n            grid-template-rows:\n                var(--_top-margin)\n                1fr\n                var(--_bottom-margin);\n            &.vertical {\n                --_max-column-count-spread: var(--_max-column-count-portrait);\n                --_max-width: var(--_max-block-size);\n                --_max-height: calc(var(--_max-inline-size) * var(--_max-column-count-spread));\n            }\n            @container (orientation: portrait) {\n                & {\n                    --_max-column-count-spread: var(--_max-column-count-portrait);\n                }\n                &.vertical {\n                    --_max-column-count-spread: var(--_max-column-count);\n                }\n            }\n        }\n        #background {\n            grid-column: 1 / -1;\n            grid-row: 1 / -1;\n        }\n        #container {\n            grid-column: 1 / -1;\n            grid-row: 1 / -1;\n            overflow: hidden;\n            -ms-overflow-style: none;  /* Internet Explorer 10+ */\n            scrollbar-width: none;  /* Firefox */\n        }\n        #container::-webkit-scrollbar { \n            display: none;  /* Safari and Chrome */\n        }\n        :host([flow="scrolled"]) #container {\n            grid-column: 1 / -1;\n            grid-row: 2;\n            overflow: auto;\n        }\n        #header {\n            grid-column: 3 / 4;\n            grid-row: 1;\n        }\n        #footer {\n            grid-column: 3 / 4;\n            grid-row: 3;\n            align-self: end;\n        }\n        #header, #footer {\n            display: grid;\n            height: var(--_margin);\n        }\n        :is(#header, #footer) > * {\n            display: flex;\n            align-items: center;\n            min-width: 0;\n        }\n        :is(#header, #footer) > * > * {\n            width: 100%;\n            overflow: hidden;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            text-align: center;\n            font-size: .75em;\n            opacity: .6;\n        }\n        </style>\n        <div id="top">\n            <div id="background" part="filter"></div>\n            <div id="container"></div>\n        </div>\n        ',x(tt,t,w(Z,t).getElementById("top")),x(et,t,w(Z,t).getElementById("background")),x(nt,t,w(Z,t).getElementById("container")),w(Q,t).observe(w(nt,t)),w(nt,t).addEventListener("scroll",(s=function(){console.log("scrolled",t.scrolled,w(ct,t)),t.scrolled&&(w(ct,t)?x(ct,t,!1):(k(xt,t,Ft).call(t,"scroll"),k(xt,t,Nt).call(t)))},function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];u&&clearTimeout(u),u=setTimeout((function(){u=null,s.apply(void 0,e)}),50)}));var l={passive:!1};return t.addEventListener("touchstart",k(xt,t,St).bind(t),l),t.addEventListener("touchmove",k(xt,t,Et).bind(t),l),t.addEventListener("touchend",k(xt,t,_t).bind(t),l),t.addEventListener("load",(function(e){var n=e.detail.doc;n.addEventListener("touchstart",k(xt,t,St).bind(t),l),n.addEventListener("touchmove",k(xt,t,Et).bind(t),l),n.addEventListener("touchend",k(xt,t,_t).bind(t),l)})),x(pt,t,(function(){w(rt,t)&&(w(et,t).style.background=D(t.getAttribute("bgimg-url")))})),w(dt,t).addEventListener("change",w(pt,t)),t}return(0,o.A)(e,t),(0,l.A)(e,[{key:"attributeChangedCallback",value:function(t,e,n){switch(t){case"flow":this.render();break;case"top-margin":case"max-block-size":case"background-color":w(tt,this).style.setProperty("--_"+t,n);break;case"bottom-margin":case"gap":case"max-column-count":case"max-inline-size":w(tt,this).style.setProperty("--_"+t,n),this.render()}}},{key:"open",value:function(t){this.bookDir=t.dir,this.sections=t.sections}},{key:"render",value:function(){w(rt,this)&&(w(rt,this).render(k(xt,this,At).call(this,{vertical:w(it,this),rtl:w(ot,this)})),this.scrollToAnchor(w(ut,this)))}},{key:"scrolled",get:function(){return"scrolled"===this.getAttribute("flow")}},{key:"scrollProp",get:function(){var t=this.scrolled;return w(it,this)?t?"scrollLeft":"scrollTop":t?"scrollTop":"scrollLeft"}},{key:"sideProp",get:function(){var t=this.scrolled;return w(it,this)?t?"width":"height":t?"height":"width"}},{key:"vertical",get:function(){return w(it,this)}},{key:"size",get:function(){return w(nt,this).getBoundingClientRect()[this.sideProp]}},{key:"viewSize",get:function(){return w(rt,this).element.getBoundingClientRect()[this.sideProp]}},{key:"start",get:function(){return Math.abs(w(nt,this)[this.scrollProp])}},{key:"end",get:function(){return this.start+this.size}},{key:"page",get:function(){return Math.floor((this.start+this.end)/2/this.size)}},{key:"pages",get:function(){return Math.round(this.viewSize/this.size)}},{key:"scrollBy",value:function(t,e){var n=w(it,this)?e:t,r=w(nt,this),i=this.scrollProp,o=w(vt,this),a=(0,f.A)(o,3),s=a[0],u=a[1],c=a[2],l=w(ot,this),h=l?s-c:s-u,d=l?s+u:s+c;r[i]=Math.max(h,Math.min(d,r[i]+n))}},{key:"snap",value:function(t,e){var n=this,r=w(it,this)?e:t,i=w(vt,this),o=(0,f.A)(i,3),a=o[0],s=o[1],u=o[2],c=this.start,l=this.end,h=this.pages,d=this.size,p=Math.abs(a)-s,v=Math.abs(a)+u,g=r*(w(ot,this)?-d:d),y=Math.floor(Math.max(p,Math.min(v,(c+l)/2+(isNaN(g)?0:g)))/d);k(xt,this,It).call(this,y,"snap").then((function(){var t=y<=0?-1:y>=h-1?1:null;if(t)return k(xt,n,Ut).call(n,{index:k(xt,n,Vt).call(n,t),anchor:t<0?function(){return 1}:function(){return 0}})}))}},{key:"scrollToAnchor",value:(a=(0,u.A)(d().mark((function t(e,n){var r,i,o,a,s,u,c;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(x(ut,this,e),!(o=null===(r=E(e))||void 0===r||null===(i=r.getClientRects)||void 0===i?void 0:i.call(r))){t.next=3;break}if(a=Array.from(o).find((function(t){return t.width>0&&t.height>0}))||o[0]){t.next=1;break}return t.abrupt("return");case 1:return t.next=2,k(xt,this,Ot).call(this,a,"anchor");case 2:return n&&k(xt,this,jt).call(this),t.abrupt("return");case 3:if(!this.scrolled){t.next=5;break}return t.next=4,k(xt,this,Lt).call(this,e*this.viewSize,"anchor");case 4:return t.abrupt("return");case 5:if(s=this.pages){t.next=6;break}return t.abrupt("return");case 6:return u=s-2,c=Math.round(e*(u-1)),t.next=7,k(xt,this,It).call(this,c+1,"anchor");case 7:case"end":return t.stop()}}),t,this)}))),function(t,e){return a.apply(this,arguments)})},{key:"goTo",value:(n=(0,u.A)(d().mark((function t(e){var n;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!w(lt,this)){t.next=1;break}return t.abrupt("return");case 1:return t.next=2,e;case 2:if(n=t.sent,!k(xt,this,Bt).call(this,n.index)){t.next=3;break}return t.abrupt("return",k(xt,this,Ut).call(this,n));case 3:case"end":return t.stop()}}),t,this)}))),function(t){return n.apply(this,arguments)})},{key:"atStart",get:function(){return null==k(xt,this,Vt).call(this,-1)&&this.page<=1}},{key:"atEnd",get:function(){return null==k(xt,this,Vt).call(this,1)&&this.page>=this.pages-2}},{key:"prev",value:function(t){return k(xt,this,$t).call(this,-1,t)}},{key:"next",value:function(t){return k(xt,this,$t).call(this,1,t)}},{key:"prevSection",value:function(){return this.goTo({index:k(xt,this,Vt).call(this,-1)})}},{key:"nextSection",value:function(){return this.goTo({index:k(xt,this,Vt).call(this,1)})}},{key:"firstSection",value:function(){var t=this.sections.findIndex((function(t){return"no"!==t.linear}));return this.goTo({index:t})}},{key:"lastSection",value:function(){var t=this.sections.findLastIndex((function(t){return"no"!==t.linear}));return this.goTo({index:t})}},{key:"getContents",value:function(){return w(rt,this)?[{index:w(st,this),overlayer:w(rt,this).overlayer,doc:w(rt,this).document}]:[]}},{key:"setStyles",value:function(t){var e,n,r=this;x(ft,this,t);var i=w(ht,this).get(null===(e=w(rt,this))||void 0===e?void 0:e.document);if(i){var o=(0,f.A)(i,2),a=o[0],s=o[1];if(Array.isArray(t)){var u=(0,f.A)(t,2),c=u[0],l=u[1];a.textContent=c,s.textContent=l}else s.textContent=t;w(et,this).style.background=D(this.getAttribute("bgimg-url")),null===(n=w(rt,this))||void 0===n||null===(n=n.document)||void 0===n||null===(n=n.fonts)||void 0===n||null===(n=n.ready)||void 0===n||n.then((function(){return w(rt,r).expand()}))}}},{key:"destroy",value:function(){var t,e;w(Q,this).unobserve(this),w(rt,this).destroy(),x(rt,this,null),null===(t=this.sections[w(st,this)])||void 0===t||null===(e=t.unload)||void 0===e||e.call(t),w(dt,this).removeEventListener("change",w(pt,this))}}]);var n,a}((0,a.A)(HTMLElement));function kt(){var t=this;return w(rt,this)&&(w(rt,this).destroy(),w(nt,this).removeChild(w(rt,this).element)),x(rt,this,new J({container:this,onExpand:function(){return t.scrollToAnchor(w(ut,t))}})),w(nt,this).append(w(rt,this).element),w(rt,this)}function At(t){var e=t.vertical,n=t.rtl;x(it,this,e),x(ot,this,n),w(tt,this).classList.toggle("vertical",e),w(et,this).style.background=D(this.getAttribute("bgimg-url"));var r=w(nt,this).getBoundingClientRect(),i=r.width,o=r.height,a=e?o:i,s=getComputedStyle(w(tt,this)),u=parseFloat(s.getPropertyValue("--_max-inline-size")),c=parseInt(s.getPropertyValue("--_max-column-count")),l=parseFloat(s.getPropertyValue("--_top-margin"));x(at,this,l);var f=parseFloat(s.getPropertyValue("--_gap"))/100,h=-f/(f-1)*a,d=parseFloat(s.getPropertyValue("--_top-margin")),p=parseFloat(s.getPropertyValue("--_bottom-margin")),v=this.getAttribute("flow");if("scrolled"===v){this.setAttribute("dir",e?"rtl":"ltr"),w(tt,this).style.padding="0";var g=u;return this.heads=null,this.feet=null,{flow:v,margin:l,gap:h,columnWidth:g,topMargin:d,bottomMargin:p}}var y=0==c?Math.min(2,Math.ceil(a/u)):c,b=a/y-h;this.setAttribute("dir",n?"rtl":"ltr");var m=e?Math.min(2,Math.ceil(i/u)):y,k=("repeat(".concat(m,", 1fr)"),"".concat(h,"px"),this.bookDir,z(m,"head")),A=z(m,"foot");return this.heads=k.map((function(t){return t.children[0]})),this.feet=A.map((function(t){return t.children[0]})),{height:o,width:i,margin:l,gap:h,columnWidth:b,topMargin:d,bottomMargin:p}}function St(t){var e=t.changedTouches[0];x(gt,this,{x:null==e?void 0:e.screenX,y:null==e?void 0:e.screenY,t:t.timeStamp,vx:0,xy:0,direction:"none",startTouch:{x:t.touches[0].screenX,y:t.touches[0].screenY},delta:{x:0,deltaY:0}}),this.dispatchEvent(new CustomEvent("doctouchstart",{detail:{touch:t.changedTouches[0],touchState:w(gt,this)},bubbles:!0,composed:!0}))}function Et(t){var e,n=this;if(null===(e=window.getSelection())||void 0===e||!e.toString()){var r=t.changedTouches[0].screenX-w(gt,this).startTouch.x,i=t.changedTouches[0].screenY-w(gt,this).startTouch.y,o=Math.abs(r),a=Math.abs(i);w(gt,this).delta.x=r,w(gt,this).delta.y=i;var s="horizontal"===w(gt,this).direction&&a>o,u="vertical"===w(gt,this).direction&&o>a;if(!(("none"!==w(gt,this).direction||s&&u)&&o<5&&a<5)){(o>5||a>5)&&"none"===w(gt,this).direction&&(w(gt,this).direction=o>a?"horizontal":"vertical");var c="horizontal"===w(gt,this).direction&&"scrollLeft"===this.scrollProp,l="vertical"===w(gt,this).direction&&"scrollTop"===this.scrollProp;if(c||l){var f=w(gt,this);f&&(f.pinched||(f.pinched=globalThis.visualViewport.scale>1,this.scrolled||f.pinched||(t.touches.length>1?w(yt,this)&&t.preventDefault():(t.preventDefault(),f.animationFrameId||(f.animationFrameId=requestAnimationFrame((function(){var e=t.changedTouches[0],r=e.screenX,i=e.screenY,o=f.x-r,a=f.y-i,s=t.timeStamp-f.t||16.7,u=.85*o,c=.85*a;f.x=r,f.y=i,f.t=t.timeStamp,f.vx=o/s,f.vy=a/s,x(yt,n,!0),n.scrollBy(u,c),f.animationFrameId=null})))))))}else this.dispatchEvent(new CustomEvent("doctouchmove",{detail:{touch:t.changedTouches[0],touchState:w(gt,this)},preventDefault:function(){return t.preventDefault()},bubbles:!0,composed:!0}))}}}function _t(t){var e=this;this.dispatchEvent(new CustomEvent("doctouchend",{detail:{touch:t.changedTouches[0],touchState:w(gt,this)},bubbles:!0,composed:!0})),x(yt,this,!1),this.scrolled||requestAnimationFrame((function(){1===globalThis.visualViewport.scale&&e.snap(w(gt,e).vx,w(gt,e).vy)}))}function Tt(){if(this.scrolled){var t=this.viewSize,e=w(at,this);return w(it,this)?function(n){var r=n.left,i=n.right;return{left:t-i-e,right:t-r-e}}:function(t){var n=t.top,r=t.bottom;return{left:n+e,right:r+e}}}var n=this.pages*this.size;return w(ot,this)?function(t){var e=t.left,r=t.right;return{left:n-r,right:n-e}}:w(it,this)?function(t){return{left:t.top,right:t.bottom}}:function(t){return t}}function Ot(t,e){return Mt.apply(this,arguments)}function Mt(){return(Mt=(0,u.A)(d().mark((function t(e,n){var r,i;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.scrolled){t.next=1;break}return r=k(xt,this,Tt).call(this)(e).left-w(at,this),t.abrupt("return",k(xt,this,Lt).call(this,r,n));case 1:return i=k(xt,this,Tt).call(this)(e).left+w(at,this)/2,t.abrupt("return",k(xt,this,It).call(this,Math.floor(i/this.size)+(w(ot,this)?-1:1),n));case 2:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}function Lt(t,e,n){return Ct.apply(this,arguments)}function Ct(){return(Ct=(0,u.A)(d().mark((function t(e,n,r){var i,o,a,s,u,c,l=this;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=w(nt,this),o=this.scrollProp,a=this.size,!(Math.abs(i[o]-e)<1)){t.next=1;break}return x(vt,this,[e,this.atStart?0:a,this.atEnd?0:a]),k(xt,this,Ft).call(this,n),t.abrupt("return");case 1:if(this.scrolled&&w(it,this)&&(e=-e),"snap"!==n&&!r||!this.hasAttribute("animated")){t.next=2;break}return s=Math.abs(i[o]-e),u=Math.min(500,Math.max(200,s/a*300)),c=function(t){return 1-Math.pow(1-t,3)},x(ct,this,!0),t.abrupt("return",S(i[o],e,u,c,(function(t){return i[o]=t})).then((function(){return i[o]=e,x(vt,l,[e,l.atStart?0:a,l.atEnd?0:a]),A(10).then((function(){k(xt,l,Ft).call(l,n)}))})));case 2:i[o]=e,x(vt,this,[e,this.atStart?0:a,this.atEnd?0:a]),k(xt,this,Ft).call(this,n);case 3:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}function It(t,e,n){return Rt.apply(this,arguments)}function Rt(){return(Rt=(0,u.A)(d().mark((function t(e,n,r){var i;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=this.size*(w(ot,this)?-e:e),t.abrupt("return",k(xt,this,Lt).call(this,i,n,r));case 1:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}function jt(){var t=w(rt,this).document.defaultView;if(w(ut,this).startContainer){var e=t.getSelection();e.removeAllRanges(),e.addRange(w(ut,this))}}function Pt(){if(this.scrolled)return F(w(rt,this).document,this.start+w(at,this),this.end-w(at,this),k(xt,this,Tt).call(this));var t=w(ot,this)?-this.size:this.size;return F(w(rt,this).document,this.start-t,this.end-t,k(xt,this,Tt).call(this))}function Ft(t){var e=k(xt,this,Pt).call(this);"anchor"!==t?x(ut,this,e):x(ct,this,!0);var n={reason:t,range:e,index:w(st,this)};if(this.scrolled)n.fraction=this.start/this.viewSize;else if(this.pages>0){var r=this.page,i=this.pages;n.fraction=(r-1)/(i-2),n.size=1/(i-2)}this.dispatchEvent(new CustomEvent("relocate",{detail:n}))}function Nt(){var t=this;if(this.scrolled&&!w(lt,this)){var e=Math.min(50,.05*this.size),n=this.viewSize-this.end<=e,r=this.start<=e;if(n&&!w(bt,this)){var i=k(xt,this,Vt).call(this,1);null!=i&&(x(bt,this,!0),setTimeout((function(){k(xt,t,Ut).call(t,{index:i,anchor:function(){return 0}}).then((function(){x(bt,t,!1)})).catch((function(){x(bt,t,!1)}))}),200))}if(r&&!w(mt,this)){var o=k(xt,this,Vt).call(this,-1);null!=o&&(x(mt,this,!0),setTimeout((function(){k(xt,t,Ut).call(t,{index:o,anchor:function(){return 1}}).then((function(){x(mt,t,!1)})).catch((function(){x(mt,t,!1)}))}),200))}}}function Dt(t){return zt.apply(this,arguments)}function zt(){return(zt=(0,u.A)(d().mark((function t(e){var n,r,i,o,a,s,u,c,l,f,h=this;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,e;case 1:if(r=t.sent,i=r.index,o=r.src,a=r.anchor,s=r.onLoad,u=r.select,x(st,this,i),!o){t.next=3;break}return c=k(xt,this,kt).call(this),l=function(t){if(t.head){var e=t.createElement("style");t.head.prepend(e);var n=t.createElement("style");t.head.append(n),w(ht,h).set(t,[e,n])}null==s||s({doc:t,index:i})},f=k(xt,this,At).bind(this),t.next=2,c.load(o,l,f);case 2:this.dispatchEvent(new CustomEvent("create-overlayer",{detail:{doc:c.document,index:i,attach:function(t){return c.overlayer=t}}})),x(rt,this,c);case 3:return t.next=4,this.scrollToAnchor(null!==(n="function"==typeof a?a(w(rt,this).document):a)&&void 0!==n?n:0,u);case 4:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}function Bt(t){return t>=0&&t<=this.sections.length-1}function Ut(t){return Wt.apply(this,arguments)}function Wt(){return(Wt=(0,u.A)(d().mark((function t(e){var n,r,i,o,a,s=this;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.index,r=e.anchor,i=e.select,n!==w(st,this)){t.next=2;break}return t.next=1,k(xt,this,Dt).call(this,{index:n,anchor:r,select:i});case 1:t.next=3;break;case 2:return o=w(st,this),a=function(t){var e,n;null===(e=s.sections[o])||void 0===e||null===(n=e.unload)||void 0===n||n.call(e),s.setStyles(w(ft,s)),s.dispatchEvent(new CustomEvent("load",{detail:t}))},t.next=3,k(xt,this,Dt).call(this,Promise.resolve(this.sections[n].load()).then((function(t){return{index:n,src:t,anchor:r,onLoad:a,select:i}})).catch((function(t){return console.warn(t),console.warn(new Error("Failed to load section ".concat(n))),{}})));case 3:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}function Ht(t){if(!w(rt,this))return!0;if(this.scrolled)return!(this.start>0)||k(xt,this,Lt).call(this,Math.max(0,this.start-(null!=t?t:this.size)),null,!0);if(!this.atStart){var e=this.page-1;return k(xt,this,It).call(this,e,"page",!0).then((function(){return e<=0}))}}function qt(t){if(!w(rt,this))return!0;if(this.scrolled)return!(this.viewSize-this.end>2)||k(xt,this,Lt).call(this,Math.min(this.viewSize,t?this.start+t:this.end),null,!0);if(!this.atEnd){var e=this.page+1,n=this.pages;return k(xt,this,It).call(this,e,"page",!0).then((function(){return e>=n-1}))}}function Vt(t){for(var e=w(st,this)+t;k(xt,this,Bt).call(this,e);e+=t){var n;if("no"!==(null===(n=this.sections[e])||void 0===n?void 0:n.linear))return e}}function $t(t,e){return Gt.apply(this,arguments)}function Gt(){return(Gt=(0,u.A)(d().mark((function t(e,n){var r,i;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return x(lt,this,!0),r=-1===e,console.log("Turning page ".concat(r?"back":"forward","..."),n),t.next=1,r?k(xt,this,Ht).call(this,n):k(xt,this,qt).call(this,n);case 1:if(!(i=t.sent)){t.next=2;break}return t.next=2,k(xt,this,Ut).call(this,{index:k(xt,this,Vt).call(this,e),anchor:r?function(){return 1}:function(){return 0}});case 2:if(!i&&this.hasAttribute("animated")){t.next=3;break}return t.next=3,A(100);case 3:x(lt,this,!1);case 4:case"end":return t.stop()}}),t,this)})))).apply(this,arguments)}(0,s.A)(wt,"observedAttributes",["flow","gap","top-margin","bottom-margin","background-color","max-inline-size","max-block-size","max-column-count"]),customElements.define("foliate-paginator",wt)},7607:function(t,e,n){"use strict";var r=n(6518),i=n(3724),o=n(2551),a=n(9306),s=n(8981),u=n(4913);i&&r({target:"Object",proto:!0,forced:o},{__defineSetter__:function(t,e){u.f(s(this),t,{set:a(e),enumerable:!0,configurable:!0})}})},7629:function(t,e,n){"use strict";var r=n(6395),i=n(4576),o=n(9433),a="__core-js_shared__",s=t.exports=i[a]||o(a,{});(s.versions||(s.versions=[])).push({version:"3.43.0",mode:r?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.43.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7631:function(t,e,n){"use strict";n.r(e),n.d(e,{TTS:function(){return E}});var r=n(296),i=n(3029),o=n(2901),a=n(4756),s=n.n(a);function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function c(t,e,n){l(t,e),e.set(t,n)}function l(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function f(t,e){return t.get(d(t,e))}function h(t,e,n){return t.set(d(t,e),n),n}function d(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}var p=s().mark(g);function v(t){return t.collapsed||""===t.toString().trim()}function g(t){var e,n,r,i,o;return s().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:e=t.createTreeWalker(t.body,NodeFilter.SHOW_TEXT),n=null,r=e.nextNode();case 1:if(!r){a.next=7;break}i=void 0,o=/[.!?。！？]["'“”‘’]?/g;case 2:if(null===(i=o.exec(r.textContent))){a.next=4;break}if(t.createRange(),!n){a.next=3;break}if(n.setEnd(r,i.index+i[0].length),v(n)){a.next=3;break}return a.next=3,n;case 3:(n=t.createRange()).setStart(r,i.index+i[0].length),a.next=2;break;case 4:if(!n){a.next=5;break}if(n.setEnd(r,r.textContent.length),v(n)){a.next=5;break}return a.next=5,n;case 5:(n=t.createRange()).setStart(r,r.textContent.length);case 6:r=e.nextNode(),a.next=1;break;case 7:case"end":return a.stop()}}),p)}new Set(["article","aside","audio","blockquote","caption","details","dialog","div","dl","dt","dd","figure","footer","form","figcaption","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","math","nav","ol","p","pre","section","tr"]);var y=new WeakMap,b=new WeakMap,m=new WeakMap,x=new WeakMap,w=function(){return(0,o.A)((function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(t){return t};(0,i.A)(this,t),c(this,y,[]),c(this,b,void 0),c(this,m,-1),c(this,x,void 0),h(b,this,e),h(x,this,n)}),[{key:"current",value:function(){if(f(y,this)[f(m,this)])return f(x,this).call(this,f(y,this)[f(m,this)])}},{key:"first",value:function(){if(f(y,this)[0])return h(m,this,0),f(x,this).call(this,f(y,this)[0])}},{key:"last",value:function(){var t,e=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return u(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?u(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}(f(b,this));try{for(e.s();!(t=e.n()).done;){var n=t.value;f(y,this).push(n)}}catch(t){e.e(t)}finally{e.f()}var r=f(y,this).length-1;if(f(y,this)[r])return h(m,this,r),f(x,this).call(this,f(y,this)[r])}},{key:"prev",value:function(){var t=f(m,this)-1;if(f(y,this)[t])return h(m,this,t),f(x,this).call(this,f(y,this)[t])}},{key:"next",value:function(){var t=f(m,this)+1;if(f(y,this)[t])return h(m,this,t),f(x,this).call(this,f(y,this)[t]);for(;;){var e=f(b,this).next(),n=e.done,r=e.value;if(n)break;if(f(y,this).push(r),f(y,this)[t])return h(m,this,t),f(x,this).call(this,f(y,this)[t])}}},{key:"prepare",value:function(){var t=f(m,this)+1;if(f(y,this)[t])return f(x,this).call(this,f(y,this)[t]);for(;;){var e=f(b,this).next(),n=e.done,r=e.value;if(n)break;if(f(y,this).push(r),f(y,this)[t])return f(x,this).call(this,f(y,this)[t])}}},{key:"find",value:function(t){var e=f(y,this).findIndex((function(e){return t(e)}));if(e>-1)return h(m,this,e),f(x,this).call(this,f(y,this)[e]);for(;;){var n=f(b,this).next(),r=n.done,i=n.value;if(r)break;if(f(y,this).push(i),t(i))return h(m,this,f(y,this).length-1),f(x,this).call(this,i)}}}])}(),k=new WeakMap,A=new WeakMap,S=new WeakSet,E=function(){return(0,o.A)((function t(e,n,r){var o;(0,i.A)(this,t),l(this,o=S),o.add(this),c(this,k,void 0),c(this,A,void 0),this.doc=e,this.highlight=r,h(k,this,new w(g(e),(function(t){return[t.toString(),t]})))}),[{key:"start",value:function(){var t;h(A,this,null);var e=null!==(t=f(k,this).first())&&void 0!==t?t:[],n=(0,r.A)(e,2),i=n[0],o=n[1];return i?(this.highlight(o.cloneRange()),d(S,this,_).call(this,i)):this.next()}},{key:"end",value:function(){var t;h(A,this,null);var e=null!==(t=f(k,this).last())&&void 0!==t?t:[],n=(0,r.A)(e,2),i=n[0],o=n[1];return i?(this.highlight(o.cloneRange()),d(S,this,_).call(this,i)):this.next()}},{key:"resume",value:function(){var t,e=null!==(t=f(k,this).current())&&void 0!==t?t:[],n=(0,r.A)(e,1)[0];return n?d(S,this,_).call(this,n):this.next()}},{key:"prev",value:function(t){var e;h(A,this,null);var n=null!==(e=f(k,this).prev())&&void 0!==e?e:[],i=(0,r.A)(n,2),o=i[0],a=i[1];return t&&a&&this.highlight(a.cloneRange()),d(S,this,_).call(this,o)}},{key:"next",value:function(t){var e;h(A,this,null);var n=null!==(e=f(k,this).next())&&void 0!==e?e:[],i=(0,r.A)(n,2),o=i[0],a=i[1];return t&&a&&this.highlight(a.cloneRange()),d(S,this,_).call(this,o)}},{key:"prepare",value:function(){var t,e=null!==(t=f(k,this).prepare())&&void 0!==t?t:[],n=(0,r.A)(e,1)[0];return d(S,this,_).call(this,n)}},{key:"from",value:function(t){h(A,this,null);var e=f(k,this).find((function(e){return t.compareBoundaryPoints(Range.END_TO_START,e)<=0})),n=(0,r.A)(e,2),i=n[0],o=n[1];return o&&this.highlight(o.cloneRange()),d(S,this,_).call(this,i)}}])}();function _(t,e){var n;if(!t)return"";if(!e)return t;var r=document.createElement("div");r.innerHTML=t;for(var i=null===(n=e(r))||void 0===n?void 0:n.previousSibling;i;){var o,a,s=null!==(o=i.previousSibling)&&void 0!==o?o:null===(a=i.parentNode)||void 0===a?void 0:a.previousSibling;i.parentNode.removeChild(i),i=s}return r.textContent}},7633:function(t,e,n){"use strict";var r=n(7751),i=n(2106),o=n(8227),a=n(3724),s=o("species");t.exports=function(t){var e=r(t);a&&e&&!e[s]&&i(e,s,{configurable:!0,get:function(){return this}})}},7657:function(t,e,n){"use strict";var r,i,o,a=n(9039),s=n(4901),u=n(34),c=n(2360),l=n(2787),f=n(6840),h=n(8227),d=n(6395),p=h("iterator"),v=!1;[].keys&&("next"in(o=[].keys())?(i=l(l(o)))!==Object.prototype&&(r=i):v=!0),!u(r)||a((function(){var t={};return r[p].call(t)!==t}))?r={}:d&&(r=c(r)),s(r[p])||f(r,p,(function(){return this})),t.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:v}},7680:function(t,e,n){"use strict";var r=n(9504);t.exports=r([].slice)},7696:function(t,e,n){"use strict";var r=n(1291),i=n(8014),o=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=r(t),n=i(e);if(e!==n)throw new o("Wrong length or index");return n}},7740:function(t,e,n){"use strict";var r=n(9297),i=n(5031),o=n(7347),a=n(4913);t.exports=function(t,e,n){for(var s=i(e),u=a.f,c=o.f,l=0;l<s.length;l++){var f=s[l];r(t,f)||n&&r(n,f)||u(t,f,c(e,f))}}},7743:function(t,e,n){"use strict";var r=n(6518),i=n(9565),o=n(9306),a=n(6043),s=n(1103),u=n(2652);r({target:"Promise",stat:!0,forced:n(537)},{race:function(t){var e=this,n=a.f(e),r=n.reject,c=s((function(){var a=o(e.resolve);u(t,(function(t){i(a,e,t).then(n.resolve,r)}))}));return c.error&&r(c.value),n.promise}})},7750:function(t,e,n){"use strict";var r=n(4117),i=TypeError;t.exports=function(t){if(r(t))throw new i("Can't call method on "+t);return t}},7751:function(t,e,n){"use strict";var r=n(4576),i=n(4901);t.exports=function(t,e){return arguments.length<2?(n=r[t],i(n)?n:void 0):r[t]&&r[t][e];var n}},7762:function(t,e,n){"use strict";var r=n(6518),i=n(3250),o=Math.cosh,a=Math.abs,s=Math.E;r({target:"Math",stat:!0,forced:!o||o(710)===1/0},{cosh:function(t){var e=i(a(t)-1)+1;return(e+1/(e*s*s))*(s/2)}})},7764:function(t,e,n){"use strict";var r=n(8183).charAt,i=n(655),o=n(1181),a=n(1088),s=n(2529),u="String Iterator",c=o.set,l=o.getterFor(u);a(String,"String",(function(t){c(this,{type:u,string:i(t),index:0})}),(function(){var t,e=l(this),n=e.string,i=e.index;return i>=n.length?s(void 0,!0):(t=r(n,i),e.index+=t.length,s(t,!1))}))},7782:function(t){"use strict";t.exports=Math.sign||function(t){var e=+t;return 0===e||e!=e?e:e<0?-1:1}},7787:function(t){"use strict";var e=Math.log,n=Math.LN2;t.exports=Math.log2||function(t){return e(t)/n}},7800:function(t,e,n){"use strict";n.d(e,{A:function(){return i}});var r=n(3145);function i(t,e){if(t){if("string"==typeof t)return(0,r.A)(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?(0,r.A)(t,e):void 0}}},7811:function(t){"use strict";t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},7812:function(t,e,n){"use strict";var r=n(6518),i=n(9297),o=n(757),a=n(6823),s=n(5745),u=n(1296),c=s("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!u},{keyFor:function(t){if(!o(t))throw new TypeError(a(t)+" is not a symbol");if(i(c,t))return c[t]}})},7829:function(t,e,n){"use strict";var r=n(8183).charAt;t.exports=function(t,e,n){return e+(n?r(t,e).length:1)}},7860:function(t,e,n){"use strict";var r=n(2839);t.exports=/web0s(?!.*chrome)/i.test(r)},7904:function(t,e,n){"use strict";var r=n(6518),i=n(3724),o=n(2551),a=n(8981),s=n(6969),u=n(2787),c=n(7347).f;i&&r({target:"Object",proto:!0,forced:o},{__lookupSetter__:function(t){var e,n=a(this),r=s(t);do{if(e=c(n,r))return e.set}while(n=u(n))}})},7916:function(t,e,n){"use strict";var r=n(6080),i=n(9565),o=n(8981),a=n(6319),s=n(4209),u=n(3517),c=n(6198),l=n(4659),f=n(81),h=n(851),d=Array;t.exports=function(t){var e=o(t),n=u(this),p=arguments.length,v=p>1?arguments[1]:void 0,g=void 0!==v;g&&(v=r(v,p>2?arguments[2]:void 0));var y,b,m,x,w,k,A=h(e),S=0;if(!A||this===d&&s(A))for(y=c(e),b=n?new this(y):d(y);y>S;S++)k=g?v(e[S],S):e[S],l(b,S,k);else for(b=n?new this:[],w=(x=f(e,A)).next;!(m=i(w,x)).done;S++)k=g?a(x,v,[m.value,S],!0):m.value,l(b,S,k);return b.length=S,b}},7945:function(t,e,n){"use strict";var r=n(6518),i=n(3724),o=n(6801).f;r({target:"Object",stat:!0,forced:Object.defineProperties!==o,sham:!i},{defineProperties:o})},7947:function(t,e,n){"use strict";n(511)("species")},7979:function(t,e,n){"use strict";var r=n(8551);t.exports=function(){var t=r(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},8014:function(t,e,n){"use strict";var r=n(1291),i=Math.min;t.exports=function(t){var e=r(t);return e>0?i(e,9007199254740991):0}},8085:function(t,e,n){"use strict";var r=n(6518),i=Math.floor,o=Math.log,a=Math.LOG2E;r({target:"Math",stat:!0},{clz32:function(t){var e=t>>>0;return e?31-i(o(e+.5)*a):32}})},8125:function(t,e,n){"use strict";var r=n(7751),i=n(511),o=n(687);i("toStringTag"),o(r("Symbol"),"Symbol")},8156:function(t,e,n){"use strict";var r=n(6518),i=n(533).start;r({target:"String",proto:!0,forced:n(3063)},{padStart:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},8183:function(t,e,n){"use strict";var r=n(9504),i=n(1291),o=n(655),a=n(7750),s=r("".charAt),u=r("".charCodeAt),c=r("".slice),l=function(t){return function(e,n){var r,l,f=o(a(e)),h=i(n),d=f.length;return h<0||h>=d?t?"":void 0:(r=u(f,h))<55296||r>56319||h+1===d||(l=u(f,h+1))<56320||l>57343?t?s(f,h):r:t?c(f,h,h+2):l-56320+(r-55296<<10)+65536}};t.exports={codeAt:l(!1),charAt:l(!0)}},8227:function(t,e,n){"use strict";var r=n(4576),i=n(5745),o=n(9297),a=n(3392),s=n(4495),u=n(7040),c=r.Symbol,l=i("wks"),f=u?c.for||c:c&&c.withoutSetter||a;t.exports=function(t){return o(l,t)||(l[t]=s&&o(c,t)?c[t]:f("Symbol."+t)),l[t]}},8229:function(t,e,n){"use strict";var r=n(9590),i=RangeError;t.exports=function(t,e){var n=r(t);if(n%e)throw new i("Wrong offset");return n}},8242:function(t,e,n){"use strict";var r=n(9565),i=n(7751),o=n(8227),a=n(6840);t.exports=function(){var t=i("Symbol"),e=t&&t.prototype,n=e&&e.valueOf,s=o("toPrimitive");e&&!e[s]&&a(e,s,(function(t){return r(n,this)}),{arity:1})}},8265:function(t){"use strict";var e=function(){this.head=null,this.tail=null};e.prototype={add:function(t){var e={item:t,next:null},n=this.tail;n?n.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},t.exports=e},8319:function(t){"use strict";var e=Math.round;t.exports=function(t){var n=e(t);return n<0?0:n>255?255:255&n}},8345:function(t,e,n){"use strict";var r=n(2805);(0,n(4644).exportTypedArrayStaticMethod)("from",n(3251),r)},8350:function(t,e,n){"use strict";var r=n(6518),i=n(259),o=n(9306),a=n(8981),s=n(6198),u=n(1469);r({target:"Array",proto:!0},{flatMap:function(t){var e,n=a(this),r=s(n);return o(t),(e=u(n,0)).length=i(e,n,n,r,0,1,t,arguments.length>1?arguments[1]:void 0),e}})},8379:function(t,e,n){"use strict";var r=n(8745),i=n(5397),o=n(1291),a=n(6198),s=n(4598),u=Math.min,c=[].lastIndexOf,l=!!c&&1/[1].lastIndexOf(1,-0)<0,f=s("lastIndexOf"),h=l||!f;t.exports=h?function(t){if(l)return r(c,this,arguments)||0;var e=i(this),n=a(e);if(0===n)return-1;var s=n-1;for(arguments.length>1&&(s=u(s,o(arguments[1]))),s<0&&(s=n+s);s>=0;s--)if(s in e&&e[s]===t)return s||0;return-1}:c},8406:function(t,e,n){"use strict";n(3792),n(7337);var r=n(6518),i=n(4576),o=n(3389),a=n(7751),s=n(9565),u=n(9504),c=n(3724),l=n(7416),f=n(6840),h=n(2106),d=n(6279),p=n(687),v=n(3994),g=n(1181),y=n(679),b=n(4901),m=n(9297),x=n(6080),w=n(6955),k=n(8551),A=n(34),S=n(655),E=n(2360),_=n(6980),T=n(81),O=n(851),M=n(2529),L=n(2812),C=n(8227),I=n(4488),R=C("iterator"),j="URLSearchParams",P=j+"Iterator",F=g.set,N=g.getterFor(j),D=g.getterFor(P),z=o("fetch"),B=o("Request"),U=o("Headers"),W=B&&B.prototype,H=U&&U.prototype,q=i.TypeError,V=i.encodeURIComponent,$=String.fromCharCode,G=a("String","fromCodePoint"),X=parseInt,Y=u("".charAt),K=u([].join),J=u([].push),Z=u("".replace),Q=u([].shift),tt=u([].splice),et=u("".split),nt=u("".slice),rt=u(/./.exec),it=/\+/g,ot=/^[0-9a-f]+$/i,at=function(t,e){var n=nt(t,e,e+2);return rt(ot,n)?X(n,16):NaN},st=function(t){for(var e=0,n=128;n>0&&t&n;n>>=1)e++;return e},ut=function(t){var e=null;switch(t.length){case 1:e=t[0];break;case 2:e=(31&t[0])<<6|63&t[1];break;case 3:e=(15&t[0])<<12|(63&t[1])<<6|63&t[2];break;case 4:e=(7&t[0])<<18|(63&t[1])<<12|(63&t[2])<<6|63&t[3]}return e>1114111?null:e},ct=function(t){for(var e=(t=Z(t,it," ")).length,n="",r=0;r<e;){var i=Y(t,r);if("%"===i){if("%"===Y(t,r+1)||r+3>e){n+="%",r++;continue}var o=at(t,r+1);if(o!=o){n+=i,r++;continue}r+=2;var a=st(o);if(0===a)i=$(o);else{if(1===a||a>4){n+="�",r++;continue}for(var s=[o],u=1;u<a&&!(3+ ++r>e||"%"!==Y(t,r));){var c=at(t,r+1);if(c!=c){r+=3;break}if(c>191||c<128)break;J(s,c),r+=2,u++}if(s.length!==a){n+="�";continue}var l=ut(s);null===l?n+="�":i=G(l)}}n+=i,r++}return n},lt=/[!'()~]|%20/g,ft={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},ht=function(t){return ft[t]},dt=function(t){return Z(V(t),lt,ht)},pt=v((function(t,e){F(this,{type:P,target:N(t).entries,index:0,kind:e})}),j,(function(){var t=D(this),e=t.target,n=t.index++;if(!e||n>=e.length)return t.target=null,M(void 0,!0);var r=e[n];switch(t.kind){case"keys":return M(r.key,!1);case"values":return M(r.value,!1)}return M([r.key,r.value],!1)}),!0),vt=function(t){this.entries=[],this.url=null,void 0!==t&&(A(t)?this.parseObject(t):this.parseQuery("string"==typeof t?"?"===Y(t,0)?nt(t,1):t:S(t)))};vt.prototype={type:j,bindURL:function(t){this.url=t,this.update()},parseObject:function(t){var e,n,r,i,o,a,u,c=this.entries,l=O(t);if(l)for(n=(e=T(t,l)).next;!(r=s(n,e)).done;){if(o=(i=T(k(r.value))).next,(a=s(o,i)).done||(u=s(o,i)).done||!s(o,i).done)throw new q("Expected sequence with length 2");J(c,{key:S(a.value),value:S(u.value)})}else for(var f in t)m(t,f)&&J(c,{key:f,value:S(t[f])})},parseQuery:function(t){if(t)for(var e,n,r=this.entries,i=et(t,"&"),o=0;o<i.length;)(e=i[o++]).length&&(n=et(e,"="),J(r,{key:ct(Q(n)),value:ct(K(n,"="))}))},serialize:function(){for(var t,e=this.entries,n=[],r=0;r<e.length;)t=e[r++],J(n,dt(t.key)+"="+dt(t.value));return K(n,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var gt=function(){y(this,yt);var t=F(this,new vt(arguments.length>0?arguments[0]:void 0));c||(this.size=t.entries.length)},yt=gt.prototype;if(d(yt,{append:function(t,e){var n=N(this);L(arguments.length,2),J(n.entries,{key:S(t),value:S(e)}),c||this.length++,n.updateURL()},delete:function(t){for(var e=N(this),n=L(arguments.length,1),r=e.entries,i=S(t),o=n<2?void 0:arguments[1],a=void 0===o?o:S(o),s=0;s<r.length;){var u=r[s];if(u.key!==i||void 0!==a&&u.value!==a)s++;else if(tt(r,s,1),void 0!==a)break}c||(this.size=r.length),e.updateURL()},get:function(t){var e=N(this).entries;L(arguments.length,1);for(var n=S(t),r=0;r<e.length;r++)if(e[r].key===n)return e[r].value;return null},getAll:function(t){var e=N(this).entries;L(arguments.length,1);for(var n=S(t),r=[],i=0;i<e.length;i++)e[i].key===n&&J(r,e[i].value);return r},has:function(t){for(var e=N(this).entries,n=L(arguments.length,1),r=S(t),i=n<2?void 0:arguments[1],o=void 0===i?i:S(i),a=0;a<e.length;){var s=e[a++];if(s.key===r&&(void 0===o||s.value===o))return!0}return!1},set:function(t,e){var n=N(this);L(arguments.length,1);for(var r,i=n.entries,o=!1,a=S(t),s=S(e),u=0;u<i.length;u++)(r=i[u]).key===a&&(o?tt(i,u--,1):(o=!0,r.value=s));o||J(i,{key:a,value:s}),c||(this.size=i.length),n.updateURL()},sort:function(){var t=N(this);I(t.entries,(function(t,e){return t.key>e.key?1:-1})),t.updateURL()},forEach:function(t){for(var e,n=N(this).entries,r=x(t,arguments.length>1?arguments[1]:void 0),i=0;i<n.length;)r((e=n[i++]).value,e.key,this)},keys:function(){return new pt(this,"keys")},values:function(){return new pt(this,"values")},entries:function(){return new pt(this,"entries")}},{enumerable:!0}),f(yt,R,yt.entries,{name:"entries"}),f(yt,"toString",(function(){return N(this).serialize()}),{enumerable:!0}),c&&h(yt,"size",{get:function(){return N(this).entries.length},configurable:!0,enumerable:!0}),p(gt,j),r({global:!0,constructor:!0,forced:!l},{URLSearchParams:gt}),!l&&b(U)){var bt=u(H.has),mt=u(H.set),xt=function(t){if(A(t)){var e,n=t.body;if(w(n)===j)return e=t.headers?new U(t.headers):new U,bt(e,"content-type")||mt(e,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),E(t,{body:_(0,S(n)),headers:_(0,e)})}return t};if(b(z)&&r({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(t){return z(t,arguments.length>1?xt(arguments[1]):{})}}),b(B)){var wt=function(t){return y(this,W),new B(t,arguments.length>1?xt(arguments[1]):{})};W.constructor=wt,wt.prototype=W,r({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:wt})}}t.exports={URLSearchParams:gt,getState:N}},8408:function(t,e,n){"use strict";n(8406)},8429:function(t,e,n){"use strict";var r=n(9039),i=n(4576).RegExp,o=r((function(){var t=i("a","y");return t.lastIndex=2,null!==t.exec("abcd")})),a=o||r((function(){return!i("a","y").sticky})),s=o||r((function(){var t=i("^r","gy");return t.lastIndex=2,null!==t.exec("str")}));t.exports={BROKEN_CARET:s,MISSED_STICKY:a,UNSUPPORTED_Y:o}},8459:function(t,e,n){"use strict";var r=n(6518),i=n(3904);r({global:!0,forced:parseFloat!==i},{parseFloat:i})},8480:function(t,e,n){"use strict";var r=n(1828),i=n(8727).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,i)}},8490:function(t){"use strict";var e=Array,n=Math.abs,r=Math.pow,i=Math.floor,o=Math.log,a=Math.LN2;t.exports={pack:function(t,s,u){var c,l,f,h=e(u),d=8*u-s-1,p=(1<<d)-1,v=p>>1,g=23===s?r(2,-24)-r(2,-77):0,y=t<0||0===t&&1/t<0?1:0,b=0;for((t=n(t))!=t||t===1/0?(l=t!=t?1:0,c=p):(c=i(o(t)/a),t*(f=r(2,-c))<1&&(c--,f*=2),(t+=c+v>=1?g/f:g*r(2,1-v))*f>=2&&(c++,f/=2),c+v>=p?(l=0,c=p):c+v>=1?(l=(t*f-1)*r(2,s),c+=v):(l=t*r(2,v-1)*r(2,s),c=0));s>=8;)h[b++]=255&l,l/=256,s-=8;for(c=c<<s|l,d+=s;d>0;)h[b++]=255&c,c/=256,d-=8;return h[b-1]|=128*y,h},unpack:function(t,e){var n,i=t.length,o=8*i-e-1,a=(1<<o)-1,s=a>>1,u=o-7,c=i-1,l=t[c--],f=127&l;for(l>>=7;u>0;)f=256*f+t[c--],u-=8;for(n=f&(1<<-u)-1,f>>=-u,u+=e;u>0;)n=256*n+t[c--],u-=8;if(0===f)f=1-s;else{if(f===a)return n?NaN:l?-1/0:1/0;n+=r(2,e),f-=s}return(l?-1:1)*n*r(2,f-e)}}},8523:function(t,e,n){"use strict";n(6468)("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),n(6938))},8551:function(t,e,n){"use strict";var r=n(34),i=String,o=TypeError;t.exports=function(t){if(r(t))return t;throw new o(i(t)+" is not an object")}},8553:function(t,e,n){"use strict";var r=n(6518),i=n(9039),o=n(3250),a=Math.abs,s=Math.exp,u=Math.E;r({target:"Math",stat:!0,forced:i((function(){return-2e-17!==Math.sinh(-2e-17)}))},{sinh:function(t){var e=+t;return a(e)<1?(o(e)-o(-e))/2:(s(e-1)-s(-e-1))*(u/2)}})},8614:function(t,e,n){"use strict";n.d(e,{A:function(){return a}});var r=n(3954),i=n(3662);function o(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(o=function(){return!!t})()}function a(t){var e="function"==typeof Map?new Map:void 0;return a=function(t){if(null===t||!function(t){try{return-1!==Function.toString.call(t).indexOf("[native code]")}catch(e){return"function"==typeof t}}(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,n)}function n(){return function(t,e,n){if(o())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,e);var a=new(t.bind.apply(t,r));return n&&(0,i.A)(a,n.prototype),a}(t,arguments,(0,r.A)(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),(0,i.A)(n,t)},a(t)}},8622:function(t,e,n){"use strict";var r=n(4576),i=n(4901),o=r.WeakMap;t.exports=i(o)&&/native code/.test(String(o))},8686:function(t,e,n){"use strict";var r=n(3724),i=n(9039);t.exports=r&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},8690:function(t,e,n){"use strict";var r=n(6518),i=n(3250),o=Math.exp;r({target:"Math",stat:!0},{tanh:function(t){var e=+t,n=i(e),r=i(-e);return n===1/0?1:r===1/0?-1:(n-r)/(o(e)+o(-e))}})},8706:function(t,e,n){"use strict";var r=n(6518),i=n(9039),o=n(4376),a=n(34),s=n(8981),u=n(6198),c=n(6837),l=n(4659),f=n(1469),h=n(597),d=n(8227),p=n(9519),v=d("isConcatSpreadable"),g=p>=51||!i((function(){var t=[];return t[v]=!1,t.concat()[0]!==t})),y=function(t){if(!a(t))return!1;var e=t[v];return void 0!==e?!!e:o(t)};r({target:"Array",proto:!0,arity:1,forced:!g||!h("concat")},{concat:function(t){var e,n,r,i,o,a=s(this),h=f(a,0),d=0;for(e=-1,r=arguments.length;e<r;e++)if(y(o=-1===e?a:arguments[e]))for(i=u(o),c(d+i),n=0;n<i;n++,d++)n in o&&l(h,d,o[n]);else c(d+1),l(h,d++,o);return h.length=d,h}})},8727:function(t){"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8745:function(t,e,n){"use strict";var r=n(616),i=Function.prototype,o=i.apply,a=i.call;t.exports="object"==typeof Reflect&&Reflect.apply||(r?a.bind(o):function(){return a.apply(o,arguments)})},8747:function(t,e,n){"use strict";var r=n(4644),i=r.aTypedArray,o=r.exportTypedArrayMethod,a=Math.floor;o("reverse",(function(){for(var t,e=this,n=i(e).length,r=a(n/2),o=0;o<r;)t=e[o],e[o++]=e[--n],e[n]=t;return e}))},8773:function(t,e){"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,i=r&&!n.call({1:2},1);e.f=i?function(t){var e=r(this,t);return!!e&&e.enumerable}:n},8781:function(t,e,n){"use strict";var r=n(350).PROPER,i=n(6840),o=n(8551),a=n(655),s=n(9039),u=n(1034),c="toString",l=RegExp.prototype,f=l[c],h=s((function(){return"/a/b"!==f.call({source:"a",flags:"b"})})),d=r&&f.name!==c;(h||d)&&i(l,c,(function(){var t=o(this);return"/"+a(t.source)+"/"+a(u(t))}),{unsafe:!0})},8814:function(t,e,n){"use strict";var r=n(9039),i=n(4576).RegExp;t.exports=r((function(){var t=i("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))},8845:function(t,e,n){"use strict";var r=n(4576),i=n(9565),o=n(4644),a=n(6198),s=n(8229),u=n(8981),c=n(9039),l=r.RangeError,f=r.Int8Array,h=f&&f.prototype,d=h&&h.set,p=o.aTypedArray,v=o.exportTypedArrayMethod,g=!c((function(){var t=new Uint8ClampedArray(2);return i(d,t,{length:1,0:3},1),3!==t[1]})),y=g&&o.NATIVE_ARRAY_BUFFER_VIEWS&&c((function(){var t=new f(2);return t.set(1),t.set("2",1),0!==t[0]||2!==t[1]}));v("set",(function(t){p(this);var e=s(arguments.length>1?arguments[1]:void 0,1),n=u(t);if(g)return i(d,this,n,e);var r=this.length,o=a(n),c=0;if(o+e>r)throw new l("Wrong length");for(;c<o;)this[e+c]=n[c++]}),!g||y)},8863:function(t,e,n){"use strict";var r=n(6518),i=n(926).right,o=n(4598),a=n(9519);r({target:"Array",proto:!0,forced:!n(6193)&&a>79&&a<83||!o("reduceRight")},{reduceRight:function(t){return i(this,t,arguments.length,arguments.length>1?arguments[1]:void 0)}})},8866:function(t,e,n){"use strict";var r=n(3802).end,i=n(706);t.exports=i("trimEnd")?function(){return r(this)}:"".trimEnd},8921:function(t,e,n){"use strict";var r=n(6518),i=n(8379);r({target:"Array",proto:!0,forced:i!==[].lastIndexOf},{lastIndexOf:i})},8934:function(t,e,n){"use strict";var r=n(6518),i=n(3487);r({target:"String",proto:!0,name:"trimStart",forced:"".trimLeft!==i},{trimLeft:i})},8940:function(t,e,n){"use strict";var r=n(6518),i=n(2703);r({global:!0,forced:parseInt!==i},{parseInt:i})},8957:function(t,e,n){"use strict";var r=n(4901),i=n(34),o=n(4913),a=n(1625),s=n(8227),u=n(283),c=s("hasInstance"),l=Function.prototype;c in l||o.f(l,c,{value:u((function(t){if(!r(this)||!i(t))return!1;var e=this.prototype;return i(e)?a(e,t):t instanceof this}),c)})},8980:function(t,e,n){"use strict";var r=n(6518),i=n(9213).findIndex,o=n(6469),a="findIndex",s=!0;a in[]&&Array(1)[a]((function(){s=!1})),r({target:"Array",proto:!0,forced:s},{findIndex:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),o(a)},8981:function(t,e,n){"use strict";var r=n(7750),i=Object;t.exports=function(t){return i(r(t))}},8995:function(t,e,n){"use strict";var r=n(4644),i=n(9213).map,o=r.aTypedArray,a=r.getTypedArrayConstructor;(0,r.exportTypedArrayMethod)("map",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0,(function(t,e){return new(a(t))(e)}))}))},9039:function(t){"use strict";t.exports=function(t){try{return!!t()}catch(t){return!0}}},9065:function(t,e,n){"use strict";var r=n(6518),i=n(3724),o=n(8551),a=n(7347);r({target:"Reflect",stat:!0,sham:!i},{getOwnPropertyDescriptor:function(t,e){return a.f(o(t),e)}})},9072:function(t,e,n){"use strict";n.r(e),n.d(e,{search:function(){return c},searchMatcher:function(){return l}});var r=n(4756),i=n.n(r);function o(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return a(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return s=t.done,t},e:function(t){u=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(u)throw o}}}}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var s=function(t){return t.replace(/\s+/g," ")},u=function(t,e){var n=e.startIndex,r=e.startOffset,i=e.endIndex,o=e.endOffset,a=t[n],u=t[i],c=a===u?a.slice(r,o):a.slice(r)+t.slice(a+1,u).join("")+u.slice(0,o),l=s(a.slice(0,r)).trimStart(),f=s(u.slice(o)).trimEnd(),h=l.length<50?"":"…",d=f.length<50?"":"…";return{pre:"".concat(h).concat(l.slice(-50)),match:c,post:"".concat(f.slice(0,50)).concat(d)}},c=function(t,e,n){var r,o=n.granularity,a=void 0===o?"grapheme":o,s=n.sensitivity,c=void 0===s?"base":s;return null!==(r=Intl)&&void 0!==r&&r.Segmenter&&("grapheme"!==a||"variant"!==c&&"accent"!==c)?function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return i().mark((function r(){var o,a,s,c,l,f,h,d,p,v,g,y,b,m,x,w,k,A,S,E,_,T,O,M,L;return i().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:o=n.locales,a=void 0===o?"en":o,s=n.granularity,c=void 0===s?"word":s,l=n.sensitivity,f=void 0===l?"base":l;try{h=new Intl.Segmenter(a,{usage:"search",granularity:c}),d=new Intl.Collator(a,{sensitivity:f})}catch(t){console.warn(t),h=new Intl.Segmenter("en",{usage:"search",granularity:c}),d=new Intl.Collator("en",{sensitivity:f})}p=Array.from(h.segment(e)).length,v=[],g=0,y=h.segment(t[g])[Symbol.iterator]();case 1:if(!(g<t.length)){r.next=9;break}case 2:if(!(v.length<p)){r.next=7;break}if(b=y.next(),m=b.done,x=b.value,!m){r.next=4;break}if(!(++g<t.length)){r.next=3;break}return y=h.segment(t[g])[Symbol.iterator](),r.abrupt("continue",2);case 3:return r.abrupt("continue",9);case 4:if(w=x.index,k=x.segment,/(?:[\0-\xAC\xAE-\u05FF\u0606-\u061B\u061D-\u06DC\u06DE-\u070E\u0710-\u088F\u0892-\u08E1\u08E3-\u180D\u180F-\u200A\u2010-\u2029\u202F-\u205F\u2065\u2070-\uD7FF\uE000-\uFEFE\uFF00-\uFFF8\uFFFC-\uFFFF]|[\uD800-\uD803\uD805-\uD80C\uD80E-\uD82E\uD830-\uD833\uD835-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD804[\uDC00-\uDCBC\uDCBE-\uDCCC\uDCCE-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC40-\uDFFF]|\uD82F[\uDC00-\uDC9F\uDCA4-\uDFFF]|\uD834[\uDC00-\uDD72\uDD7B-\uDFFF]|\uDB40[\uDC00\uDC02-\uDC1F\uDC80-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/.test(k)){r.next=5;break}return r.abrupt("continue",2);case 5:if(!/[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]/.test(k)){r.next=6;break}return/[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]/.test(null===(A=v[v.length-1])||void 0===A?void 0:A.segment)||v.push({strIndex:g,index:w,segment:" "}),r.abrupt("continue",2);case 6:x.strIndex=g,v.push(x),r.next=2;break;case 7:if(S=v.map((function(t){return t.segment})).join(""),0!==d.compare(e,S)){r.next=8;break}return E=g,_=v[v.length-1],T=_.index+_.segment.length,O=v[0].strIndex,M=v[0].index,L={startIndex:O,startOffset:M,endIndex:E,endOffset:T},r.next=8,{range:L,excerpt:u(t,L)};case 8:v.shift(),r.next=1;break;case 9:case"end":return r.stop()}}),r)}))()}(t,e,n):function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return i().mark((function r(){var o,a,s,c,l,f,h,d,p,v,g,y,b,m,x,w,k;return i().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:o=n.locales,a=void 0===o?"en":o,s=n.sensitivity,c="variant"===s,l=t.join(""),f=c?l:l.toLocaleLowerCase(a),h=c?e:e.toLocaleLowerCase(a),d=h.length,p=-1,v=-1,g=0;case 1:if(!((p=f.indexOf(h,p+1))>-1)){r.next=2;break}for(;g<=p;)g+=t[++v].length;for(y=v,b=p-(g-t[v].length),m=p+d;g<=m;)g+=t[++v].length;return x=v,w=m-(g-t[v].length),k={startIndex:y,startOffset:b,endIndex:x,endOffset:w},r.next=2,{range:k,excerpt:u(t,k)};case 2:if(p>-1){r.next=1;break}case 3:case"end":return r.stop()}}),r)}))()}(t,e,n)},l=function(t,e){var n=e.defalutLocale,r=e.matchCase,a=e.matchDiacritics,s=e.matchWholeWords;return i().mark((function e(u,l){var f,h,d,p,v;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:f=t(u,i().mark((function t(e,f){var h,d,p,v,g,y,b,m,x;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:h=o(c(e,l,{locales:u.body.lang||u.documentElement.lang||n||"en",granularity:s?"word":"grapheme",sensitivity:a&&r?"variant":a&&!r?"accent":!a&&r?"case":"base"})),t.prev=1,h.s();case 2:if((d=h.n()).done){t.next=4;break}return p=d.value,v=p.range,g=v.startIndex,y=v.startOffset,b=v.endIndex,m=v.endOffset,p.range=f(g,y,b,m),t.next=3,p;case 3:t.next=2;break;case 4:t.next=6;break;case 5:t.prev=5,x=t.catch(1),h.e(x);case 6:return t.prev=6,h.f(),t.finish(6);case 7:case"end":return t.stop()}}),t,null,[[1,5,6,7]])}))),h=o(f),e.prev=1,h.s();case 2:if((d=h.n()).done){e.next=4;break}return p=d.value,e.next=3,p;case 3:e.next=2;break;case 4:e.next=6;break;case 5:e.prev=5,v=e.catch(1),h.e(v);case 6:return e.prev=6,h.f(),e.finish(6);case 7:case"end":return e.stop()}}),e,null,[[1,5,6,7]])}))}},9085:function(t,e,n){"use strict";var r=n(6518),i=n(4213);r({target:"Object",stat:!0,arity:2,forced:Object.assign!==i},{assign:i})},9142:function(t,e,n){"use strict";var r=n(6518),i=n(4644);r({target:"ArrayBuffer",stat:!0,forced:!i.NATIVE_ARRAY_BUFFER_VIEWS},{isView:i.isView})},9149:function(t,e,n){"use strict";var r=n(6518),i=n(2087),o=Math.abs;r({target:"Number",stat:!0},{isSafeInteger:function(t){return i(t)&&o(t)<=9007199254740991}})},9167:function(t,e,n){"use strict";var r=n(4576);t.exports=r},9202:function(t,e,n){"use strict";n(3313);var r=n(6518),i=n(8866);r({target:"String",proto:!0,name:"trimEnd",forced:"".trimEnd!==i},{trimEnd:i})},9213:function(t,e,n){"use strict";var r=n(6080),i=n(9504),o=n(7055),a=n(8981),s=n(6198),u=n(1469),c=i([].push),l=function(t){var e=1===t,n=2===t,i=3===t,l=4===t,f=6===t,h=7===t,d=5===t||f;return function(p,v,g,y){for(var b,m,x=a(p),w=o(x),k=s(w),A=r(v,g),S=0,E=y||u,_=e?E(p,k):n||h?E(p,0):void 0;k>S;S++)if((d||S in w)&&(m=A(b=w[S],S,x),t))if(e)_[S]=m;else if(m)switch(t){case 3:return!0;case 5:return b;case 6:return S;case 2:c(_,b)}else switch(t){case 4:return!1;case 7:c(_,b)}return f?-1:i||l?l:_}};t.exports={forEach:l(0),map:l(1),filter:l(2),some:l(3),every:l(4),find:l(5),findIndex:l(6),filterReject:l(7)}},9220:function(t,e,n){"use strict";var r=n(6518),i=n(3724),o=n(2551),a=n(8981),s=n(6969),u=n(2787),c=n(7347).f;i&&r({target:"Object",proto:!0,forced:o},{__lookupGetter__:function(t){var e,n=a(this),r=s(t);do{if(e=c(n,r))return e.get}while(n=u(n))}})},9225:function(t,e,n){"use strict";var r,i,o,a,s=n(4576),u=n(8745),c=n(6080),l=n(4901),f=n(9297),h=n(9039),d=n(397),p=n(7680),v=n(4055),g=n(2812),y=n(9544),b=n(6193),m=s.setImmediate,x=s.clearImmediate,w=s.process,k=s.Dispatch,A=s.Function,S=s.MessageChannel,E=s.String,_=0,T={},O="onreadystatechange";h((function(){r=s.location}));var M=function(t){if(f(T,t)){var e=T[t];delete T[t],e()}},L=function(t){return function(){M(t)}},C=function(t){M(t.data)},I=function(t){s.postMessage(E(t),r.protocol+"//"+r.host)};m&&x||(m=function(t){g(arguments.length,1);var e=l(t)?t:A(t),n=p(arguments,1);return T[++_]=function(){u(e,void 0,n)},i(_),_},x=function(t){delete T[t]},b?i=function(t){w.nextTick(L(t))}:k&&k.now?i=function(t){k.now(L(t))}:S&&!y?(a=(o=new S).port2,o.port1.onmessage=C,i=c(a.postMessage,a)):s.addEventListener&&l(s.postMessage)&&!s.importScripts&&r&&"file:"!==r.protocol&&!h(I)?(i=I,s.addEventListener("message",C,!1)):i=O in v("script")?function(t){d.appendChild(v("script"))[O]=function(){d.removeChild(this),M(t)}}:function(t){setTimeout(L(t),0)}),t.exports={set:m,clear:x}},9228:function(t,e,n){"use strict";n(7495);var r=n(9565),i=n(6840),o=n(7323),a=n(9039),s=n(8227),u=n(6699),c=s("species"),l=RegExp.prototype;t.exports=function(t,e,n,f){var h=s(t),d=!a((function(){var e={};return e[h]=function(){return 7},7!==""[t](e)})),p=d&&!a((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[c]=function(){return n},n.flags="",n[h]=/./[h]),n.exec=function(){return e=!0,null},n[h](""),!e}));if(!d||!p||n){var v=/./[h],g=e(h,""[t],(function(t,e,n,i,a){var s=e.exec;return s===o||s===l.exec?d&&!a?{done:!0,value:r(v,e,n,i)}:{done:!0,value:r(t,n,e,i)}:{done:!1}}));i(String.prototype,t,g[0]),i(l,h,g[1])}f&&u(l[h],"sham",!0)}},9296:function(t,e,n){"use strict";var r=n(4055)("span").classList,i=r&&r.constructor&&r.constructor.prototype;t.exports=i===Object.prototype?void 0:i},9297:function(t,e,n){"use strict";var r=n(9504),i=n(8981),o=r({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return o(i(t),e)}},9306:function(t,e,n){"use strict";var r=n(4901),i=n(6823),o=TypeError;t.exports=function(t){if(r(t))return t;throw new o(i(t)+" is not a function")}},9309:function(t,e,n){"use strict";var r=n(6518),i=n(4576),o=n(9225).set,a=n(9472),s=i.setImmediate?a(o,!1):o;r({global:!0,bind:!0,enumerable:!0,forced:i.setImmediate!==s},{setImmediate:s})},9340:function(t){"use strict";var e=Math.log,n=Math.LOG10E;t.exports=Math.log10||function(t){return e(t)*n}},9369:function(t,e,n){"use strict";var r=n(4644),i=n(9504),o=r.aTypedArray,a=r.exportTypedArrayMethod,s=i([].join);a("join",(function(t){return s(o(this),t)}))},9391:function(t,e,n){"use strict";var r=n(6518),i=n(6395),o=n(550),a=n(9039),s=n(7751),u=n(4901),c=n(2293),l=n(3438),f=n(6840),h=o&&o.prototype;if(r({target:"Promise",proto:!0,real:!0,forced:!!o&&a((function(){h.finally.call({then:function(){}},(function(){}))}))},{finally:function(t){var e=c(this,s("Promise")),n=u(t);return this.then(n?function(n){return l(e,t()).then((function(){return n}))}:t,n?function(n){return l(e,t()).then((function(){throw n}))}:t)}}),!i&&u(o)){var d=s("Promise").prototype.finally;h.finally!==d&&f(h,"finally",d,{unsafe:!0})}},9417:function(t,e,n){"use strict";function r(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}n.d(e,{A:function(){return r}})},9423:function(t,e,n){"use strict";var r=n(4644),i=n(9039),o=n(7680),a=r.aTypedArray,s=r.getTypedArrayConstructor;(0,r.exportTypedArrayMethod)("slice",(function(t,e){for(var n=o(a(this),t,e),r=s(this),i=0,u=n.length,c=new r(u);u>i;)c[i]=n[i++];return c}),i((function(){new Int8Array(1).slice()})))},9432:function(t,e,n){"use strict";var r=n(6518),i=n(8981),o=n(1072);r({target:"Object",stat:!0,forced:n(9039)((function(){o(1)}))},{keys:function(t){return o(i(t))}})},9433:function(t,e,n){"use strict";var r=n(4576),i=Object.defineProperty;t.exports=function(t,e){try{i(r,t,{value:e,configurable:!0,writable:!0})}catch(n){r[t]=e}return e}},9449:function(t,e,n){"use strict";var r,i=n(6518),o=n(7476),a=n(7347).f,s=n(8014),u=n(655),c=n(5749),l=n(7750),f=n(1436),h=n(6395),d=o("".slice),p=Math.min,v=f("endsWith");i({target:"String",proto:!0,forced:!(!h&&!v&&(r=a(String.prototype,"endsWith"),r&&!r.writable)||v)},{endsWith:function(t){var e=u(l(this));c(t);var n=arguments.length>1?arguments[1]:void 0,r=e.length,i=void 0===n?r:p(s(n),r),o=u(t);return d(e,i-o.length,i)===o}})},9463:function(t,e,n){"use strict";var r=n(6518),i=n(3724),o=n(4576),a=n(9504),s=n(9297),u=n(4901),c=n(1625),l=n(655),f=n(2106),h=n(7740),d=o.Symbol,p=d&&d.prototype;if(i&&u(d)&&(!("description"in p)||void 0!==d().description)){var v={},g=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:l(arguments[0]),e=c(p,this)?new d(t):void 0===t?d():d(t);return""===t&&(v[e]=!0),e};h(g,d),g.prototype=p,p.constructor=g;var y="Symbol(description detection)"===String(d("description detection")),b=a(p.valueOf),m=a(p.toString),x=/^Symbol\((.*)\)[^)]+$/,w=a("".replace),k=a("".slice);f(p,"description",{configurable:!0,get:function(){var t=b(this);if(s(v,t))return"";var e=m(t),n=y?k(e,7,-1):w(e,x,"$1");return""===n?void 0:n}}),r({global:!0,constructor:!0,forced:!0},{Symbol:g})}},9469:function(t,e,n){"use strict";var r=n(6518),i=Math.hypot,o=Math.abs,a=Math.sqrt;r({target:"Math",stat:!0,arity:2,forced:!!i&&i(1/0,NaN)!==1/0},{hypot:function(t,e){for(var n,r,i=0,s=0,u=arguments.length,c=0;s<u;)c<(n=o(arguments[s++]))?(i=i*(r=c/n)*r+1,c=n):i+=n>0?(r=n/c)*r:n;return c===1/0?1/0:c*a(i)}})},9472:function(t,e,n){"use strict";var r,i=n(4576),o=n(8745),a=n(4901),s=n(4215),u=n(2839),c=n(7680),l=n(2812),f=i.Function,h=/MSIE .\./.test(u)||"BUN"===s&&((r=i.Bun.version.split(".")).length<3||"0"===r[0]&&(r[1]<3||"3"===r[1]&&"0"===r[2]));t.exports=function(t,e){var n=e?2:1;return h?function(r,i){var s=l(arguments.length,1)>n,u=a(r)?r:f(r),h=s?c(arguments,n):[],d=s?function(){o(u,this,h)}:u;return e?t(d,i):t(d)}:t}},9479:function(t,e,n){"use strict";var r=n(3724),i=n(2106),o=n(5213),a=n(7979);r&&!o.correct&&(i(RegExp.prototype,"flags",{configurable:!0,get:a}),o.correct=!0)},9504:function(t,e,n){"use strict";var r=n(616),i=Function.prototype,o=i.call,a=r&&i.bind.bind(o,o);t.exports=r?a:function(t){return function(){return o.apply(t,arguments)}}},9519:function(t,e,n){"use strict";var r,i,o=n(4576),a=n(2839),s=o.process,u=o.Deno,c=s&&s.versions||u&&u.version,l=c&&c.v8;l&&(i=(r=l.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!i&&a&&(!(r=a.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/))&&(i=+r[1]),t.exports=i},9539:function(t,e,n){"use strict";var r=n(9565),i=n(8551),o=n(5966);t.exports=function(t,e,n){var a,s;i(t);try{if(!(a=o(t,"return"))){if("throw"===e)throw n;return n}a=r(a,t)}catch(t){s=!0,a=t}if("throw"===e)throw n;if(s)throw a;return i(a),n}},9544:function(t,e,n){"use strict";var r=n(2839);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(r)},9557:function(t,e,n){"use strict";n.d(e,{u:function(){return d}});var r=n(296),i=n(3029),o=n(2901);function a(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return s(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,u=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){u=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw o}}}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function u(t,e,n){(function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,e),e.set(t,n)}function c(t,e){return t.get(function(t,e,n){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:n;throw new TypeError("Private element is not present on this object")}(t,e))}var l=function(t){return document.createElementNS("http://www.w3.org/2000/svg",t)},f=new WeakMap,h=new WeakMap,d=function(){return(0,o.A)((function t(){(0,i.A)(this,t),u(this,f,l("svg")),u(this,h,new Map),Object.assign(c(f,this).style,{position:"absolute",top:"0",left:"0",width:"100%",height:"100%",pointerEvents:"none"})}),[{key:"element",get:function(){return c(f,this)}},{key:"add",value:function(t,e,n,r){c(h,this).has(t)&&this.remove(t),"function"==typeof e&&(e=e(c(f,this).getRootNode()));var i=e.getClientRects(),o=n(i,r);c(f,this).append(o),c(h,this).set(t,{range:e,draw:n,options:r,element:o,rects:i})}},{key:"remove",value:function(t){c(h,this).has(t)&&(c(f,this).removeChild(c(h,this).get(t).element),c(h,this).delete(t))}},{key:"redraw",value:function(){var t,e=a(c(h,this).values());try{for(e.s();!(t=e.n()).done;){var n=t.value,r=n.range,i=n.draw,o=n.options,s=n.element;c(f,this).removeChild(s);var u=r.getClientRects(),l=i(u,o);c(f,this).append(l),n.element=l,n.rects=u}}catch(t){e.e(t)}finally{e.f()}}},{key:"hitTest",value:function(t){for(var e=t.x,n=t.y,i=Array.from(c(h,this).entries()),o=i.length-1;o>=0;o--){var s,u=(0,r.A)(i[o],2),l=u[0],f=u[1],d=a(f.rects);try{for(d.s();!(s=d.n()).done;){var p=s.value,v=p.left,g=p.top,y=p.right,b=p.bottom;if(g<=n&&v<=e&&b>n&&y>e)return[l,f.range]}}catch(t){d.e(t)}finally{d.f()}}return[]}}],[{key:"underline",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.color,r=void 0===n?"red":n,i=e.width,o=void 0===i?2:i,s=e.writingMode,u=l("g");if(u.setAttribute("fill",r),"vertical-rl"===s||"vertical-lr"===s){var c,f=a(t);try{for(f.s();!(c=f.n()).done;){var h=c.value,d=h.right,p=h.top,v=h.height,g=l("rect");g.setAttribute("x",d-o),g.setAttribute("y",p),g.setAttribute("height",v),g.setAttribute("width",o),u.append(g)}}catch(t){f.e(t)}finally{f.f()}}else{var y,b=a(t);try{for(b.s();!(y=b.n()).done;){var m=y.value,x=m.left,w=m.bottom,k=m.width,A=l("rect");A.setAttribute("x",x),A.setAttribute("y",w-o),A.setAttribute("height",o),A.setAttribute("width",k),u.append(A)}}catch(t){b.e(t)}finally{b.f()}}return u}},{key:"strikethrough",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.color,r=void 0===n?"red":n,i=e.width,o=void 0===i?2:i,s=e.writingMode,u=l("g");if(u.setAttribute("fill",r),"vertical-rl"===s||"vertical-lr"===s){var c,f=a(t);try{for(f.s();!(c=f.n()).done;){var h=c.value,d=h.right,p=h.left,v=h.top,g=h.height,y=l("rect");y.setAttribute("x",(d+p)/2),y.setAttribute("y",v),y.setAttribute("height",g),y.setAttribute("width",o),u.append(y)}}catch(t){f.e(t)}finally{f.f()}}else{var b,m=a(t);try{for(m.s();!(b=m.n()).done;){var x=b.value,w=x.left,k=x.top,A=x.bottom,S=x.width,E=l("rect");E.setAttribute("x",w),E.setAttribute("y",(k+A)/2),E.setAttribute("height",o),E.setAttribute("width",S),u.append(E)}}catch(t){m.e(t)}finally{m.f()}}return u}},{key:"squiggly",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.color,r=void 0===n?"red":n,i=e.width,o=void 0===i?2:i,s=e.writingMode,u=l("g");u.setAttribute("fill","none"),u.setAttribute("stroke",r),u.setAttribute("stroke-width",o);var c=1.5*o;if("vertical-rl"===s||"vertical-lr"===s){var f,h=a(t);try{var d=function(){var t=f.value,e=t.right,n=t.top,r=t.height,i=l("path"),o=Math.round(r/c/1.5),a=r/o,s=Array.from({length:o},(function(t,e){return"l".concat(e%2?-c:c," ").concat(a)})).join("");i.setAttribute("d","M".concat(e," ").concat(n).concat(s)),u.append(i)};for(h.s();!(f=h.n()).done;)d()}catch(t){h.e(t)}finally{h.f()}}else{var p,v=a(t);try{var g=function(){var t=p.value,e=t.left,n=t.bottom,r=t.width,i=l("path"),o=Math.round(r/c/1.5),a=r/o,s=Array.from({length:o},(function(t,e){return"l".concat(a," ").concat(e%2?c:-c)})).join("");i.setAttribute("d","M".concat(e," ").concat(n).concat(s)),u.append(i)};for(v.s();!(p=v.n()).done;)g()}catch(t){v.e(t)}finally{v.f()}}return u}},{key:"highlight",value:function(t){var e=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).color,n=void 0===e?"red":e,r=l("g");r.setAttribute("fill",n),r.style.opacity="var(--overlayer-highlight-opacity, .3)",r.style.mixBlendMode="var(--overlayer-highlight-blend-mode, normal)";var i,o=a(t);try{for(o.s();!(i=o.n()).done;){var s=i.value,u=s.left,c=s.top,f=s.height,h=s.width,d=l("rect");d.setAttribute("x",u),d.setAttribute("y",c),d.setAttribute("height",f),d.setAttribute("width",h),r.append(d)}}catch(t){o.e(t)}finally{o.f()}return r}},{key:"outline",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.color,r=void 0===n?"red":n,i=e.width,o=void 0===i?3:i,s=e.radius,u=void 0===s?3:s,c=l("g");c.setAttribute("fill","none"),c.setAttribute("stroke",r),c.setAttribute("stroke-width",o);var f,h=a(t);try{for(h.s();!(f=h.n()).done;){var d=f.value,p=d.left,v=d.top,g=d.height,y=d.width,b=l("rect");b.setAttribute("x",p),b.setAttribute("y",v),b.setAttribute("height",g),b.setAttribute("width",y),b.setAttribute("rx",u),c.append(b)}}catch(t){h.e(t)}finally{h.f()}return c}},{key:"copyImage",value:function(t){var e=(0,r.A)(t,1)[0],n=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).src,i=l("image"),o=e.left,a=e.top,s=e.height,u=e.width;return i.setAttribute("href",n),i.setAttribute("x",o),i.setAttribute("y",a),i.setAttribute("height",s),i.setAttribute("width",u),i}}])}()},9565:function(t,e,n){"use strict";var r=n(616),i=Function.prototype.call;t.exports=r?i.bind(i):function(){return i.apply(i,arguments)}},9570:function(t,e,n){"use strict";n.d(e,{u:function(){return f}});var r=n(4756),i=n.n(r);function o(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return a(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return s=t.done,t},e:function(t){u=!0,o=t},f:function(){try{s||null==n.return||n.return()}finally{if(u)throw o}}}}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var s=function(t,e){for(var n=[],r=e.currentNode;r;r=e.nextNode()){var i=t.comparePoint(r,0);if(0===i)n.push(r);else if(i>0)break}return n},u=function(t,e){for(var n=[],r=e.nextNode();r;r=e.nextNode())n.push(r);return n},c=NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT|NodeFilter.SHOW_CDATA_SECTION,l=function(t){if(1===t.nodeType){var e=t.tagName.toLowerCase();return"script"===e||"style"===e?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_SKIP}return NodeFilter.FILTER_ACCEPT},f=i().mark((function t(e,n){var r,a,f,h,d,p,v,g,y,b,m;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:f=null!==(r=null!==(a=e.commonAncestorContainer)&&void 0!==a?a:e.body)&&void 0!==r?r:e,h=document.createTreeWalker(f,c,{acceptNode:l}),d=e.commonAncestorContainer?s:u,p=d(e,h),v=p.map((function(t){return t.nodeValue})),g=o(n(v,(function(t,e,n,r){var i=document.createRange();return i.setStart(p[t],e),i.setEnd(p[n],r),i}))),t.prev=1,g.s();case 2:if((y=g.n()).done){t.next=4;break}return b=y.value,t.next=3,b;case 3:t.next=2;break;case 4:t.next=6;break;case 5:t.prev=5,m=t.catch(1),g.e(m);case 6:return t.prev=6,g.f(),t.finish(6);case 7:case"end":return t.stop()}}),t,null,[[1,5,6,7]])}))},9572:function(t,e,n){"use strict";var r=n(9297),i=n(6840),o=n(3640),a=n(8227)("toPrimitive"),s=Date.prototype;r(s,a)||i(s,a,o)},9590:function(t,e,n){"use strict";var r=n(1291),i=RangeError;t.exports=function(t){var e=r(t);if(e<0)throw new i("The argument can't be less than 0");return e}},9617:function(t,e,n){"use strict";var r=n(5397),i=n(5610),o=n(6198),a=function(t){return function(e,n,a){var s=r(e),u=o(s);if(0===u)return!t&&-1;var c,l=i(a,u);if(t&&n!=n){for(;u>l;)if((c=s[l++])!=c)return!0}else for(;u>l;l++)if((t||l in s)&&s[l]===n)return t||l||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},9773:function(t,e,n){"use strict";var r=n(6518),i=n(4495),o=n(9039),a=n(3717),s=n(8981);r({target:"Object",stat:!0,forced:!i||o((function(){a.f(1)}))},{getOwnPropertySymbols:function(t){var e=a.f;return e?e(s(t)):[]}})},9796:function(t,e,n){"use strict";var r=n(6518),i=n(8745),o=n(9306),a=n(8551);r({target:"Reflect",stat:!0,forced:!n(9039)((function(){Reflect.apply((function(){}))}))},{apply:function(t,e,n){return i(o(t),e,a(n))}})},9833:function(t,e,n){"use strict";n(5823)("Float64",(function(t){return function(e,n,r){return t(this,e,n,r)}}))},9848:function(t,e,n){"use strict";n(6368),n(9309)},9948:function(t,e,n){"use strict";var r=n(5370),i=n(4644).getTypedArrayConstructor;t.exports=function(t,e){return r(i(t),e)}},9955:function(t,e,n){"use strict";var r=n(4644),i=n(9213).findIndex,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("findIndex",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))}},o={};function a(t){var e=o[t];if(void 0!==e)return e.exports;var n=o[t]={exports:{}};return i[t].call(n.exports,n,n.exports,a),n.exports}return t="function"==typeof Symbol?Symbol("webpack queues"):"__webpack_queues__",e="function"==typeof Symbol?Symbol("webpack exports"):"__webpack_exports__",n="function"==typeof Symbol?Symbol("webpack error"):"__webpack_error__",r=function(t){t&&t.d<1&&(t.d=1,t.forEach((function(t){t.r--})),t.forEach((function(t){t.r--?t.r++:t()})))},a.a=function(i,o,a){var s;a&&((s=[]).d=-1);var u,c,l,f=new Set,h=i.exports,d=new Promise((function(t,e){l=e,c=t}));d[e]=h,d[t]=function(t){s&&t(s),f.forEach(t),d.catch((function(){}))},i.exports=d,o((function(i){var o;u=function(i){return i.map((function(i){if(null!==i&&"object"==typeof i){if(i[t])return i;if(i.then){var o=[];o.d=0,i.then((function(t){a[e]=t,r(o)}),(function(t){a[n]=t,r(o)}));var a={};return a[t]=function(t){t(o)},a}}var s={};return s[t]=function(){},s[e]=i,s}))}(i);var a=function(){return u.map((function(t){if(t[n])throw t[n];return t[e]}))},c=new Promise((function(e){(o=function(){e(a)}).r=0;var n=function(t){t!==s&&!f.has(t)&&(f.add(t),t&&!t.d&&(o.r++,t.push(o)))};u.map((function(e){e[t](n)}))}));return o.r?c:a()}),(function(t){t?l(d[n]=t):c(h),r(s)})),s&&s.d<0&&(s.d=0)},a.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,{a:e}),e},a.d=function(t,e){for(var n in e)a.o(e,n)&&!a.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},a.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},a(6561)}()}));