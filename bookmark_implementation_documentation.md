# Comprehensive Bookmark Implementation Documentation for ANX-Reader

## Table of Contents
1. [Overview](#overview)
2. [Architecture Overview](#architecture-overview)
3. [File Inventory](#file-inventory)
4. [Data Model](#data-model)
5. [State Management](#state-management)
6. [UI Components](#ui-components)
7. [EPUB Reader Integration](#epub-reader-integration)
8. [Reading Page Integration](#reading-page-integration)
9. [Key Features](#key-features)
10. [Data Flow](#data-flow)
11. [Dependencies](#dependencies)
12. [Implementation Notes](#implementation-notes)

## Overview

The bookmark functionality in ANX-Reader is a sophisticated system that integrates seamlessly with the EPUB reading experience. It provides users with the ability to bookmark specific locations in their books, view and manage bookmarks, and navigate directly to bookmarked content.

## Architecture Overview

The bookmark system follows a clean architecture pattern with clear separation of concerns:

1. **Data Layer**: SQLite database with `tb_notes` table
2. **Model Layer**: `BookmarkModel` with Freezed for immutability
3. **State Management**: Riverpod providers for reactive state management
4. **Service Layer**: Database operations and business logic
5. **UI Layer**: Flutter widgets for bookmark display and interaction
6. **Integration Layer**: JavaScript bridge for EPUB reader integration

## File Inventory

### Core Files

#### Models
- `lib/models/bookmark.dart` - Core bookmark data model
- `lib/models/bookmark.freezed.dart` - Generated Freezed code
- `lib/models/bookmark.g.dart` - Generated JSON serialization

#### Providers (State Management)
- `lib/providers/bookmark.dart` - Riverpod provider for bookmark state
- `lib/providers/bookmark.g.dart` - Generated Riverpod code

#### Widgets (UI Components)
- `lib/widgets/reading_page/widgets/bookmark.dart` - Bookmark list and item widgets
- `lib/widgets/reading_page/toc_widget.dart` - TOC widget with bookmark tab

#### Database
- `lib/dao/database.dart` - Database schema and operations
- `lib/dao/book_note.dart` - Note/bookmark database operations

#### Integration
- `lib/page/book_player/epub_player.dart` - EPUB player with bookmark integration
- `lib/page/reading_page.dart` - Reading page with bookmark UI
- `assets/foliate-js/src/book.js` - JavaScript bookmark handling

## Data Model

### BookmarkModel Structure

```dart
@freezed
abstract class BookmarkModel with _$BookmarkModel {
  const factory BookmarkModel({
    int? id,                    // Database primary key
    required int bookId,        // Foreign key to book
    required String content,    // Text content at bookmark location
    required String cfi,        // EPUB CFI (Canonical Fragment Identifier)
    required String chapter,    // Chapter title
    required double percentage, // Reading progress percentage
    DateTime? createTime,       // Creation timestamp
    required DateTime updateTime, // Last update timestamp
  }) = _BookmarkModel;

  factory BookmarkModel.fromJson(Map<String, dynamic> json) =>
      _$BookmarkModelFromJson(json);
}
```

### Database Schema

The bookmarks are stored in the `tb_notes` table with the following structure:

```sql
CREATE TABLE tb_notes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  book_id INTEGER,
  content TEXT,
  cfi TEXT,
  chapter TEXT,
  type TEXT,        -- 'bookmark' for bookmarks
  color TEXT,       -- Stores percentage as string
  reader_note TEXT, -- Additional notes (empty for bookmarks)
  create_time TEXT,
  update_time TEXT
)
```

### Data Mapping

The `BookmarkModel` maps to database fields as follows:

```dart
extension BookmarkModelExtension on BookmarkModel {
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'book_id': bookId,
      'content': content,
      'cfi': cfi,
      'chapter': chapter,
      'type': 'bookmark',           // Fixed type identifier
      'color': percentage,          // Percentage stored in color field
      'reader_note': '',           // Empty for bookmarks
      'create_time': createTime?.toIso8601String(),
      'update_time': updateTime.toIso8601String(),
    };
  }
}
```

## State Management

### Riverpod Provider Implementation

```dart
@Riverpod(keepAlive: true)
class Bookmark extends _$Bookmark {
  @override
  Future<List<BookmarkModel>> build(int bookId) async {
    final db = await DBHelper().database;
    final List<Map<String, dynamic>> maps = await db.query('tb_notes',
        where: 'type = ? AND book_id = ?', whereArgs: ['bookmark', bookId]);

    return List.generate(maps.length, (i) {
      return BookmarkModel(
        id: maps[i]['id'],
        bookId: maps[i]['book_id'],
        content: maps[i]['content'],
        cfi: maps[i]['cfi'],
        percentage: double.tryParse(maps[i]['color']) ?? 0.0,
        chapter: maps[i]['chapter'],
        createTime: DateTime.parse(maps[i]['create_time']),
        updateTime: DateTime.parse(maps[i]['update_time']),
      );
    });
  }

  void refreshBookmarks() {
    ref.invalidateSelf();
  }
```

### Key Provider Methods

#### Add Bookmark
```dart
Future<BookmarkModel> addBookmark(BookmarkModel bookmark) async {
  final db = await DBHelper().database;

  // Check for duplicate CFI
  final List<Map<String, dynamic>> maps = await db.query('tb_notes',
      where: 'cfi = ? AND book_id = ?', whereArgs: [bookmark.cfi, bookId]);

  if (maps.isEmpty) {
    int id = await db.insert(
      'tb_notes',
      bookmark.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    bookmark = bookmark.copyWith(id: id);
    List<BookmarkModel> newState = [
      ...state.valueOrNull ?? [],
      bookmark,
    ];

    // Sort by percentage for proper ordering
    newState.sort((a, b) => a.percentage.compareTo(b.percentage));
    state = AsyncData(newState);
  }

  return bookmark;
}
```

#### Remove Bookmark
```dart
void removeBookmark({int? id, String? cfi}) {
  assert(id != null || cfi != null, 'Either id or cfi must be provided');
  assert(!(id != null && cfi != null), 'Only one of id or cfi should be provided');

  try {
    if (id == null) {
      final bookmark = state.valueOrNull?.firstWhere((b) => b.cfi == cfi);
      id = bookmark?.id;
    }

    if (cfi == null) {
      final bookmark = state.valueOrNull?.firstWhere((b) => b.id == id);
      cfi = bookmark?.cfi;
    }

    final db = DBHelper().database;
    db.then((database) {
      database.delete(
        'tb_notes',
        where: 'id = ?',
        whereArgs: [id],
      );
    });

    var newState = state.valueOrNull?.where((b) => b.id != id).toList() ?? [];
    state = AsyncData(newState);

    // Remove from EPUB reader
    final key = epubPlayerKey.currentState;
    key?.removeAnnotation(cfi!);
  } catch (e) {
    AnxLog.info('Bookmark already removed: $e');
  }
}
```

## UI Components

### BookmarkWidget - Main List Component

```dart
class BookmarkWidget extends ConsumerStatefulWidget {
  const BookmarkWidget({
    super.key,
    required this.epubPlayerKey,
  });

  final GlobalKey<EpubPlayerState> epubPlayerKey;

  @override
  ConsumerState<BookmarkWidget> createState() => _BookmarkWidgetState();
}

class _BookmarkWidgetState extends ConsumerState<BookmarkWidget> {
  @override
  Widget build(BuildContext context) {
    final bookId = widget.epubPlayerKey.currentState!.book.id;

    final bookmarkList = ref.watch(BookmarkProvider(bookId));
    return bookmarkList.when(
      data: (bookmarks) {
        if (bookmarks.isEmpty) {
          return Center(
            child: Column(
              children: [
                Text(
                  L10n.of(context).no_bookmarks,
                  style: Theme.of(context).textTheme.titleLarge!.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                SizedBox(height: 16.0),
                Text(L10n.of(context).no_bookmarks_tip),
              ],
            ),
          );
        }
        return ListView.builder(
          itemCount: bookmarks.length,
          itemBuilder: (context, index) {
            final bookmark = bookmarks[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: BookmarkItem(
                bookmark: bookmark,
                onTap: (cfi) {
                  widget.epubPlayerKey.currentState?.goToCfi(cfi);
                },
                onDelete: (id) {
                  ref.read(BookmarkProvider(bookId).notifier).removeBookmark(
                        id: id,
                      );
                },
              ),
            );
          },
        );
      },
      error: (error, stackTrace) {
        return errorHandler(error, stack: stackTrace);
      },
      loading: () {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
  }
}
```

### BookmarkItem - Individual Bookmark Component

```dart
class BookmarkItem extends StatelessWidget {
  const BookmarkItem({
    super.key,
    required this.bookmark,
    required this.onTap,
    required this.onDelete,
  });

  final BookmarkModel bookmark;
  final Function(String) onTap;
  final Function(int) onDelete;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(bookmark.cfi),
      child: FilledContainer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              bookmark.content,
              style: TextStyle(
                fontSize: 16,
              ),
            ),
            const Divider(height: 2),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(bookmark.chapter,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        )),
                    Text('${bookmark.percentage.toStringAsFixed(2)}%',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        )),
                  ],
                ),
                DeleteConfirm(
                  delete: () {
                    onDelete(bookmark.id!);
                  },
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
```

### TOC Integration

```dart
class TocWidget extends StatefulWidget {
  const TocWidget({
    super.key,
    required this.epubPlayerKey,
    required this.hideAppBarAndBottomBar,
  });

  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Function hideAppBarAndBottomBar;

  @override
  State<TocWidget> createState() => _TocWidgetState();
}

class _TocWidgetState extends State<TocWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height - 300,
      child: Column(
        children: [
          TabBar(
            controller: _tabController,
            tabs: [
              Tab(text: L10n.of(context).reading_contents),
              Tab(text: L10n.of(context).reading_bookmark),
            ],
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: TabBarView(
                controller: _tabController,
                children: [
                  buildBookToc(),
                  buildBookmarkList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildBookmarkList() {
    return BookmarkWidget(epubPlayerKey: widget.epubPlayerKey);
  }

  BookToc buildBookToc() {
    return BookToc(
        epubPlayerKey: widget.epubPlayerKey,
        hideAppBarAndBottomBar: widget.hideAppBarAndBottomBar);
  }
}
```

## EPUB Reader Integration

### Flutter-JavaScript Bridge

The bookmark functionality integrates with the EPUB reader through a JavaScript bridge:

```dart
controller.addJavaScriptHandler(
  handlerName: 'handleBookmark',
  callback: (args) async {
    Map<String, dynamic> detail = args[0]['detail'];
    bool remove = args[0]['remove'];
    String cfi = detail['cfi'];
    double percentage = detail['percentage'];
    String content = detail['content'];

    if (remove) {
      ref.read(bookmarkProvider(widget.book.id).notifier).removeBookmark(
            cfi: cfi,
          );
      bookmarkCfi = '';
      bookmarkExists = false;
    } else {
      BookmarkModel bookmark = await ref
          .read(BookmarkProvider(widget.book.id).notifier)
          .addBookmark(
            BookmarkModel(
              bookId: widget.book.id,
              cfi: cfi,
              percentage: percentage,
              content: content,
              chapter: chapterTitle,
              updateTime: DateTime.now(),
              createTime: DateTime.now(),
            ),
          );
      bookmarkCfi = cfi;
      bookmarkExists = true;
      addBookmark(bookmark);
    }
    widget.updateParent();
    setState(() {});
  },
);
```

### JavaScript Implementation

```javascript
handleBookmark = (remove) => {
  const cfi = remove ? this.#bookmarkInfo.cfi : this.view.lastLocation?.cfi

  let content = this.view.lastLocation.range.startContainer.data ?? this.view.lastLocation.range.startContainer.innerText
  content = content.trim()
  if (content.length > 200) {
    content = content.slice(0, 200) + '...'
  }
  const percentage = this.view.lastLocation.fraction

  callFlutter('handleBookmark', {
    remove,
    detail: {
      cfi,
      content,
      percentage
    }
  })
}
```

### Bookmark Detection and Visual Feedback

```javascript
#checkCurrentPageBookmark() {
  const spineCode = this.#index
  const list = this.annotations.get(spineCode)
  let found = false
  let bookmark = null
  if (list) {
    for (const bm of list) {
      if (bm.type === 'bookmark') {
        found = this.#checkBookmark(bm) ? true : found
        if (found) {
          bookmark = bm
          this.#showBookmarkIcon(60)
          break
        }
      }
    }
  }

  this.#bookmarkInfo = {
    exists: found,
    cfi: found ? bookmark.value : null,
    id: found ? bookmark.id : null,
  }
  if (!found) {
    this.#hideBookmarkIcon()
  }
}

#checkBookmark(bookmark) {
  const currCfi = this.view.lastLocation?.cfi
  const currStart = collapse(currCfi)
  const currEnd = collapse(currCfi, true)

  const bookmarkCfi = bookmark.value
  const bookmarkStart = collapse(bookmarkCfi)

  if (compare(currStart, bookmarkStart) <= 0 &&
    compare(currEnd, bookmarkStart) > 0) {
    return true
  }
}
```

### Touch Gesture Integration

```javascript
#onTouchEnd = ({ detail: e }) => {
  if (this.#ignoreTouch()) return;

  const mainView = this.view.shadowRoot.children[0]
  if (e.touchState.direction === 'vertical') {
    const deltaY = e.touchState.delta.y;

    if (deltaY < -60) {
      // Pull up gesture
    } else if (deltaY > 60) {
      if (this.#bookMarkExists) {
        this.#hideBookmarkIcon();
        this.handleBookmark(true);  // Remove bookmark
      } else {
        this.#showBookmarkIcon(deltaY);
        this.handleBookmark(false); // Add bookmark
      }
    } else {
      this.#hideBookmarkIcon();
    }

    mainView.style.transition = 'transform 0.3s ease-out';
    mainView.style.transform = 'translateY(0px)';

    setTimeout(() => {
      mainView.style.transition = '';
    }, 300);
  }
}
```

## Reading Page Integration

### Bookmark Button in App Bar

```dart
actions: [
  aiButton,
  IconButton(
      onPressed: () {
        if (bookmarkExists) {
          epubPlayerKey.currentState!.removeAnnotation(
            epubPlayerKey.currentState!.bookmarkCfi,
          );
        } else {
          epubPlayerKey.currentState!.addBookmarkHere();
        }
      },
      icon: bookmarkExists
          ? const Icon(Icons.bookmark)
          : const Icon(Icons.bookmark_border)),
  IconButton(
    icon: const Icon(EvaIcons.more_vertical),
    onPressed: () {
      Navigator.push(
        context,
        CupertinoPageRoute(
          builder: (context) =>
              BookDetail(book: widget.book),
        ),
      );
    },
  ),
],
```

### State Management in Reading Page

```dart
class ReadingPageState extends ConsumerState<ReadingPage>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  // ... other state variables
  bool bookmarkExists = false;

  void updateState() {
    if (mounted) {
      setState(() {
        bookmarkExists = epubPlayerKey.currentState!.bookmarkExists;
      });
    }
  }
}
```

## Key Features

### 1. Automatic Content Extraction
- Extracts up to 200 characters of text content at bookmark location
- Trims whitespace and adds ellipsis for longer content
- Captures chapter title and reading percentage

### 2. CFI-Based Positioning
- Uses EPUB Canonical Fragment Identifier (CFI) for precise positioning
- Enables accurate navigation back to bookmarked locations
- Supports complex EPUB structures and nested content

### 3. Visual Feedback
- Dynamic bookmark icon in app bar (filled/outlined)
- Touch gesture support for bookmark creation/removal
- Smooth animations for bookmark icon appearance

### 4. Duplicate Prevention
- Checks for existing bookmarks at the same CFI location
- Prevents duplicate bookmarks for the same position
- Maintains data integrity

### 5. Sorting and Organization
- Automatically sorts bookmarks by reading percentage
- Displays bookmarks in reading order
- Shows chapter information and progress percentage

### 6. Reactive UI Updates
- Uses Riverpod for reactive state management
- Automatic UI updates when bookmarks change
- Efficient re-rendering with AsyncValue handling

## Data Flow

### Adding a Bookmark

1. **User Action**: User taps bookmark button or uses touch gesture
2. **JavaScript**: `handleBookmark(false)` called in book.js
3. **Bridge**: JavaScript calls Flutter via `callFlutter('handleBookmark', data)`
4. **Flutter Handler**: `handleBookmark` handler in epub_player.dart receives data
5. **Provider**: Calls `bookmarkProvider.addBookmark()` with BookmarkModel
6. **Database**: Inserts bookmark into `tb_notes` table
7. **State Update**: Provider updates state and notifies listeners
8. **UI Update**: BookmarkWidget rebuilds with new bookmark list
9. **Visual Feedback**: Bookmark icon updates in app bar and EPUB reader

### Removing a Bookmark

1. **User Action**: User taps delete button or bookmark button when bookmark exists
2. **Provider**: Calls `bookmarkProvider.removeBookmark()` with ID or CFI
3. **Database**: Deletes bookmark from `tb_notes` table
4. **EPUB Reader**: Calls `removeAnnotation(cfi)` to remove visual indicator
5. **State Update**: Provider updates state and notifies listeners
6. **UI Update**: BookmarkWidget rebuilds without deleted bookmark

### Navigation to Bookmark

1. **User Action**: User taps on bookmark item in list
2. **Callback**: `onTap(bookmark.cfi)` called with CFI
3. **EPUB Player**: `goToCfi(cfi)` method called
4. **JavaScript**: `window.goToCfi = cfi => reader.view.goTo(cfi)` executed
5. **Reader**: Navigates to specified CFI location
6. **State Update**: Reading position updates, bookmark status checked

## Dependencies

### Flutter Packages
- `riverpod_annotation` - State management code generation
- `flutter_riverpod` - Reactive state management
- `freezed_annotation` - Immutable model generation
- `sqflite_common_ffi` - SQLite database operations
- `flutter_inappwebview` - WebView for EPUB rendering

### Generated Files
- `bookmark.g.dart` - Riverpod provider generation
- `bookmark.freezed.dart` - Freezed model generation
- `bookmark.g.dart` - JSON serialization generation

## Implementation Notes

### Database Design
- Bookmarks share the `tb_notes` table with other annotation types
- Type discrimination using `type = 'bookmark'` field
- Percentage stored in `color` field for historical reasons
- CFI stored as string for precise location referencing

### Performance Considerations
- Bookmarks sorted by percentage for efficient display
- Database queries filtered by book_id and type
- Lazy loading with AsyncValue for smooth UI
- Efficient state updates with Riverpod

### Error Handling
- Graceful handling of bookmark removal errors
- Validation of CFI and ID parameters
- Fallback UI states for loading and error conditions
- Logging for debugging bookmark operations

### Complete Source Files

#### lib/models/bookmark.dart
```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'bookmark.freezed.dart';
part 'bookmark.g.dart';

@freezed
abstract class BookmarkModel with _$BookmarkModel {
  const factory BookmarkModel({
    int? id,
    required int bookId,
    required String content,
    required String cfi,
    required String chapter,
    required double percentage,
    DateTime? createTime,
    required DateTime updateTime,
  }) = _BookmarkModel;

  factory BookmarkModel.fromJson(Map<String, dynamic> json) =>
      _$BookmarkModelFromJson(json);
}

extension BookmarkModelExtension on BookmarkModel {
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'book_id': bookId,
      'content': content,
      'cfi': cfi,
      'chapter': chapter,
      'type': 'bookmark',
      'color': percentage,
      'reader_note': '',
      'create_time': createTime?.toIso8601String(),
      'update_time': updateTime.toIso8601String(),
    };
  }
}
```

#### lib/providers/bookmark.dart
```dart
import 'package:anx_reader/dao/database.dart';
import 'package:anx_reader/models/bookmark.dart';
import 'package:anx_reader/page/reading_page.dart';
import 'package:anx_reader/utils/log/common.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

part 'bookmark.g.dart';

@Riverpod(keepAlive: true)
class Bookmark extends _$Bookmark {
  @override
  Future<List<BookmarkModel>> build(int bookId) async {
    final db = await DBHelper().database;
    final List<Map<String, dynamic>> maps = await db.query('tb_notes',
        where: 'type = ? AND book_id = ?', whereArgs: ['bookmark', bookId]);

    return List.generate(maps.length, (i) {
      return BookmarkModel(
        id: maps[i]['id'],
        bookId: maps[i]['book_id'],
        content: maps[i]['content'],
        cfi: maps[i]['cfi'],
        percentage: double.tryParse(maps[i]['color']) ?? 0.0,
        chapter: maps[i]['chapter'],
        createTime: DateTime.parse(maps[i]['create_time']),
        updateTime: DateTime.parse(maps[i]['update_time']),
      );
    });
  }

  void refreshBookmarks() {
    ref.invalidateSelf();
  }

  Future<BookmarkModel> addBookmark(BookmarkModel bookmark) async {
    final db = await DBHelper().database;

    final List<Map<String, dynamic>> maps = await db.query('tb_notes',
        where: 'cfi = ? AND book_id = ?', whereArgs: [bookmark.cfi, bookId]);
    if (maps.isEmpty) {
      int id = await db.insert(
        'tb_notes',
        bookmark.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      bookmark = bookmark.copyWith(id: id);
      List<BookmarkModel> newState = [
        ...state.valueOrNull ?? [],
        bookmark,
      ];

      newState.sort((a, b) => a.percentage.compareTo(b.percentage));
      state = AsyncData(newState);
    }

    return bookmark;
  }

  void removeBookmark({int? id, String? cfi}) {
    assert(id != null || cfi != null, 'Either id or cfi must be provided');
    assert(!(id != null && cfi != null),
        'Only one of id or cfi should be provided');

    try {
      if (id == null) {
        final bookmark = state.valueOrNull?.firstWhere((b) => b.cfi == cfi);
        id = bookmark?.id;
      }

      if (cfi == null) {
        final bookmark = state.valueOrNull?.firstWhere((b) => b.id == id);
        cfi = bookmark?.cfi;
      }

      final db = DBHelper().database;
      db.then((database) {
        database.delete(
          'tb_notes',
          where: 'id = ?',
          whereArgs: [id],
        );
      });

      var newState = state.valueOrNull?.where((b) => b.id != id).toList() ?? [];
      state = AsyncData(newState);
      final key = epubPlayerKey.currentState;
      key?.removeAnnotation(cfi!);
    } catch (e) {
      AnxLog.info('Bookmark already removed: $e');
    }
  }
}
```

#### lib/widgets/reading_page/widgets/bookmark.dart
```dart
import 'package:anx_reader/l10n/generated/L10n.dart';
import 'package:anx_reader/models/bookmark.dart';
import 'package:anx_reader/page/book_player/epub_player.dart';
import 'package:anx_reader/providers/bookmark.dart';
import 'package:anx_reader/utils/error_handler.dart';
import 'package:anx_reader/widgets/container/filled_container.dart';
import 'package:anx_reader/widgets/delete_confirm.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BookmarkWidget extends ConsumerStatefulWidget {
  const BookmarkWidget({
    super.key,
    required this.epubPlayerKey,
  });

  final GlobalKey<EpubPlayerState> epubPlayerKey;

  @override
  ConsumerState<BookmarkWidget> createState() => _BookmarkWidgetState();
}

class _BookmarkWidgetState extends ConsumerState<BookmarkWidget> {
  @override
  Widget build(BuildContext context) {
    final bookId = widget.epubPlayerKey.currentState!.book.id;

    final bookmarkList = ref.watch(BookmarkProvider(bookId));
    return bookmarkList.when(
      data: (bookmarks) {
        if (bookmarks.isEmpty) {
          return Center(
            child: Column(
              children: [
                Text(
                  L10n.of(context).no_bookmarks,
                  style: Theme.of(context).textTheme.titleLarge!.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                SizedBox(height: 16.0),
                Text(L10n.of(context).no_bookmarks_tip),
              ],
            ),
          );
        }
        return ListView.builder(
          itemCount: bookmarks.length,
          itemBuilder: (context, index) {
            final bookmark = bookmarks[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: BookmarkItem(
                bookmark: bookmark,
                onTap: (cfi) {
                  widget.epubPlayerKey.currentState?.goToCfi(cfi);
                },
                onDelete: (id) {
                  ref.read(BookmarkProvider(bookId).notifier).removeBookmark(
                        id: id,
                      );
                },
              ),
            );
          },
        );
      },
      error: (error, stackTrace) {
        return errorHandler(error, stack: stackTrace);
      },
      loading: () {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
  }
}

class BookmarkItem extends StatelessWidget {
  const BookmarkItem({
    super.key,
    required this.bookmark,
    required this.onTap,
    required this.onDelete,
  });

  final BookmarkModel bookmark;
  final Function(String) onTap;
  final Function(int) onDelete;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(bookmark.cfi),
      child: FilledContainer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              bookmark.content,
              style: TextStyle(
                fontSize: 16,
              ),
            ),
            const Divider(height: 2),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(bookmark.chapter,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        )),
                    Text('${bookmark.percentage.toStringAsFixed(2)}%',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        )),
                  ],
                ),
                DeleteConfirm(
                  delete: () {
                    onDelete(bookmark.id!);
                  },
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
```

#### Key JavaScript Functions (assets/foliate-js/src/book.js)

```javascript
// Bookmark handling function
handleBookmark = (remove) => {
  const cfi = remove ? this.#bookmarkInfo.cfi : this.view.lastLocation?.cfi

  let content = this.view.lastLocation.range.startContainer.data ?? this.view.lastLocation.range.startContainer.innerText
  content = content.trim()
  if (content.length > 200) {
    content = content.slice(0, 200) + '...'
  }
  const percentage = this.view.lastLocation.fraction

  callFlutter('handleBookmark', {
    remove,
    detail: {
      cfi,
      content,
      percentage
    }
  })
}

// Check if current page has bookmark
#checkCurrentPageBookmark() {
  const spineCode = this.#index
  const list = this.annotations.get(spineCode)
  let found = false
  let bookmark = null
  if (list) {
    for (const bm of list) {
      if (bm.type === 'bookmark') {
        found = this.#checkBookmark(bm) ? true : found
        if (found) {
          bookmark = bm
          this.#showBookmarkIcon(60)
          break
        }
      }
    }
  }

  this.#bookmarkInfo = {
    exists: found,
    cfi: found ? bookmark.value : null,
    id: found ? bookmark.id : null,
  }
  if (!found) {
    this.#hideBookmarkIcon()
  }
}

// Window functions for Flutter integration
window.addBookmarkHere = () => reader.handleBookmark(false)
window.removeAnnotation = (cfi) => reader.removeAnnotation(cfi)
window.goToCfi = cfi => reader.view.goTo(cfi)
```

### Database Schema (lib/dao/database.dart)

```sql
-- Notes table used for bookmarks and annotations
const createNoteSQL = '''
CREATE TABLE tb_notes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  book_id INTEGER,
  content TEXT,
  cfi TEXT,
  chapter TEXT,
  type TEXT,
  color TEXT,
  reader_note TEXT,
  create_time TEXT,
  update_time TEXT
)
''';
```

## Conclusion

This comprehensive documentation provides all the necessary information to replicate the bookmark functionality in the dass-reader project. The implementation includes:

1. **Complete source code** for all bookmark-related files
2. **Detailed architecture** explanation with clear separation of concerns
3. **Database schema** and data mapping strategies
4. **State management** using Riverpod for reactive updates
5. **UI components** with proper error handling and loading states
6. **JavaScript integration** for EPUB reader communication
7. **Touch gesture support** for intuitive bookmark creation
8. **Performance optimizations** and best practices

The bookmark system is designed to be:
- **Scalable**: Handles large numbers of bookmarks efficiently
- **Reliable**: Includes proper error handling and data validation
- **User-friendly**: Provides intuitive UI and smooth interactions
- **Maintainable**: Uses clean architecture and well-documented code

To implement this in dass-reader, follow the file structure, copy the source code, and ensure all dependencies are properly configured. The modular design allows for easy customization and extension of the bookmark functionality.
